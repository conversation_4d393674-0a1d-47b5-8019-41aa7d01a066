
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.14.36221.1
MinimumVisualStudioVersion = 10.0.40219.1
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "deployments", "deployments", "{1BFD2429-9F75-A5C4-566F-6C7C131A873C}"
	ProjectSection(SolutionItems) = preProject
		.gitlab-ci.yml = .gitlab-ci.yml
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "files", "files", "{9F5E6384-8D91-7B30-BF94-463B36B69FC3}"
	ProjectSection(SolutionItems) = preProject
		Directory.Packages.props = Directory.Packages.props
		GitVersion.yml = GitVersion.yml
		nuget.config = nuget.config
		README.md = README.md
		.editorconfig = .editorconfig
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "src", "src", "{827E0CD3-B72D-47B6-A68D-7590B98EB39B}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "tests", "tests", "{0AB3BF05-4346-4AA6-1389-037BE0695223}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Host", "src\Host\Host.csproj", "{7DA33573-A525-1376-4E0E-735EC1A297BC}"
	ProjectSection(ProjectDependencies) = postProject
		{AAF235C9-2D68-4ECE-9EA0-052D3B7C8F61} = {AAF235C9-2D68-4ECE-9EA0-052D3B7C8F61}
		{D73FC2BA-0C29-4BB0-B5C7-A9FF1355AD6C} = {D73FC2BA-0C29-4BB0-B5C7-A9FF1355AD6C}
		{3EF21380-5FB7-40CF-A083-B203CB21CA1C} = {3EF21380-5FB7-40CF-A083-B203CB21CA1C}
	EndProjectSection
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Domain", "src\Domain\Domain.csproj", "{AAF235C9-2D68-4ECE-9EA0-052D3B7C8F61}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "ToNugets", "ToNugets", "{D5A77B72-3C5C-401C-B829-EFA1C67BD62C}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Authentication.Providers.Ldap", "src\ToNugets\Authentication.Providers.Ldap\Authentication.Providers.Ldap.csproj", "{D73FC2BA-0C29-4BB0-B5C7-A9FF1355AD6C}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "DAL", "src\DAL\DAL.csproj", "{3EF21380-5FB7-40CF-A083-B203CB21CA1C}"
	ProjectSection(ProjectDependencies) = postProject
		{AAF235C9-2D68-4ECE-9EA0-052D3B7C8F61} = {AAF235C9-2D68-4ECE-9EA0-052D3B7C8F61}
	EndProjectSection
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{7DA33573-A525-1376-4E0E-735EC1A297BC}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{7DA33573-A525-1376-4E0E-735EC1A297BC}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{7DA33573-A525-1376-4E0E-735EC1A297BC}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{7DA33573-A525-1376-4E0E-735EC1A297BC}.Release|Any CPU.Build.0 = Release|Any CPU
		{AAF235C9-2D68-4ECE-9EA0-052D3B7C8F61}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{AAF235C9-2D68-4ECE-9EA0-052D3B7C8F61}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{AAF235C9-2D68-4ECE-9EA0-052D3B7C8F61}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{AAF235C9-2D68-4ECE-9EA0-052D3B7C8F61}.Release|Any CPU.Build.0 = Release|Any CPU
		{D73FC2BA-0C29-4BB0-B5C7-A9FF1355AD6C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D73FC2BA-0C29-4BB0-B5C7-A9FF1355AD6C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D73FC2BA-0C29-4BB0-B5C7-A9FF1355AD6C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D73FC2BA-0C29-4BB0-B5C7-A9FF1355AD6C}.Release|Any CPU.Build.0 = Release|Any CPU
		{3EF21380-5FB7-40CF-A083-B203CB21CA1C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{3EF21380-5FB7-40CF-A083-B203CB21CA1C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{3EF21380-5FB7-40CF-A083-B203CB21CA1C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{3EF21380-5FB7-40CF-A083-B203CB21CA1C}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{7DA33573-A525-1376-4E0E-735EC1A297BC} = {827E0CD3-B72D-47B6-A68D-7590B98EB39B}
		{AAF235C9-2D68-4ECE-9EA0-052D3B7C8F61} = {827E0CD3-B72D-47B6-A68D-7590B98EB39B}
		{D5A77B72-3C5C-401C-B829-EFA1C67BD62C} = {827E0CD3-B72D-47B6-A68D-7590B98EB39B}
		{D73FC2BA-0C29-4BB0-B5C7-A9FF1355AD6C} = {D5A77B72-3C5C-401C-B829-EFA1C67BD62C}
		{3EF21380-5FB7-40CF-A083-B203CB21CA1C} = {827E0CD3-B72D-47B6-A68D-7590B98EB39B}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {305E2833-9A54-4E57-A203-6EBAA359000A}
	EndGlobalSection
EndGlobal
