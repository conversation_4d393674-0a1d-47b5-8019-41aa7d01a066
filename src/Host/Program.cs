using System.Runtime.Loader;
using System.Text.Json.Serialization;
using Microsoft.FeatureManagement;
using Platform.AWP.Authentication.Providers.Ldap;
using Platform.OperationInfo.Default;
using Platform.WebApi.WebProxies;
using Platform.WebApps;
using Platform.WebApps.Auth.KnownConfigurations;
using Platform.WebApps.Auth.KnownConfigurations.Configuration;
using Platform.WebApps.Auth.Negotiate;
using Product.AWP.Infrastructure.Converters;
using Product.AWP.Infrastructure.DAL.Extensions;
using Product.AWP.Infrastructure.Extensions;
using Product.Sdk.Core.Extensions;
using Product.Utils.Auth.TicketStore;

namespace Product.AWP.Infrastructure;

internal class Program
{
    private static ILogger<Program>? _logger;

    public static async Task Main(string[] args)
    {
        ThreadPool.GetMinThreads(out var workerThreads, out var iocpThreads);
        ThreadPool.SetMinThreads(500, iocpThreads);

        //	Cannot write DateTime with Kind=Unspecified to PostgreSQL type 'timestamp with time zone', only UTC is supported
        //	дурацкий Npgsql кидается такой ошибкой при сохранении аудита, хотя в схеме аудита НЕТ колонок с тайм-зоной, а все даты - в UTC.
        //	При этом - только когда меняется реквест. При изменении Assignments, например - ошибки нет, аудит пишется и без этого свитча...
        AppContext.SetSwitch("Npgsql.EnableLegacyTimestampBehavior", true);

        // THIS IS THE MAGIC!
        // .NET Core assembly loading is confusing. Things that happen to be in your bin folder don't just suddenly
        // qualify with the assembly loader. If the assembly isn't specifically referenced by your app, you need to
        // tell .NET Core where to get it EVEN IF IT'S IN YOUR BIN FOLDER.
        // https://stackoverflow.com/questions/43918837/net-core-1-1-type-gettype-from-external-assembly-returns-null
        //
        // The documentation says that any .dll in the application base folder should work, but that doesn't seem
        // to be entirely true. You always have to set up additional handlers if you AREN'T referencing the plugin assembly.
        // https://github.com/dotnet/core-setup/blob/master/Documentation/design-docs/corehost.md
        //
        // To verify, try commenting this out and you'll see that the config system can't load the external plugin type.
        var executionFolder = Path.GetDirectoryName(typeof(Program).Assembly.Location);
        // DISCLAIMER: NO PROMISES THIS IS SECURE. You may or may not want this strategy. It's up to
        // you to determine if allowing any assembly in the directory to be loaded is acceptable. This
        // is for demo purposes only.
        AssemblyLoadContext.Default.Resolving += (context, assembly) => context.LoadFromAssemblyPath(Path.Combine(executionFolder, $"{assembly.Name}.dll"));

        var builder = WebApplication.CreateBuilder(args);
        var services = builder.Services;
        var configuration = builder.Configuration;

        services.AddInternalProductSdk(configuration, builder.Logging, builder.Host, args);
        var authConfiguration = AuthConfiguration.ReadFromConfig(configuration);

        services.AddDatabase(configuration);

        services.AddDefaultCors();
        //services.AddControllersWithJsonOptions(new List<JsonConverter> {new ApplicationInitializationConverter(), new ShortcutActionConverter()}).AddOperationInfoForControllersActions();
        services.AddControllers().AddJsonOptions(options =>
        {
            options.JsonSerializerOptions.Converters.Add(new JsonStringEnumConverter());
            options.JsonSerializerOptions.Converters.Add(new ApplicationInitializationConverter());
            options.JsonSerializerOptions.Converters.Add(new ShortcutActionConverter());
            options.JsonSerializerOptions.AllowOutOfOrderMetadataProperties = true;
        });

        services.AddHttpContextAccessor();
        services.AddDefaultOperationInfoManager().AddReadOperationInfoFromHeader();
        services.AddHttpClient(string.Empty).AddTokenProviderAuth().AddOperationInfoInHeader();
        
        if (authConfiguration == null)
        {
            //	обратная совместимость после добавления возможности настройки аутентификации
            var ldapConnectionParameters = LdapConnectionParametersWithAliases.ReadSettingFromConfiguration(configuration);
            if (ldapConnectionParameters?.Length > 0)
            {
                services.AddAuthWithNegotiateWithLdapOnLinux(ldapConnectionParameters.FirstOrDefault(), () => _logger);
            }
        }

        services.AddIdentityAndMembershipProviders(configuration);
        var multiplexer = services.AddHybridCacheWithRedis(configuration);        
        var applicationName = configuration.GetSection(DataProtectionWithRedisStorageConfiguration.DefaultConfigName).Get<DataProtectionWithRedisStorageConfiguration>()?.ApplicationName;
        services.AddDataProtectionWithRedisStorageIfConfigured(multiplexer, applicationName);
        services.AddRedisCacheTicketStore(multiplexer);

        services.AddFeatureManagement();
        if (configuration.GetValue("FeatureManagement:Swagger", false))
            services.AddSwagger(authConfiguration);
        //services.AddExceptionHandler<ProblemExceptionHandler>();

        var app = builder.Build();
        _logger = app.Services.GetRequiredService<ILogger<Program>>();

        app.UseExceptionHandler();

        app.UseConfiguredForwardedHeaders(configuration);

        app.UseProductSdk(app.Configuration);
        RequestLocalizationConfiguration.UseRequestLocalization(app);

        app.UseHttpsRedirection();
        app.UseRouting();
        app.UseDefaultCors();
        app.UseAuthentication();
        app.UseAuthorization();
        app.UseAllElasticApmIfConfigured(app.Configuration);

        var featureManager = app.Services.GetRequiredService<IFeatureManager>();
        if (await featureManager.IsEnabledAsync("Swagger"))
        {
            app.UseSwaggerWithProxyDetermination();
            app.UseSwagger();
            app.UseSwaggerUI(option =>
            {
                option.SwaggerEndpoint("../swagger/v1/swagger.json", "Product AWP Infrastructure API V1");
                option.DisplayRequestDuration();
            });
        }

        var controllersBuilder = app.MapControllers();
        if (app.Environment.IsDevelopment())
        {
            controllersBuilder.AllowAnonymous();
        }

        app.Run();
    }
}
