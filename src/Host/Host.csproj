<Project Sdk="Microsoft.NET.Sdk.Web">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <Nullable>enable</Nullable>
        <ImplicitUsings>enable</ImplicitUsings>
        <AssemblyName>Product.AWP.Infrastructure.Host</AssemblyName>
        <RootNamespace>Product.AWP.Infrastructure</RootNamespace>
        <LangVersion>default</LangVersion>
        <Product>Product</Product>
        <GenerateDocumentationFile>True</GenerateDocumentationFile>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Microsoft.EntityFrameworkCore.Design">
            <PrivateAssets>all</PrivateAssets>
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
        <PackageReference Include="Microsoft.Extensions.Caching.Hybrid"/>
        <PackageReference Include="Microsoft.Extensions.Caching.StackExchangeRedis"/>
        <PackageReference Include="Microsoft.Extensions.Hosting.Systemd"/>
        <PackageReference Include="Microsoft.FeatureManagement"/>
        <PackageReference Include="Microsoft.OpenApi"/>
        <PackageReference Include="Platform.TextJsonConverters"/>
        <PackageReference Include="Platform.WebApi.WebProxies.AuthTokenProviders.Oidc" />
        <PackageReference Include="Platform.WebApps.Auth.KnownConfigurations" />
        <PackageReference Include="Platform.WebApps.Common"/>
        <PackageReference Include="Platform.WebApps.Swagger"/>
        <PackageReference Include="Product.Sdk.Core"/>
        <PackageReference Include="Swashbuckle.AspNetCore"/>
        <PackageReference Include="Swashbuckle.AspNetCore.Annotations"/>
        <PackageReference Include="Microsoft.Extensions.Logging">
            <TreatAsUsed>true</TreatAsUsed>
        </PackageReference>
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\DAL\DAL.csproj"/>
        <ProjectReference Include="..\Domain\Domain.csproj"/>
        <ProjectReference Include="..\ToNugets\Authentication.Providers.Ldap\Authentication.Providers.Ldap.csproj"/>
    </ItemGroup>

</Project>
