namespace Product.AWP.Infrastructure.Configurations;

public sealed class CacheOptions
{
    public int DefaultMaximumKeyLength { get; init; } = 512;
    public int DefaultMaximumPayloadMegabytes { get; init; } = 100;
    public TimeSpan DefaultExpiration { get; init; } = TimeSpan.FromMinutes(30);
    public TimeSpan DefaultLocalCacheExpiration { get; init; } = TimeSpan.FromMinutes(30);
    public TimeSpan ConfigurationCacheDuration { get; init; } = TimeSpan.FromMinutes(30);
    public TimeSpan OperatorsCacheDuration { get; init; } = TimeSpan.FromMinutes(30);
    public TimeSpan OperatorGroupsCacheDuration { get; init; } = TimeSpan.FromMinutes(30);
}
