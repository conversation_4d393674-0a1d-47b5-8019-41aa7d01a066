using System.Security.Claims;

namespace Product.AWP.Infrastructure.Configurations.OperatorAuthentication;

public class OperatorAuthenticationConfiguration
{
    public OperatorAuthenticationConfiguration()
    {
        DataSource = DataSourceType.Database;
        AuthenticationMode = AuthenticationMode.ActiveDirectoryId;
        ADAttributeNameMapping = new NamesAttibutesMapping
        {
            FirstNameAttributeName = "givenName",
            MiddleNameAttributeName = "initials",
            LastNameAttributeName = "sn"
        };
        ClaimsAttributeNameMapping = new NamesAttibutesMapping
        {
            FirstNameAttributeName = ClaimTypes.GivenName,
            MiddleNameAttributeName = "middle_name",
            LastNameAttributeName = ClaimTypes.Surname
        };
        CustomAttributeFillSettings = new[]
        {
            new CustomAttributeFillSetting {ClaimType = ClaimTypes.Email, CustomAttrCode = "EmailAddress", CustomAttrType = CustomAttributeType.String}
        };
    }

    public NamesAttibutesMapping ADAttributeNameMapping { get; set; }

    public NamesAttibutesMapping ClaimsAttributeNameMapping { get; set; }

    public CustomAttributeFillSetting[] CustomAttributeFillSettings { get; set; }

    public AuthenticationMode AuthenticationMode { get; set; }

    public DataSourceType DataSource { get; set; }

    public string[] NewOperatorDefaultGroupNames { get; set; }
}
