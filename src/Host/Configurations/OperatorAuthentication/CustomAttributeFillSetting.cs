using System.Xml.Serialization;

namespace Product.AWP.Infrastructure.Configurations.OperatorAuthentication;

[Serializable]
public class CustomAttributeFillSetting
{
    private static readonly Dictionary<CustomAttributeType, Type> TypeMapping = new()
    {
        {CustomAttributeType.String, typeof(string)},
        {CustomAttributeType.Binary, typeof(byte[])},
        {CustomAttributeType.Bool, typeof(bool)},
        {CustomAttributeType.DateTime, typeof(DateTime)},
        {CustomAttributeType.Decimal, typeof(decimal)},
        {CustomAttributeType.Guid, typeof(Guid)},
        {CustomAttributeType.Long, typeof(long)}
    };

    [XmlAttribute] public string AdAttrName { get; set; }

    [XmlAttribute] public string ClaimType { get; set; }

    [XmlAttribute] public string CustomAttrCode { get; set; }

    [XmlAttribute] public CustomAttributeType CustomAttrType { get; set; }

    public Type GetRealType()
    {
        return TypeMapping[CustomAttrType];
    }
}
