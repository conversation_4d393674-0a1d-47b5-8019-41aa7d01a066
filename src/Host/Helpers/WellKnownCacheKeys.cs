using Platform.AWP.DataContracts.Infrastructure;

namespace Product.AWP.Infrastructure.Helpers;

internal static class WellKnownCacheKeys
{
    public const string UserGroupsList = "awp:user-groups:list";
    public const string UserProfilesList = "awp:user-profiles:list";
    public const string UserRolesList = "awp:user-roles:list";
    public const string ServiceAreasList = "awp:service-areas:list";
    public const string WorkplacesList = "awp:workplaces:list";
    public const string OperatorGroups = "awp:operator-groups:list";
    
    public static string CreateModulesListKey(AwpClientTypes clientTypes, Guid userRoleId, Guid? serviceAreaId) => $"awp:modules:clType-{clientTypes}:role-{userRoleId}:area-{serviceAreaId}:list";
    public static string CreateWorkflowListKey(AwpClientTypes clientTypes, Guid userRoleId) => $"awp:workflow:clType-{clientTypes}:role-{userRoleId}:list";
    public static string CreateConfigurationKey(string configurationName, Guid? serviceAreaId) => $"awp:configuration:{configurationName.ToUpper()}:area-{serviceAreaId}";
    public static string CreateConfigurationsKey(string[] configurationNames, Guid? serviceAreaId) => $"awp:configurations:{string.Join(',', configurationNames.OrderBy(x => x).Select(cfg => cfg.ToUpper()))}:area-{serviceAreaId}:list";
    
    public static string CreateOperatorInfoKey(Guid operatorId) => $"awp:operator:{operatorId}";
    public static string CreateOperatorCustomAttributesKey(Guid operatorId, string[] customAttributesCodes) => $"awp:operator:{operatorId}:ca-{string.Join(',', customAttributesCodes.OrderBy(x => x).Select(ca => ca.ToUpper()))}";
    public static string CreateOperatorsCustomAttributesKey(Guid[] operatorIds, string[] customAttributesCodes) => $"awp:operators:{string.Join(',', operatorIds.OrderBy(x => x))}:ca-{string.Join(',', customAttributesCodes.OrderBy(x => x).Select(ca => ca.ToUpper()))}";
    
    public static string CreateOperatorGroupsInfoKey(Guid operatorId) => $"awp:operator-groups:{operatorId}";
    public static string CreateOperatorGroupsCustomAttributesKey(Guid[] operatorGroupIds, string[] customAttributesCodes) => $"awp:operator-groups:{string.Join(',', operatorGroupIds.OrderBy(x => x))}:ca-{string.Join(',', customAttributesCodes.OrderBy(x => x).Select(ca => ca.ToUpper()))}";
    public static string CreateOperatorGroupCustomAttributesKey(Guid operatorGroupId, string[] customAttributesCodes) => $"awp:operator-groups:{operatorGroupId}:ca-{string.Join(',', customAttributesCodes.OrderBy(x => x).Select(ca => ca.ToUpper()))}";
    
}
