using System.Security.Principal;
using Platform.AWP.DataContracts.Infrastructure;
using Product.AWP.Infrastructure.DAL.Interfaces.Repositories;

namespace Product.AWP.Infrastructure.Helpers;

internal static class FindOperatorHelper
{
    public static async Task<Operator> FindOperatorByIdentity(this IOperatorDataRepository repository, IIdentity identity, Platform.Identities.IIdentityProvider identityProvider)
    {
        identityProvider.SplitLogin(identity.Name, out var identityUsername, out var identityDomain);
        return await repository.FindOperatorByLogin(identityUsername, identityDomain, identityProvider);
    }

    public static async Task<Operator> FindOperatorByIdentity(this IOperatorDataRepository repository, Platform.Identities.Identity identity, Platform.Identities.IIdentityProvider identityProvider)
    {
        identityProvider.SplitLogin(identity.Name, out var identityUsername, out var identityDomain);
        return await repository.FindOperatorByLogin(identityUsername, identityDomain, identityProvider);
    }

    public static async Task<Operator> FindOperatorByLogin(this IOperatorDataRepository repository, string identityUsername, string identityDomain, Platform.Identities.IIdentityProvider identityProvider)
    {
        var operatorsWithContainedLogin = await repository.FindByUserNameContains(identityUsername);
        foreach (var operatorWithContainedLogin in operatorsWithContainedLogin)
        {
            identityProvider.SplitLogin(operatorWithContainedLogin.UserName, out var operatorUsername, out var operatorDomain);

            if (string.Equals(operatorUsername, identityUsername, StringComparison.InvariantCultureIgnoreCase)
                &&
                string.Equals(operatorDomain, identityDomain, StringComparison.InvariantCultureIgnoreCase))
            {
                return operatorWithContainedLogin;
            }
        }

        return null;
    }
}
