using System.Runtime.CompilerServices;
using Microsoft.AspNetCore.Http.Features;
using Microsoft.AspNetCore.SignalR;
using Product.AWP.Infrastructure.DAL.Interfaces.Audit;
using Product.AWP.Infrastructure.Exceptions;

namespace Product.AWP.Infrastructure.Helpers;

public static class AuditHelper
{
    public static void InitAuditOnAuditable(this IAuditable auditable, IAuditor auditor, HttpContext httpContext, [CallerMemberName] string operationName = null)
    {
        if (httpContext.User == null) throw new InfrastructureException("User is null, can not create OperationInfo");
        if (httpContext.User.Identity == null) throw new InfrastructureException("User Identity is null, can not create OperationInfo");
        if (!httpContext.User.Identity.IsAuthenticated) throw new InfrastructureException("User Identity is NOT authenticated, can not create OperationInfo");

        var login = httpContext.User.Identity.Name;
        var dns = httpContext.Connection.RemoteIpAddress?.ToString() ?? "unknown";

        auditable.InitAuditOnAuditable(auditor, login, dns, operationName);
    }

    public static void InitAuditOnAuditable(this IAuditable auditable, IAuditor auditor, HubCallerContext hubContext, [CallerMemberName] string operationName = null)
    {
        if (hubContext.User == null) throw new InfrastructureException("User is null, can not create OperationInfo");
        if (hubContext.User.Identity == null) throw new InfrastructureException("User Identity is null, can not create OperationInfo");
        if (!hubContext.User.Identity.IsAuthenticated) throw new InfrastructureException("User Identity is NOT authenticated, can not create OperationInfo");

        var httpConnectionFeature = hubContext.Features.Get<IHttpConnectionFeature>();
        if (httpConnectionFeature == null) throw new InfrastructureException("HubContext does not contain HttpConnectionFeature - can not obtain caller ip");

        string login;
        try
        {
            login = hubContext.User.Identity.Name;
        }
        catch (Exception exc)
        {
            //	при дисконнекте хаба (по крайней мере, на винде) обращение к Name выдаёт ObjectDisposedException, как иначе это сделать - нет идей
            login = $"Unknown (reason='{exc.Message}')";
        }

        var dns = httpConnectionFeature.RemoteIpAddress?.MapToIPv4()?.ToString();

        auditable.InitAuditOnAuditable(auditor, login, dns, operationName);
    }

    public static void InitAuditOnAuditable(this IAuditable auditable, IAuditor auditor, string login, string ipOrDns, [CallerMemberName] string operationName = null)
    {
        if (auditable == null) throw new ArgumentNullException(nameof(auditable));
        if (auditor == null) throw new ArgumentNullException(nameof(auditor));

        auditable.SetAuditor(auditor);
        auditable.InitAuditData(ipOrDns, login, operationName);
    }
}
