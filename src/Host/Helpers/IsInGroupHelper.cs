using Platform.Identities;
using Platform.Identities.Exceptions;
using Platform.Logging.MicrosoftExtensions;
using ILogger = Microsoft.Extensions.Logging.ILogger;

namespace Product.AWP.Infrastructure.Helpers;

public static class IsInGroupHelper
{
    public static async Task<bool> IsCurrentUserInGroup(HttpContext httpContext, IIdentityProvider identityProvider, IGroupMembershipProvider groupMembershipProvider, string groupName, ILogger logger, object source)
    {
        var currentPrincipal = httpContext.User;
        if (currentPrincipal.Identity == null) throw new InvalidOperationException("Current user has no identity");
        if (!currentPrincipal.Identity.IsAuthenticated) throw new InvalidOperationException("Current user identity not authenticated");

        var currentUserName = currentPrincipal.Identity.Name;
        if (string.Equals(currentUserName, groupName, StringComparison.InvariantCultureIgnoreCase)) return true;

        if (identityProvider == null) throw new ArgumentNullException(nameof(identityProvider));
        identityProvider.SplitLogin(currentUserName, out var curUserLogin, out var curUserDomain);
        identityProvider.SplitLogin(groupName, out var checkingLogin, out var checkingDomain);

        if (string.Equals(curUserLogin, checkingLogin, StringComparison.InvariantCultureIgnoreCase) &&
            string.Equals(curUserDomain, checkingDomain, StringComparison.InvariantCultureIgnoreCase))
        {
            return true;
        }

        //	группы нигде не могут задаваться через собаку, значит, groupName - на самом деле логин пользователя, а значит, если он не равен логину текущего пользователя - результат false
        if (groupName.Contains('@')) return false;


        if (groupMembershipProvider == null) throw new ArgumentNullException(nameof(groupMembershipProvider));
        if (await groupMembershipProvider.IsPrincipalInGroup(httpContext.User, groupName))
        {
            return true;
        }

        using var mlh = source.CreateMethodLogHelper(logger).WithArgs("currentUserName='{currentUserName}', groupName='{groupName}'", currentUserName, groupName);

        var identityId = await identityProvider.GetIdentityIdByLogin(currentUserName);
        mlh.LogMethodDetails("User id: '{identityId}'", identityId);

        try
        {
            var groupId = await identityProvider.GetIdentityIdByLogin(groupName);
            mlh.LogMethodDetails("Group id: '{groupId}'", groupId);

            return await groupMembershipProvider.IsUserInGroup(identityId, groupId);
        }
        catch (IdentityNotFoundException notFoundException)
        {
            mlh.LogMethodWarn(notFoundException, "Failed with IdentityNotFoundException - returning false");
            return false;
        }
        catch (Exception exc)
        {
            mlh.LogMethodFailed(exc);
            throw;
        }
    }
}
