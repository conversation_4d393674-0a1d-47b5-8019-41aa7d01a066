{"Serilog": {"Using": ["Serilog.Sinks.File", "Serilog.Sinks.Console"], "MinimumLevel": {"Default": "Debug", "Override": {"Microsoft": "Information", "System": "Warning"}}, "WriteTo": [{"Name": "Async", "Args": {"configure": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"theme": "Serilog.Sinks.SystemConsole.Themes.AnsiConsoleTheme::Code, Serilog.Sinks.Console", "outputTemplate": "{Timestamp:dd.MM.yyyy HH:mm:ss.fffzzz} [{Level:u3}] ({ThreadId}) {Message} (ActionId:{ActionId},RequestId:{RequestId}){NewLine}{Exception}"}}]}}], "Enrich": ["FromLogContext", "WithMachineName", "WithThreadId"]}}