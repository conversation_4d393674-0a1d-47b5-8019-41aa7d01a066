namespace Product.AWP.Infrastructure.Logic;

internal class AwpUser
{
    /// <summary>
    /// Имя
    /// </summary>
    public string FirstName { get; set; }

    /// <summary>
    /// Отчество
    /// </summary>
    public string MiddleName { get; set; }

    /// <summary>
    /// Фамилия
    /// </summary>
    public string LastName { get; set; }

    /// <summary>
    /// Логин оператор
    /// </summary>
    public string UserName { get; set; }

    /// <summary>
    /// Идентификатор оператора в Active Directory
    /// </summary>
    public Guid ActiveDirectoryId { get; set; }

    /// <summary>
    /// Кастомные аттрибуты оператора.
    /// </summary>
    public IList<Tuple<string, object>> CustomAttributes { get; set; }
}
