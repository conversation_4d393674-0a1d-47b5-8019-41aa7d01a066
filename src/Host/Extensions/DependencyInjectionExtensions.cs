using Microsoft.Extensions.Caching.Hybrid;
using Microsoft.OpenApi.Models;
using Platform.WebApps;
using Platform.WebApps.Auth.KnownConfigurations.Configuration;
using Product.AWP.Infrastructure.Configurations;
using StackExchange.Redis;

namespace Product.AWP.Infrastructure.Extensions;

internal static class DependencyInjectionExtensions
{
    /// <summary>
    ///     Добавить поддержку Swagger
    /// </summary>
    public static void AddSwagger(this IServiceCollection services, AuthConfiguration? authConfiguration = null)
    {
        services.AddSwaggerGen(c =>
        {
            c.EnableAnnotations();

            c.SwaggerDoc("v1", new OpenApiInfo()
            {
                Title = "Product AWP Infrastructure API",
                Version = "v1",
                Description = "AWP Infrastructure API",
            });

            var xmlFile = $"{System.Reflection.Assembly.GetExecutingAssembly().GetName().Name}.xml";
            var xmlPath = Path.Combine(AppContext.BaseDirectory, xmlFile);
            c.IncludeXmlComments(xmlPath, true);

            // Добавляем фильтры для поддержки полиморфизма
            //c.SchemaFilter<TypeDiscriminatorSchemaFilter>();

            c.UseAllOfForInheritance();
            c.UseOneOfForPolymorphism();
            c.EnableAnnotations(true, true);
            c.SupportNonNullableReferenceTypes();

            if (authConfiguration?.JwtBearer != null)
            {
                c.AddJwtBearerSecurity();
            }
        });
    }

    public static IConnectionMultiplexer AddHybridCacheWithRedis(this IServiceCollection services, IConfiguration configuration)
    {
        var multiplexer = ConnectionMultiplexer.Connect(configuration.GetConnectionString("Redis") ?? string.Empty);
        services.AddSingleton<IConnectionMultiplexer>(multiplexer);
        services.AddStackExchangeRedisCache(options => { options.ConnectionMultiplexerFactory = () => Task.FromResult<IConnectionMultiplexer>(multiplexer); });

        var configSection = configuration.GetSection("AWPCacheOptions");
        services.Configure<CacheOptions>(configSection);
        var cacheConfig = configSection.Get<CacheOptions>()!;
        services.AddHybridCache(options =>
        {
            options.MaximumPayloadBytes = cacheConfig.DefaultMaximumPayloadMegabytes * 1024 * 1024;
            options.MaximumKeyLength = cacheConfig.DefaultMaximumKeyLength;
            
            options.DefaultEntryOptions = new HybridCacheEntryOptions
            {
                Expiration = cacheConfig.DefaultExpiration,
                LocalCacheExpiration = cacheConfig.DefaultLocalCacheExpiration,
                Flags = HybridCacheEntryFlags.DisableLocalCache,
                // TODO: Сейчас в HybridCacheEntryOptions нет настройки sliding. Узнать нужен ли он вообще. Затем подумать над обходом (как вариант - написать обёртку с вызовом явного Expire или как-то пробрасывать DistributedCacheEntryOptions
                // Issue уже есть на эту тему. https://github.com/dotnet/extensions/issues/5649 https://github.com/dotnet/aspnetcore/issues/56754
            };
        });
        return multiplexer;
    }
}
