{"Serilog": {"Using": ["Serilog.Sinks.File", "Serilog.Sinks.Console"], "MinimumLevel": {"Default": "Debug", "Override": {"Microsoft": "Information", "System": "Warning"}}, "WriteTo": [{"Name": "Async", "Args": {"configure": [{"Name": "File", "Args": {"path": "{configValue:LogFolders:ServiceLogFolder}/log_.txt", "rollingInterval": "Day", "fileSizeLimitBytes": "10485760", "rollOnFileSizeLimit": true, "outputTemplate": "{Timestamp:dd.MM.yyyy HH:mm:ss.fffzzz} [{Level:u3}] ({ThreadId}) {Message} (ActionId:{ActionId},RequestId:{RequestId}){NewLine}{Exception}"}}, {"Name": "<PERSON><PERSON><PERSON>", "Args": {"theme": "Serilog.Sinks.SystemConsole.Themes.AnsiConsoleTheme::Code, Serilog.Sinks.Console", "outputTemplate": "{Timestamp:dd.MM.yyyy HH:mm:ss.fffzzz} [{Level:u3}] ({ThreadId}) {Message} (ActionId:{ActionId},RequestId:{RequestId}){NewLine}{Exception}"}}]}}], "Enrich": ["FromLogContext", "WithMachineName", "WithThreadId"]}, "LogFolders": {"ServiceLogFolder": "{configValue:LogFolders:WebLogFolder}/awp-infrastructure"}, "AllowedHosts": "*", "ConnectionStrings": {"AWP.Infrastructure": "Server={configValue:Databases:Product:Host}; DataBase={configValue:Databases:Product:DbName}; UserId={configValue:Databases:Product:awp:User??awp}; password={configValue:Databases:Product:awp:Password??configValue:Databases:Product:Password}; ApplicationName={configValue:ApplicationName??Platform.AWP.WebApi.Infrastructure}; Maximum Pool Size=500", "AWP.Audit": "Server={configValue:Databases:Product:Host}; DataBase={configValue:Databases:Product:DbName}; UserId={configValue:Databases:Product:awp:User??awp}; password={configValue:Databases:Product:awp:Password??configValue:Databases:Product:Password}; ApplicationName={configValue:ApplicationName??Platform.AWP.WebApi.Infrastructure}; Maximum Pool Size=500", "Redis": "{configValue:RedisConnection:Server}:{configValue:RedisConnection:Port},password={configValue:RedisConnection:Password},allowAdmin=true,abortConnect=true"}, "FeatureManagement": {"Swagger": true}, "AWPCacheOptions": {"DefaultMaximumKeyLength": 512, "DefaultMaximumPayloadMegabytes": 100, "DefaultExpiration": "00:30:00", "DefaultLocalCacheExpiration": "00:30:00", "ConfigurationCacheDuration": "00:30:00", "OperatorsCacheDuration": "02:00:00", "OperatorGroupsCacheDuration": "00:30:00"}}