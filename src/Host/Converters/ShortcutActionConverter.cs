using System.Text.Json;
using Platform.AWP.DataContracts.Infrastructure;
using Platform.AWP.DataContracts.Infrastructure.ShortcutActions;
using Platform.TextJsonConverters;
using Product.AWP.Infrastructure.Converters.Models;

namespace Product.AWP.Infrastructure.Converters;

public sealed class ShortcutActionConverter : PolymorphicConverter<ShortcutActionType, ShortcutAction>
{
    private readonly Dictionary<ShortcutActionType, Type> _map = new()
    {
        {ShortcutActionType.CallAdapterFunction, typeof(CallAdapterAction)},
        {ShortcutActionType.StartExternalApp, typeof(ExternalAppAction)},
        {ShortcutActionType.OpenWebAppInNewWindow, typeof(WebNewWindowAction)},
        {ShortcutActionType.LaunchWorkflow, typeof(LaunchWorkflowAction)},
        {ShortcutActionType.LaunchAppAndLogin, typeof(LaunchAppAction)},
        {ShortcutActionType.CustomUserShortcut, typeof(CustomUserShortcutAction)}
    };

    protected override string DataPropertyName => "shortcutActionData";

    protected override ShortcutAction ReadFromDescriptor(ref Utf8JsonReader reader, ShortcutActionType typeDescriptor, JsonSerializerOptions options)
    {
        if (!_map.ContainsKey(typeDescriptor))
        {
            throw new ArgumentException($@"Json map not contains correspondence for {typeDescriptor}", nameof(typeDescriptor));
        }

        var type = _map[typeDescriptor];
        return (ShortcutAction) JsonSerializer.Deserialize(ref reader, type, options);
    }

    protected override ShortcutActionType DescriptorFromValue(ShortcutAction value)
    {
        var type = value.GetType();
        if (!_map.ContainsValue(type))
        {
            throw new ArgumentException($@"Json map not contains correspondence for {type}", nameof(value));
        }

        return _map.FirstOrDefault(p => p.Value == type).Key;
    }
}
