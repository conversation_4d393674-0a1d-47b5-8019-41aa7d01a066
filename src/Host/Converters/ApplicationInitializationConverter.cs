using System.Text.Json;
using Platform.AWP.DataContracts.Infrastructure;
using Platform.AWP.DataContracts.Infrastructure.ApplicationInitializations;
using Platform.TextJsonConverters;
using Product.AWP.Infrastructure.Converters.Models;

namespace Product.AWP.Infrastructure.Converters;

public sealed class ApplicationInitializationConverter : PolymorphicConverter<ApplicationInitializationType, ApplicationInitialization>
{
    private readonly Dictionary<ApplicationInitializationType, Type> _map = new()
    {
        {ApplicationInitializationType.Control, typeof(ControlApplicationInitialization)},
        {ApplicationInitializationType.Web, typeof(WebApplicationInitialization)},
        {ApplicationInitializationType.External, typeof(ExternalApplicationInitialization)}
    };

    protected override string DataPropertyName => "appInitData";

    protected override ApplicationInitialization ReadFromDescriptor(ref Utf8JsonReader reader, ApplicationInitializationType typeDescriptor, JsonSerializerOptions options)
    {
        if (!_map.ContainsKey(typeDescriptor))
        {
            throw new ArgumentException($@"Json map not contains correspondence for {typeDescriptor}", nameof(typeDescriptor));
        }

        var type = _map[typeDescriptor];
        return (ApplicationInitialization) JsonSerializer.Deserialize(ref reader, type, options);
    }

    protected override ApplicationInitializationType DescriptorFromValue(ApplicationInitialization value)
    {
        var type = value.GetType();
        if (!_map.ContainsValue(type))
        {
            throw new ArgumentException($@"Json map not contains correspondence for {type}", nameof(value));
        }

        return _map.FirstOrDefault(p => p.Value == type).Key;
    }
}
