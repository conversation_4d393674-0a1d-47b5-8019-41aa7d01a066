using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Caching.Hybrid;
using Microsoft.Extensions.Options;
using Platform.AWP.DataContracts.Infrastructure;
using Platform.Identities;
using Platform.Logging.MicrosoftExtensions;
using Platform.OperationInfo;
using Product.AWP.Infrastructure.Configurations;
using Product.AWP.Infrastructure.Controllers.Base;
using Product.AWP.Infrastructure.DAL.Interfaces.Audit;
using Product.AWP.Infrastructure.DAL.Interfaces.Repositories;
using Product.AWP.Infrastructure.Domain.Interfaces;
using Product.AWP.Infrastructure.Helpers;

namespace Product.AWP.Infrastructure.Controllers.Operators;

public class OperatorGroupsController : InfrastructureApiControllerBase, IOperatorGroupsWebApi
{
    private readonly IOperatorGroupRepository _operatorGroupRepository;
    private readonly HybridCache _cache;
    private readonly CacheOptions _cacheOptions;

    public OperatorGroupsController(IOperatorGroupRepository operatorGroupRepository, IOperationInfoProvider operationInfoProvider, 
        IAuditor auditor, ILoggerFactory loggerFactory, IIdentityProvider identityProvider, IGroupMembershipProvider groupMembershipProvider,
        HybridCache cache, IOptionsMonitor<CacheOptions> cacheOptions)
        : base(operationInfoProvider, auditor, loggerFactory, identityProvider, groupMembershipProvider)
    {
        _operatorGroupRepository = operatorGroupRepository;
        _cache =  cache;
        _cacheOptions = cacheOptions.CurrentValue;
    }

    [HttpGet]
    public async Task<OperatorGroup[]> GetAllGroups()
    {
        using var mlh = this.CreateMethodLogHelper(Logger).WithNoArgs();

        return await mlh.ExecuteWithTryAsync(async () =>
        {
            return await _cache.GetOrCreateAsync(WellKnownCacheKeys.OperatorGroups, 
                async token => await _operatorGroupRepository.GetList(), 
                new HybridCacheEntryOptions { Expiration = _cacheOptions.OperatorGroupsCacheDuration },
                ["awp", "operator-groups"]);
        });
    }

    [HttpPost]
    public async Task AddOperatorToGroup([FromQuery] Guid operatorId, [FromQuery] Guid operatorGroupId)
    {
        using var mlh = this.CreateMethodLogHelper(Logger).WithArgs("operatorId={operatorId}, operatorGroupId={operatorGroupId}", operatorId, operatorGroupId);

        await CreateAuditExecutionHelper().ExecuteWithTryWithAudit(
            mlh,
            () => _operatorGroupRepository,
            async da => await da.AddOperatorToGroups(operatorId, operatorGroupId)
        );
        await _cache.RemoveByTagAsync("operator-groups");
    }

    [HttpPost]
    public async Task RemoveOperatorFromGroup([FromQuery] Guid operatorId, [FromQuery] Guid operatorGroupId)
    {
        using var mlh = this.CreateMethodLogHelper(Logger).WithArgs("operatorId={operatorId}, operatorGroupId={operatorGroupId}", operatorId, operatorGroupId);

        await CreateAuditExecutionHelper().ExecuteWithTryWithAudit(
            mlh,
            () => _operatorGroupRepository,
            async da => await da.RemoveOperatorFromGroup(operatorId, operatorGroupId)
        );
        await _cache.RemoveByTagAsync("operator-groups");
    }

    [HttpGet]
    public async Task<bool> IsOperatorInGroup([FromQuery] Guid operatorId, [FromQuery] Guid operatorGroupId, [FromQuery] bool searchInChilds)
    {
        using var mlh = this.CreateMethodLogHelper(Logger).WithArgs("operatorId={operatorId}, operatorGroupId={operatorGroupId}, searchInChilds={searchInChilds}", operatorId, operatorGroupId, searchInChilds);

        return await mlh.ExecuteWithTryAsync(async () => await _operatorGroupRepository.IsOperatorInGroup(operatorId, operatorGroupId, searchInChilds));
    }

    [HttpPost]
    public async Task<OperatorGroupCustomAttribute[]> GetCustomAttributesForOperatorGroups([FromQuery] Guid[] operatorGroupIds, [FromBody] string[] caCodes)
    {
        using var mlh = this.CreateMethodLogHelper(Logger).WithArgs("operatorGroupsIds={operatorGroupsIds}, caCodes={caCodes}", operatorGroupIds, caCodes);

        return await mlh.ExecuteWithTryAsync(async () =>
        {
            return await _cache.GetOrCreateAsync(WellKnownCacheKeys.CreateOperatorGroupsCustomAttributesKey(operatorGroupIds, caCodes), 
                async token => await _operatorGroupRepository.GetCustomAttributes(operatorGroupIds, caCodes), 
                new HybridCacheEntryOptions { Expiration = _cacheOptions.OperatorGroupsCacheDuration },
                ["awp", "operator-groups"]);
        });
    }

    [HttpPost]
    public async Task<OperatorGroupCustomAttribute[]> GetCustomAttributes([FromQuery] Guid operatorGroupId, [FromBody] string[] caCodes)
    {
        using var mlh = this.CreateMethodLogHelper(Logger).WithArgs("operatorGroupId={operatorGroupId}, caCodes={caCodes}", operatorGroupId, caCodes);

        return await mlh.ExecuteWithTryAsync(async () =>
        {
            return await _cache.GetOrCreateAsync(WellKnownCacheKeys.CreateOperatorGroupCustomAttributesKey(operatorGroupId, caCodes), 
                async token => await _operatorGroupRepository.GetCustomAttributes([operatorGroupId], caCodes), 
                new HybridCacheEntryOptions { Expiration = _cacheOptions.OperatorGroupsCacheDuration },
                ["awp", "operator-groups"]);
        });
    }

    [HttpPost]
    public async Task AddOrUpdateCustomAttributes([FromBody] OperatorGroupCustomAttribute[] attributes)
    {
        using var mlh = this.CreateMethodLogHelper(Logger).WithArgs("attributes={@attributes}", attributes);

        await CreateAuditExecutionHelper().ExecuteWithTryWithAudit(
            mlh,
            () => _operatorGroupRepository,
            async da => await da.AddOrUpdateCustomAttributes(attributes)
        );
        await _cache.RemoveByTagAsync("operator-groups");
    }

    [HttpPost]
    public async Task RemoveCustomAttribute([FromQuery] Guid operatorGroupId, [FromBody] string[] caCodes)
    {
        using var mlh = this.CreateMethodLogHelper(Logger).WithArgs("operatorGroupId={operatorGroupId}, caCodes={caCodes}", operatorGroupId, caCodes);

        await CreateAuditExecutionHelper().ExecuteWithTryWithAudit(
            mlh,
            () => _operatorGroupRepository,
            async da => await da.RemoveCustomAttribute(operatorGroupId, caCodes)
        );
        await _cache.RemoveByTagAsync("operator-groups");
    }

    [HttpGet]
    public async Task<Guid[]> GetOperatorGroupsIdsWithParentGroupsInHierarchy([FromQuery] Guid operatorId)
    {
        using var mlh = this.CreateMethodLogHelper(Logger).WithArgs("operatorId={operatorId}", operatorId);

        return await mlh.ExecuteWithTryAsync(async () =>
        {
            return await _cache.GetOrCreateAsync(WellKnownCacheKeys.CreateOperatorGroupsInfoKey(operatorId), 
                async token => await _operatorGroupRepository.GetOperatorGroupsIdsWithParentGroupsInНierarchy(operatorId), 
                new HybridCacheEntryOptions { Expiration = _cacheOptions.OperatorGroupsCacheDuration },
                ["awp", "operator-groups"]);
        });
    }
}
