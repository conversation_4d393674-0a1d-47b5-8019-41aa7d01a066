using Microsoft.AspNetCore.Mvc;
using Platform.AWP.DataContracts;
using Platform.AWP.DataContracts.Infrastructure;
using Platform.Identities;
using Platform.Logging.MicrosoftExtensions;
using Platform.OperationInfo;
using Product.AWP.Infrastructure.Controllers.Base;
using Product.AWP.Infrastructure.DAL.Interfaces.Audit;
using Product.AWP.Infrastructure.DAL.Interfaces.Repositories;
using Product.AWP.Infrastructure.Domain.Interfaces;

namespace Product.AWP.Infrastructure.Controllers.Operators;

public class ActualOperatorStatusesController : InfrastructureApiControllerBase, IActualOperatorStatusesWebApi
{
    private readonly IActualOperatorStatusRepository _actualOperatorStatusRepository;

    public ActualOperatorStatusesController(IActualOperatorStatusRepository actualOperatorStatusRepository,
        IOperationInfoProvider operationInfoProvider, IAuditor auditor, ILoggerFactory loggerFactory, IIdentityProvider identityProvider, IGroupMembershipProvider groupMembershipProvider)
        : base(operationInfoProvider, auditor, loggerFactory, identityProvider, groupMembershipProvider)
    {
        _actualOperatorStatusRepository = actualOperatorStatusRepository;
    }

    [HttpPost]
    public async Task<OperatorStatusInfo[]> GetActualOperatorsStatuses([FromBody] ActualOperatorStatusFilter actualOperatorStatusFilter)
    {
        using var mlh = this.CreateMethodLogHelper(Logger).WithArgs("actualOperatorStatusFilter={@actualOperatorStatusFilter}", actualOperatorStatusFilter);

        return await mlh.ExecuteWithTryAsync(async () => await _actualOperatorStatusRepository.GetActualOperatorsStatuses(actualOperatorStatusFilter));
    }
}
