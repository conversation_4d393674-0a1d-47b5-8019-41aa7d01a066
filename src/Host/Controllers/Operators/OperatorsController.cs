using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Caching.Hybrid;
using Microsoft.Extensions.Options;
using Platform.AWP.DataContracts.Infrastructure;
using Platform.Identities;
using Platform.Logging.MicrosoftExtensions;
using Platform.OperationInfo;
using Product.AWP.Infrastructure.Configurations;
using Product.AWP.Infrastructure.Controllers.Base;
using Product.AWP.Infrastructure.DAL.Interfaces.Audit;
using Product.AWP.Infrastructure.DAL.Interfaces.Repositories;
using Product.AWP.Infrastructure.Domain.Entities;
using Product.AWP.Infrastructure.Domain.Interfaces;
using Product.AWP.Infrastructure.Helpers;

namespace Product.AWP.Infrastructure.Controllers.Operators;

public class OperatorsController : InfrastructureApiControllerBase, IOperatorsWebApi
{
    private readonly IOperatorDataRepository _operatorDataRepository;
    private readonly HybridCache _cache;
    private readonly CacheOptions _cacheOptions;

    public OperatorsController(IOperatorDataRepository operatorDataRepository, IOperationInfoProvider operationInfoProvider, 
        IAuditor auditor, ILoggerFactory loggerFactory, IIdentityProvider identityProvider, IGroupMembershipProvider groupMembershipProvider,
        HybridCache cache, IOptionsMonitor<CacheOptions> cacheOptions)
        : base(operationInfoProvider, auditor, loggerFactory, identityProvider, groupMembershipProvider)
    {
        _operatorDataRepository = operatorDataRepository;
        _cache = cache;
        _cacheOptions = cacheOptions.CurrentValue;
    }

    [HttpGet]
    public async Task<Operator> GetOperatorInfo([FromQuery] Guid operatorId)
    {
        using var mlh = this.CreateMethodLogHelper(Logger).WithArgs("operatorId={operatorId}", operatorId);

        return await mlh.ExecuteWithTryAsync(async () =>
        {
            return await _cache.GetOrCreateAsync(WellKnownCacheKeys.CreateOperatorInfoKey(operatorId), 
                async token => await _operatorDataRepository.Get(operatorId), 
                new HybridCacheEntryOptions { Expiration = _cacheOptions.OperatorsCacheDuration },
                ["awp", "operators"]);
        });
    }

    [HttpGet]
    public async Task<Operator[]> FindByUserNames([FromQuery] string[] userNames)
    {
        using var mlh = this.CreateMethodLogHelper(Logger).WithArgs("userNames={userNames}", userNames);

        return await mlh.ExecuteWithTryAsync(async () =>
        {
            var foundOperators = new List<Operator>();
            foreach (var userName in userNames)
            {
                IdentityProvider.SplitLogin(userName, out var login, out var domain);
                mlh.LogMethodDetails("Searching operator for username={userName} (login={login}, domain={domain})", userName, login, domain);
                var foundOperator = await _operatorDataRepository.FindOperatorByLogin(login, domain, IdentityProvider);
                if (foundOperator != null)
                {
                    mlh.LogMethodDetails("Operator found for username={userName}", userName);
                    foundOperators.Add(foundOperator);
                    continue;
                }

                mlh.LogMethodDetails("Operator NOT found for username={userName}, searching identity...", userName);

                Platform.Identities.Identity identity;
                try
                {
                    identity = await IdentityProvider.GetIdentityByLogin(userName);
                    mlh.LogMethodDetails("Found identity for userName={userName} [Name={identity_Name}, Id={identity_Id}]", userName, identity.Name, identity.Id);
                }
                catch (Exception exc)
                {
                    mlh.LogMethodError(exc, "Could not get identity by username={userName}", userName);
                    continue;
                }

                mlh.LogMethodDetails("FindByUserNames: searching operator for username={userName} by found identity...", userName);
                foundOperator = await _operatorDataRepository.FindOperatorByIdentity(identity, IdentityProvider);
                if (foundOperator != null)
                {
                    mlh.LogMethodDetails("FindByUserNames: operator found by identity for username={userName}", userName);
                    foundOperators.Add(foundOperator);
                }
                else
                {
                    mlh.LogMethodDetails("FindByUserNames: operator NOT found by identity for username={userName}", userName);
                }
            }

            return foundOperators.ToArray();
        });
    }

    [HttpGet]
    public async Task<Operator[]> FindByActiveDirectoryIds([FromQuery] Guid[] adIds)
    {
        using var mlh = this.CreateMethodLogHelper(Logger).WithArgs("adIds={adIds}", adIds);

        return await mlh.ExecuteWithTryAsync(async () => await _operatorDataRepository.FindByActiveDirectoryIds(adIds));
    }

    [HttpPost]
    public async Task AddOrUpdateCustomAttributes([FromBody] OperatorCustomAttribute[] attributes)
    {
        using var mlh = this.CreateMethodLogHelper(Logger).WithArgs("attributes={@attributes}", attributes);

        await CreateAuditExecutionHelper().ExecuteWithTryWithAudit(
            mlh,
            () => _operatorDataRepository,
            async da => await da.AddOrUpdateCustomAttributes(attributes)
        );
       await _cache.RemoveByTagAsync("operators");
    }

    [HttpPost]
    public async Task RemoveCustomAttribute([FromQuery] Guid operatorId, [FromBody] string[] caCodes)
    {
        using var mlh = this.CreateMethodLogHelper(Logger).WithArgs("operatorId={operatorId}, caCodes={caCodes}", caCodes);

        await CreateAuditExecutionHelper().ExecuteWithTryWithAudit(
            mlh,
            () => _operatorDataRepository,
            async da => await da.RemoveCustomAttribute(operatorId, caCodes)
        );
        await _cache.RemoveByTagAsync("operators");
    }

    [HttpPost]
    public async Task<OperatorCustomAttribute[]> GetCustomAttributes([FromQuery] Guid operatorId, [FromBody] string[] caCodes)
    {
        using var mlh = this.CreateMethodLogHelper(Logger).WithArgs("operatorId={operatorId}, caCodes={caCodes}", caCodes);

        return await mlh.ExecuteWithTryAsync(async () =>
        {
            return await _cache.GetOrCreateAsync(WellKnownCacheKeys.CreateOperatorCustomAttributesKey(operatorId, caCodes), 
                async token => await _operatorDataRepository.GetCustomAttributes([operatorId], caCodes), 
                new HybridCacheEntryOptions { Expiration = _cacheOptions.OperatorsCacheDuration },
                ["awp", "operators"]);
        });
    }

    [HttpPost]
    public async Task<OperatorCustomAttribute[]> GetCustomAttributesForOperators([FromQuery] Guid[] operatorIds, [FromBody] string[] codes)
    {
        using var mlh = this.CreateMethodLogHelper(Logger).WithArgs("operatorIds={operatorIds}, caCodes={caCodes}", operatorIds, codes);

        return await mlh.ExecuteWithTryAsync(async () =>
        {
            return await _cache.GetOrCreateAsync(WellKnownCacheKeys.CreateOperatorsCustomAttributesKey(operatorIds, codes), 
                async token => await _operatorDataRepository.GetCustomAttributes(operatorIds, codes), 
                new HybridCacheEntryOptions { Expiration = _cacheOptions.OperatorsCacheDuration },
                ["awp", "operators"]);
        });
    }

    [HttpGet]
    public async Task<Operator[]> FindByCustomAttributeStringValue([FromQuery] string customAttributeCode, [FromQuery] string customAttributeValue)
    {
        using var mlh = this.CreateMethodLogHelper(Logger).WithArgs("customAttributeCode={customAttributeCode}, customAttributeValue={customAttributeValue}", customAttributeCode, customAttributeValue);

        return await mlh.ExecuteWithTryAsync(async () => await _operatorDataRepository.FindByStringCa(customAttributeCode, customAttributeValue));
    }

    [HttpGet]
    public async Task<Operator[]> FindByCustomAttributeGuidValue([FromQuery] string customAttributeCode, [FromQuery] Guid? customAttributeValue)
    {
        using var mlh = this.CreateMethodLogHelper(Logger).WithArgs("customAttributeCode={customAttributeCode}, customAttributeValue={customAttributeValue}", customAttributeCode, customAttributeValue);

        return await mlh.ExecuteWithTryAsync(async () => await _operatorDataRepository.FindByGuidCa(customAttributeCode, customAttributeValue));
    }

    [HttpGet]
    public async Task<Operator[]> FindByCustomAttributeLongValue([FromQuery] string customAttributeCode, [FromQuery] long? customAttributeValue)
    {
        using var mlh = this.CreateMethodLogHelper(Logger).WithArgs("customAttributeCode={customAttributeCode}, customAttributeValue={customAttributeValue}", customAttributeCode, customAttributeValue);

        return await mlh.ExecuteWithTryAsync(async () => await _operatorDataRepository.FindByLongCa(customAttributeCode, customAttributeValue));
    }

    [HttpGet]
    public async Task<Operator[]> FindByCustomAttributeDecimalValue([FromQuery] string customAttributeCode, [FromQuery] decimal? customAttributeValue)
    {
        using var mlh = this.CreateMethodLogHelper(Logger).WithArgs("customAttributeCode={customAttributeCode}, customAttributeValue={customAttributeValue}", customAttributeCode, customAttributeValue);

        return await mlh.ExecuteWithTryAsync(async () => await _operatorDataRepository.FindByDecimalCa(customAttributeCode, customAttributeValue));
    }

    [HttpGet]
    public async Task<Operator[]> FindByCustomAttributeBoolValue([FromQuery] string customAttributeCode, [FromQuery] bool? customAttributeValue)
    {
        using var mlh = this.CreateMethodLogHelper(Logger).WithArgs("customAttributeCode={customAttributeCode}, customAttributeValue={customAttributeValue}", customAttributeCode, customAttributeValue);

        return await mlh.ExecuteWithTryAsync(async () => await _operatorDataRepository.FindByBoolCa(customAttributeCode, customAttributeValue));
    }

    [HttpGet]
    public async Task<Operator[]> FindByCustomAttributeDateTimeValue([FromQuery] string customAttributeCode, [FromQuery] DateTime? customAttributeValue)
    {
        using var mlh = this.CreateMethodLogHelper(Logger).WithArgs("customAttributeCode={customAttributeCode}, customAttributeValue={customAttributeValue}", customAttributeCode, customAttributeValue);

        return await mlh.ExecuteWithTryAsync(async () => await _operatorDataRepository.FindByDateTimeCa(customAttributeCode, customAttributeValue));
    }

    [HttpPost]
    public async Task RemoveCustomAttributes([FromBody] RemoveOperatorsCustomAttributesData removeOperatorsCustomAttributesData)
    {
        ArgumentNullException.ThrowIfNull(removeOperatorsCustomAttributesData);

        using var mlh = this.CreateMethodLogHelper(Logger).WithArgs("operatorsIds={operatorsIds}, customAttributesCodes={customAttributesCodes}", removeOperatorsCustomAttributesData.OperatorsIds, removeOperatorsCustomAttributesData.CustomAttributesCodes);

        if (removeOperatorsCustomAttributesData.OperatorsIds?.Length == 0)
        {
            return;
        }

        await CreateAuditExecutionHelper().ExecuteWithTryWithAudit(
            mlh,
            () => _operatorDataRepository,
            async da => await da.RemoveCustomAttributes(removeOperatorsCustomAttributesData.OperatorsIds, removeOperatorsCustomAttributesData.CustomAttributesCodes)
        );
        await _cache.RemoveByTagAsync("operators");
    }
}
