using Microsoft.AspNetCore.Mvc;
using Platform.AWP.DataContracts.Infrastructure;
using Platform.Identities;
using Platform.Logging.MicrosoftExtensions;
using Platform.OperationInfo;
using Product.AWP.Infrastructure.Controllers.Base;
using Product.AWP.Infrastructure.DAL.Interfaces.Audit;
using Product.AWP.Infrastructure.DAL.Interfaces.Repositories;
using Product.AWP.Infrastructure.Domain.Entities;
using Product.AWP.Infrastructure.Domain.Interfaces;

namespace Product.AWP.Infrastructure.Controllers;

public class OperatorStatusController : InfrastructureApiControllerBase, IOperatorStatusWebApi
{
    private readonly IOperatorStatusAvailabilityRepository _operatorStatusAvailabilityRepository;
    private readonly IOperatorStatusRepository _operatorStatusRepository;

    public OperatorStatusController(IOperatorStatusAvailabilityRepository operatorStatusAvailabilityRepository, 
        IOperatorStatusRepository operatorStatusRepository, IOperationInfoProvider operationInfoProvider, IAuditor auditor, 
        ILoggerFactory loggerFactory, IIdentityProvider identityProvider, IGroupMembershipProvider groupMembershipProvider)
        : base(operationInfoProvider, auditor, loggerFactory, identityProvider, groupMembershipProvider)
    {
        _operatorStatusAvailabilityRepository = operatorStatusAvailabilityRepository;
        _operatorStatusRepository = operatorStatusRepository;
    }

    [HttpGet]
    public async Task<OperatorStatus[]> GetAvailableStatuses(Guid userRoleId)
    {
        using var mlh = this.CreateMethodLogHelper(Logger).WithArgs("userRoleId={userRoleId}", userRoleId);

        return await mlh.ExecuteWithTryAsync(async () => await _operatorStatusAvailabilityRepository.GetAvailableForUser(userRoleId, IsCurrentUserInGroup));
    }

    [HttpGet]
    public async Task<OperatorStatus[]> GetAvailableFrom(Guid currentStatusId, Guid userRoleId)
    {
        using var mlh = this.CreateMethodLogHelper(Logger).WithArgs("currentStatusId={currentStatusId}, userRoleId={userRoleId}", currentStatusId, userRoleId);

        return await mlh.ExecuteWithTryAsync(async () => await _operatorStatusAvailabilityRepository.GetAvailableFrom(currentStatusId, userRoleId, IsCurrentUserInGroup));
    }

    [HttpPost]
    public async Task<CustomAttribute[]> GetCustomAttributes([FromQuery] Guid statusId, [FromBody] NullableWebApiStringArray attributeNames)
    {
        var attributesNames = attributeNames?.Value;
        using var mlh = this.CreateMethodLogHelper(Logger).WithArgs("statusId={statusId}, attributesNames={attributesNames}", statusId, attributesNames);

        return await mlh.ExecuteWithTryAsync(async () => await _operatorStatusRepository.GetCustomAttributes(statusId, attributeNames?.Value));
    }
}
