using System.Text.Json;
using Microsoft.AspNetCore.Mvc;
using Platform.AWP.DataContracts.Infrastructure;
using Platform.Identities;
using Platform.Logging.MicrosoftExtensions;
using Platform.OperationInfo;
using Product.AWP.Infrastructure.Configurations.OperatorAuthentication;
using Product.AWP.Infrastructure.Controllers.Base;
using Product.AWP.Infrastructure.DAL.Interfaces.Audit;
using Product.AWP.Infrastructure.DAL.Interfaces.Repositories;
using Product.AWP.Infrastructure.Domain.Interfaces;
using Product.AWP.Infrastructure.Logic;

namespace Product.AWP.Infrastructure.Controllers;

public class OperatorAuthenticationController : InfrastructureApiControllerBase, IOperatorAuthenticationWebApi
{
    private static OperatorAuthenticationConfiguration _configuration;
    private static SemaphoreSlim _configurationSemaphore = new(1);
    private readonly IConfigurationRepository _configurationRepository;
    private readonly IOperatorDataRepository _operatorDataRepository;
    private readonly IOperatorGroupRepository _operatorGroupRepository;

    public OperatorAuthenticationController(
        IOperatorDataRepository operatorDataRepository, IOperatorGroupRepository operatorGroupRepository, 
        IConfigurationRepository configurationRepository, IOperationInfoProvider operationInfoProvider, IAuditor auditor, 
        ILoggerFactory loggerFactory, IIdentityProvider identityProvider, IGroupMembershipProvider groupMembershipProvider)
        : base(operationInfoProvider, auditor, loggerFactory, identityProvider, groupMembershipProvider)
    {
        _operatorDataRepository = operatorDataRepository;
        _operatorGroupRepository = operatorGroupRepository;
        _configurationRepository = configurationRepository;
    }

    [HttpPost]
    public async Task<Operator> AuthenticateOperator()
    {
        using var mlh = this.CreateMethodLogHelper(Logger).WithNoArgs();

        return await CreateAuditExecutionHelper().ExecuteWithTryWithAudit(
            mlh,
            () => _operatorDataRepository,
            async operatorDataAccessor =>
            {
                var operatorAuthenticationManager = await GetOperatorAuthenticationManager();
                return await operatorAuthenticationManager.GetAuthenticatedOperator(HttpContext.User.Identity, operatorDataAccessor, _operatorGroupRepository);
            }
        );
    }

    private async Task<OperatorAuthenticationManager> GetOperatorAuthenticationManager()
    {
        var identityProvider = HttpContext.RequestServices.GetService<IIdentityProvider>();
        var systemConfig = await LoadConfiguration(Logger, LoggerFactory, this);
        return new OperatorAuthenticationManager(LoggerFactory, identityProvider, systemConfig);
    }

    private async Task<OperatorAuthenticationConfiguration> LoadConfiguration(ILogger logger, ILoggerFactory loggerFactory, object source)
    {
        if (_configuration != null) return _configuration;

        if (_configurationSemaphore != null)
        {
            await _configurationSemaphore.WaitAsync();
            if (_configuration != null) return _configuration;
        }

        Exception err = null;
        try
        {
            var configJson = await _configurationRepository.GetConfiguration("_SystemConfiguration_OperatorAuthentication", null);
            if (configJson == null)
            {
                logger.InfoDev(source, "GetConfiguration: Configuration not specified, using default...");
                return _configuration = new OperatorAuthenticationConfiguration();
            }

            if (string.IsNullOrWhiteSpace(configJson))
            {
                logger.InfoDev(source, "GetConfiguration: Configuration is null or whitespace, using default...");
                return _configuration = new OperatorAuthenticationConfiguration();
            }

            return _configuration = JsonSerializer.Deserialize<OperatorAuthenticationConfiguration>(configJson, new JsonSerializerOptions {PropertyNameCaseInsensitive = true});
        }
        catch (Exception exc)
        {
            err = exc;
            throw;
        }
        finally
        {
            if (_configurationSemaphore != null)
            {
                if (err != null)
                {
                    _configurationSemaphore.Release();
                }
                else
                {
                    _configurationSemaphore.Release(int.MaxValue);
                    var toDispose = _configurationSemaphore;
                    _configurationSemaphore = null;
                    toDispose.Dispose();
                }
            }
        }
    }
}
