using Microsoft.AspNetCore.Mvc;
using Platform.AWP.DataContracts.Infrastructure;
using Platform.Identities;
using Platform.Logging.MicrosoftExtensions;
using Platform.OperationInfo;
using Product.AWP.Infrastructure.Controllers.Base;
using Product.AWP.Infrastructure.DAL.Interfaces.Audit;
using Product.AWP.Infrastructure.DAL.Interfaces.Repositories;
using Product.AWP.Infrastructure.Domain.Interfaces;

namespace Product.AWP.Infrastructure.Controllers;

public class PermissionsController : InfrastructureApiControllerBase, IPermissionsWebApi
{
    private readonly IPermissionRepository _permissionRepository;

    public PermissionsController(IPermissionRepository permissionRepository, IOperationInfoProvider operationInfoProvider, IAuditor auditor, ILoggerFactory loggerFactory, IIdentityProvider identityProvider, IGroupMembershipProvider groupMembershipProvider)
        : base(operationInfoProvider, auditor, loggerFactory, identityProvider, groupMembershipProvider)
    {
        _permissionRepository = permissionRepository;
    }

    /// <summary>
    /// Gets list of permissions for current user
    /// </summary>
    [HttpGet]
    public async Task<Permission[]> GetPermissionsForCurrentUser([FromQuery] Guid userRoleId)
    {
        using var mlh = this.CreateMethodLogHelper(Logger).WithArgs("userRoleId={userRoleId}", userRoleId);
        return await mlh.ExecuteWithTryAsync(async () => await _permissionRepository.GetPermissionsForCurrentUser(userRoleId));
    }

    /// <summary>
    /// Checks if current user has permission.
    /// </summary>
    /// <param name="permission">
    /// The permission.
    /// </param>
    /// <param name="userRoleId"></param>
    [HttpGet]
    public async Task<bool> CheckPermission([FromQuery] string permission, [FromQuery] Guid userRoleId)
    {
        using var mlh = this.CreateMethodLogHelper(Logger).WithArgs("permission={permission}, userRoleId={userRoleId}", permission, userRoleId);
        return await mlh.ExecuteWithTryAsync(async () => await _permissionRepository.RequestPermission(permission, userRoleId));
    }
}
