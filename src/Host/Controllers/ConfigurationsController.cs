using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Caching.Hybrid;
using Microsoft.Extensions.Options;
using Platform.Identities;
using Platform.Logging.MicrosoftExtensions;
using Platform.OperationInfo;
using Product.AWP.Infrastructure.Configurations;
using Product.AWP.Infrastructure.Controllers.Base;
using Product.AWP.Infrastructure.DAL.Interfaces.Audit;
using Product.AWP.Infrastructure.DAL.Interfaces.Repositories;
using Product.AWP.Infrastructure.Domain.Entities;
using Product.AWP.Infrastructure.Domain.Interfaces;
using Product.AWP.Infrastructure.Helpers;

namespace Product.AWP.Infrastructure.Controllers;

public class ConfigurationsController : InfrastructureApiControllerBase, IConfigurationsWebApi
{
    private readonly IConfigurationRepository _configurationRepository;
    private readonly HybridCache _cache;
    private readonly CacheOptions _cacheOptions;

    public ConfigurationsController(IConfigurationRepository configurationRepository, IOperationInfoProvider operationInfoProvider, 
        IAuditor auditor, ILoggerFactory logger, IIdentityProvider identityProvider, IGroupMembershipProvider groupMembershipProvider, 
        HybridCache cache, IOptionsMonitor<CacheOptions> cacheOptions)
        : base(operationInfoProvider, auditor, logger, identityProvider, groupMembershipProvider)
    {
        _configurationRepository = configurationRepository;
        _cache = cache;
        _cacheOptions = cacheOptions.CurrentValue;
    }

    [HttpGet]
    public async Task<NullableWebApiString> GetConfiguration([FromQuery] string configurationName, [FromQuery] Guid? serviceAreaId)
    {
        using var mlh = this.CreateMethodLogHelper(Logger).WithArgs("configurationName={configurationName}, serviceAreaId={serviceAreaId}", configurationName, serviceAreaId);

        var result = await mlh.ExecuteWithTryAsync(async () =>
        {
            return await _cache.GetOrCreateAsync(WellKnownCacheKeys.CreateConfigurationKey(configurationName, serviceAreaId), 
                async token => await _configurationRepository.GetConfiguration(configurationName, serviceAreaId), 
                new HybridCacheEntryOptions { Expiration = _cacheOptions.ConfigurationCacheDuration },
                ["awp", "configuration"]);
        });
        return new NullableWebApiString {StringValue = result};
    }

    [HttpGet]
    public async Task<Dictionary<string, string>> GetConfigurations([FromQuery] string[] configurationNames, [FromQuery] Guid? serviceAreaId)
    {
        using var mlh = this.CreateMethodLogHelper(Logger).WithArgs("configurationNames={configurationNames}, serviceAreaId={serviceAreaId}", configurationNames, serviceAreaId);

        return await mlh.ExecuteWithTryAsync(async () =>
        {
            return await _cache.GetOrCreateAsync(WellKnownCacheKeys.CreateConfigurationsKey(configurationNames, serviceAreaId), 
                async token => await _configurationRepository.GetConfigurations(configurationNames, serviceAreaId), 
                new HybridCacheEntryOptions { Expiration = _cacheOptions.ConfigurationCacheDuration },
                ["awp", "configuration"]);
        });
    }
}
