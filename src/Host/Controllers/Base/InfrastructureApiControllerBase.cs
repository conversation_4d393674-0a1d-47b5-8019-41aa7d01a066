using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Platform.Identities;
using Platform.Logging.MicrosoftExtensions;
using Platform.OperationInfo;
using Product.AWP.Infrastructure.DAL.Interfaces.Audit;
using Product.AWP.Infrastructure.Helpers;

namespace Product.AWP.Infrastructure.Controllers.Base;

[Authorize]
[ApiController]
[Route("[controller]/[action]")]
public abstract class InfrastructureApiControllerBase : ControllerBase, IHasAdditionalLogInfo
{
    private static readonly object InitLockObj = new();
    private static volatile List<Type> _initializedServices;
    private readonly AdditionalLogInfo _additionalLogInfo;
    private readonly IAuditor _auditor;
    private readonly IGroupMembershipProvider _groupMembershipProvider;

    protected InfrastructureApiControllerBase(IOperationInfoProvider operationInfoProvider, IAuditor auditor, ILoggerFactory loggerFactory, IIdentityProvider identityProvider, IGroupMembershipProvider groupMembershipProvider)
    {
        LoggerFactory = loggerFactory ?? throw new ArgumentNullException(nameof(loggerFactory));
        Logger = loggerFactory.CreateLogger(GetType());
        _additionalLogInfo = new AdditionalLogInfo(new Platform.Logging.LoggingCategory("AWP.Infrastructure.Api"));

        IdentityProvider = identityProvider ?? throw new ArgumentNullException(nameof(identityProvider));
        _auditor = auditor;
        _groupMembershipProvider = groupMembershipProvider ?? throw new ArgumentNullException(nameof(groupMembershipProvider));

        OperationInfoProvider = operationInfoProvider;

        DoStaticInitialize();
    }

    public IOperationInfoProvider OperationInfoProvider { get; }

    protected IIdentityProvider IdentityProvider { get; }

    protected ILoggerFactory LoggerFactory { get; }

    protected ILogger Logger { get; }

    AdditionalLogInfo IHasAdditionalLogInfo.AdditionalLogInfo => _additionalLogInfo;

    protected AuditExecutionHelper CreateAuditExecutionHelper(string operationSuffixForAudit = null)
    {
        return AuditExecutionHelper.CreateForController(this, _auditor, LoggerFactory, operationSuffixForAudit);
    }

    internal void DoStaticInitialize()
    {
        var type = GetType();
        if (_initializedServices != null && _initializedServices.Contains(type))
        {
            return;
        }

        using var mlh = this.CreateMethodLogHelper(Logger).WithNoArgs();
        lock (InitLockObj)
        {
            if (_initializedServices == null)
            {
                _initializedServices = new List<Type>();
            }

            if (_initializedServices.Contains(type))
            {
                return;
            }

            try
            {
                mlh.LogMethodDetails("Start static initializion");
                StaticInitialize();
                _initializedServices.Add(type);
                mlh.LogMethodDetails("Static initializion finished");
            }
            catch (Exception ex)
            {
                mlh.LogMethodError(ex, "Static initializion failed!");
                throw;
            }
        }
    }

    protected virtual void StaticInitialize()
    {
    }

    protected async Task<bool> IsCurrentUserInGroup(string groupName)
    {
        if (IdentityProvider == null) throw new InvalidOperationException($"{typeof(IIdentityProvider)} not resolved");
        if (_groupMembershipProvider == null) throw new InvalidOperationException($"{typeof(IGroupMembershipProvider)} not resolved");

        return await IsInGroupHelper.IsCurrentUserInGroup(HttpContext, IdentityProvider, _groupMembershipProvider, groupName, Logger, this);
    }
}

public abstract class InfrastructureApiControllerBase<TConfig> : InfrastructureApiControllerBase
{
    protected InfrastructureApiControllerBase(IOperationInfoProvider operationInfoProvider, IAuditor auditor, ILoggerFactory loggerFactory, IIdentityProvider identityProvider, IGroupMembershipProvider groupMembershipProvider, TConfig configuration)
        : base(operationInfoProvider, auditor, loggerFactory, identityProvider, groupMembershipProvider)
    {
        Configuration = configuration;
    }

    protected TConfig Configuration { get; }
}
