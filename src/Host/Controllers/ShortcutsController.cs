using Microsoft.AspNetCore.Mvc;
using Platform.AWP.DataContracts.Infrastructure;
using Platform.Identities;
using Platform.Logging.MicrosoftExtensions;
using Platform.OperationInfo;
using Product.AWP.Infrastructure.Controllers.Base;
using Product.AWP.Infrastructure.DAL.Interfaces.Audit;
using Product.AWP.Infrastructure.DAL.Interfaces.Repositories;
using Product.AWP.Infrastructure.Domain.Interfaces;

namespace Product.AWP.Infrastructure.Controllers;

public class ShortcutsController : InfrastructureApiControllerBase, IShortcutsWebApi
{
    private const string CustomShortcutsConfigurationVersionKey = "CustomShortcuts";
    private readonly IConfigurationVersionRepository _configurationVersionRepository;
    private readonly IShortcutsRepository _shortcutsRepository;

    public ShortcutsController(IShortcutsRepository shortcutsRepository, IConfigurationVersionRepository configurationVersionRepository,
        IOperationInfoProvider operationInfoProvider, IAuditor auditor,
        ILoggerFactory loggerFactory, IIdentityProvider identityProvider, IGroupMembershipProvider groupMembershipProvider)
        : base(operationInfoProvider, auditor, loggerFactory, identityProvider, groupMembershipProvider)
    {
        _shortcutsRepository = shortcutsRepository;
        _configurationVersionRepository = configurationVersionRepository;
    }

    /// <summary>
    /// Gets list of shortcuts for current user
    /// </summary>
    [HttpGet]
    public async Task<Shortcut[]> GetShortcutsForCurrentUser(Guid userRoleId, Guid? serviceAreaId)
    {
        using var mlh = this.CreateMethodLogHelper(Logger).WithArgs("userRoleId={userRoleId}, serviceAreaId={serviceAreaId}", userRoleId, serviceAreaId);

        return await mlh.ExecuteWithTryAsync(async () => await _shortcutsRepository.GetShortcutsForCurrentUser(serviceAreaId, userRoleId));
    }

    /// <summary>
    /// Gets list of shortcuts for current user
    /// </summary>
    [HttpGet]
    public async Task<UserShortcut[]> GetCurrentUserCustomShortcuts()
    {
        using var mlh = this.CreateMethodLogHelper(Logger).WithNoArgs();

        return await mlh.ExecuteWithTryAsync(async () =>
        {
            var userLogin = (OperationInfoProvider.GetCurrentOperationInfo() ?? OperationInfoProvider.GetPreviousOperationsInfo()?.FirstOrDefault()).Login;
            return await _shortcutsRepository.GetCurrentUserCustomShortcuts(userLogin);
        });
    }

    /// <summary>
    /// Adds shortcut for current user
    /// </summary>
    /// <param name="userShortcut">
    /// The shortcut.
    /// </param>
    [HttpPut]
    public async Task AddCurrentUserCustomShortcut([FromBody] UserShortcut userShortcut)
    {
        using var mlh = this.CreateMethodLogHelper(Logger).WithArgs("userShortcut={@userShortcut}", userShortcut);

        await CreateAuditExecutionHelper().ExecuteWithTryWithAudit(
            mlh,
            () => _shortcutsRepository,
            async da =>
            {
                var userLogin = (OperationInfoProvider.GetCurrentOperationInfo() ?? OperationInfoProvider.GetPreviousOperationsInfo()?.FirstOrDefault()).Login;
                await da.AddCurrentUserCustomShortcut(userShortcut, userLogin);
                await _configurationVersionRepository.UpdateVersion(CustomShortcutsConfigurationVersionKey);
            });
    }

    /// <summary>
    /// Updates current user shortcut
    /// </summary>
    /// <param name="userShortcut">
    /// The user shortcut.
    /// </param>
    [HttpPost]
    public async Task UpdateCurrentUserCustomShortcut([FromBody] UserShortcut userShortcut)
    {
        using var mlh = this.CreateMethodLogHelper(Logger).WithArgs("userShortcut={@userShortcut}", userShortcut);

        await CreateAuditExecutionHelper().ExecuteWithTryWithAudit(
            mlh,
            () => _shortcutsRepository,
            async da =>
            {
                await da.UpdateCurrentUserCustomShortcut(userShortcut);
                await _configurationVersionRepository.UpdateVersion(CustomShortcutsConfigurationVersionKey);
            });
    }

    /// <summary>
    /// Removes current user shortcut
    /// </summary>
    /// <param name="userShortcutId">
    /// Id of user shortcut.
    /// </param>
    [HttpDelete]
    public async Task RemoveCurrentUserCustomShortcut([FromQuery] Guid userShortcutId)
    {
        using var mlh = this.CreateMethodLogHelper(Logger).WithArgs("userShortcutId={userShortcutId}", userShortcutId);

        await CreateAuditExecutionHelper().ExecuteWithTryWithAudit(
            mlh,
            () => _shortcutsRepository,
            async da =>
            {
                await da.RemoveCurrentUserCustomShortcut(userShortcutId);
                await _configurationVersionRepository.UpdateVersion(CustomShortcutsConfigurationVersionKey);
            });
    }
}
