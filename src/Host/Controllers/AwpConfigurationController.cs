using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Caching.Hybrid;
using Microsoft.Extensions.Options;
using Platform.AWP.DataContracts.Infrastructure;
using Platform.Identities;
using Platform.Logging.MicrosoftExtensions;
using Platform.OperationInfo;
using Product.AWP.Infrastructure.Configurations;
using Product.AWP.Infrastructure.Controllers.Base;
using Product.AWP.Infrastructure.DAL.Interfaces.Audit;
using Product.AWP.Infrastructure.DAL.Interfaces.Repositories;
using Product.AWP.Infrastructure.Domain.Interfaces;
using Product.AWP.Infrastructure.Helpers;

namespace Product.AWP.Infrastructure.Controllers;

public class AwpConfigurationController : InfrastructureApiControllerBase, IAwpConfigurationWebApi
{
    private readonly IConfigurationRepository _configurationRepository;
    private readonly IProfileRepository _profileRepository;
    private readonly IUserGroupRepository _userGroupRepository;
    private readonly HybridCache _cache;
    private readonly CacheOptions _cacheOptions;
    
    public AwpConfigurationController(
        IUserGroupRepository userGroupRepository, IProfileRepository profileRepository, IConfigurationRepository configurationRepository,
        IOperationInfoProvider operationInfoProvider, IAuditor auditor, ILoggerFactory loggerFactory, IIdentityProvider identityProvider, 
        IGroupMembershipProvider groupMembershipProvider, HybridCache cache, IOptionsMonitor<CacheOptions> cacheOptions)
        : base(operationInfoProvider, auditor, loggerFactory, identityProvider, groupMembershipProvider)
    {
        _userGroupRepository = userGroupRepository;
        _profileRepository = profileRepository;
        _configurationRepository = configurationRepository;
        _cache = cache;
        _cacheOptions = cacheOptions.CurrentValue;
    }

    [HttpGet]
    public async Task<ProfileData[]> GetAvailableProfiles()
    {
        using var mlh = this.CreateMethodLogHelper(Logger).WithNoArgs();

        var userGroups = await mlh.ExecuteWithTryAsync(async () =>
        {
            return await _cache.GetOrCreateAsync(WellKnownCacheKeys.UserGroupsList, 
                async token => await _userGroupRepository.GetList(), 
                new HybridCacheEntryOptions { Expiration = _cacheOptions.ConfigurationCacheDuration },
                ["awp", "configuration"]);
            
        }, "GetUserGroupsList");
        var currentUserGroups = new List<Guid>(userGroups.Length);

        var ugCount = 0;
        var idCount = 0;
        foreach (var userGroup in userGroups)
        {
            ugCount++;
            mlh.LogMethodDetails("Processing profile name=={userGroup_Name}...", userGroup.Name);

            foreach (var userGroupIdentity in userGroup.Identities)
            {
                idCount++;
                if (await IsCurrentUserInGroup(userGroupIdentity.Login))
                {
                    Logger.DebugDev("User is in group {userGroupIdentity_Login}, adding profile name={userGroup_Name}", userGroupIdentity.Login, userGroup.Name);
                    currentUserGroups.Add(userGroup.Id);
                    break;
                }

                mlh.LogMethodDetails("User is NOT in group {userGroupIdentity_Login}", userGroupIdentity.Login);
            }
        }

        mlh.LogMethodDetails("Check stats: ugCount: {ugCount}; idCount: {idCount}", ugCount, idCount);
        return await mlh.ExecuteWithTryAsync(async () =>
        {
            return await _cache.GetOrCreateAsync(WellKnownCacheKeys.UserProfilesList, 
                async token => await _profileRepository.GetProfiles(currentUserGroups.ToArray()), 
                new HybridCacheEntryOptions { Expiration = _cacheOptions.ConfigurationCacheDuration },
                ["awp", "configuration"]);
        }, "ProfileDataAccessor.GetProfiles");
    }

    [HttpGet]
    public async Task<ProfilesAndEssentialsData> GetAvailableProfilesWithAllEssentials()
    {
        using var mlh = this.CreateMethodLogHelper(Logger).WithNoArgs();
        var profilesData = await GetAvailableProfiles();

        const string getUserRolesWithNoUserGroups = nameof(_profileRepository.GetUserRoles);
        mlh.LogMethodDetails("Calling {getUserRolesWithNoUserGroups}", getUserRolesWithNoUserGroups);
        var allUserRoles = await _cache.GetOrCreateAsync(WellKnownCacheKeys.UserRolesList, 
            async token => await _profileRepository.GetUserRoles(), 
            new HybridCacheEntryOptions { Expiration = _cacheOptions.ConfigurationCacheDuration },
            ["awp", "configuration"]);
        mlh.LogMethodDetails("{getUserRolesWithNoUserGroups} completed", getUserRolesWithNoUserGroups);

        const string getServiceAreasWithNoUserGroups = nameof(_profileRepository.GetServiceAreas);
        mlh.LogMethodDetails("Calling {getServiceAreasWithNoUserGroups}", getServiceAreasWithNoUserGroups);
        var allServiceAreas = await _cache.GetOrCreateAsync(WellKnownCacheKeys.ServiceAreasList, 
            async token => await _profileRepository.GetServiceAreas(), 
            new HybridCacheEntryOptions { Expiration = _cacheOptions.ConfigurationCacheDuration },
            ["awp", "configuration"]);
        
        mlh.LogMethodDetails("{getServiceAreasWithNoUserGroups} completed", getServiceAreasWithNoUserGroups);

        const string getWorkplacesWithNoUserGroups = nameof(_profileRepository.GetWorkplaces);
        mlh.LogMethodDetails("Calling {getWorkplacesWithNoUserGroups)}", getWorkplacesWithNoUserGroups);
        var allWorkPlaces = await _cache.GetOrCreateAsync(WellKnownCacheKeys.WorkplacesList, 
            async token => await _profileRepository.GetWorkplaces(), 
            new HybridCacheEntryOptions { Expiration = _cacheOptions.ConfigurationCacheDuration },
            ["awp", "configuration"]);
        
        mlh.LogMethodDetails("{pda_GetWorkplacesWithNoUserGroups} completed", getWorkplacesWithNoUserGroups);

        return new ProfilesAndEssentialsData
        {
            ProfilesData = profilesData,
            AllUserRoles = allUserRoles,
            AllServiceAreas = allServiceAreas,
            AllWorkplaces = allWorkPlaces
        };
    }

    [HttpGet]
    public async Task<ModuleDescription[]> GetModules([FromQuery] AwpClientTypes clientTypes, [FromQuery] Guid userRoleId, [FromQuery] Guid? serviceAreaId)
    {
        using var mlh = this.CreateMethodLogHelper(Logger).WithArgs("clientTypes={clientTypes}, userRoleId={userRoleId}, serviceAreaId={serviceAreaId}", clientTypes, userRoleId, serviceAreaId);

        var modules = await mlh.ExecuteWithTryAsync(async () =>
        {
            return await _cache.GetOrCreateAsync(WellKnownCacheKeys.CreateModulesListKey(clientTypes, userRoleId, serviceAreaId), 
                async token => (await _configurationRepository.GetModulesForCurrentUser(serviceAreaId, userRoleId, clientTypes)).ToArray(), 
                new HybridCacheEntryOptions { Expiration = _cacheOptions.ConfigurationCacheDuration },
                ["awp", "configuration"]);
        });
        return modules ?? [];
    }

    [HttpGet]
    public async Task<Application[]> GetHostedApplications([FromQuery] AwpClientTypes clientTypes, [FromQuery] Guid userRoleId, [FromQuery] Guid? serviceAreaId)
    {
        using var mlh = this.CreateMethodLogHelper(Logger).WithArgs("clientTypes={clientTypes}, userRoleId={userRoleId}, serviceAreaId={serviceAreaId}", clientTypes, userRoleId, serviceAreaId);
        
        return await mlh.ExecuteWithTryAsync(async () => (await _configurationRepository.GetHostedApplications(serviceAreaId, userRoleId, clientTypes)).ToArray());
    }

    [HttpGet]
    public async Task<LoginWorkflowWithNoAppInfo[]> GetLoginWorkflowsForApplication([FromQuery] AwpClientTypes clientTypes, [FromQuery] Guid applicationId)
    {
        using var mlh = this.CreateMethodLogHelper(Logger).WithArgs("clientTypes={clientTypes}, applicationId={applicationId}", clientTypes, applicationId);

        var result = await mlh.ExecuteWithTryAsync(async () => await _configurationRepository.GetLoginWorkflowsForApplication(applicationId, clientTypes));
        return result.Cast<LoginWorkflowWithNoAppInfo>().ToArray();
    }

    [HttpGet]
    public async Task<LoginWorkflowWithNoAppInfo[]> GetLoginWorkflowForApplicationWithStringId([FromQuery] AwpClientTypes clientTypes, [FromQuery] string applicationId)
    {
        return await GetLoginWorkflowsForApplication(clientTypes, Guid.Parse(applicationId));
    }

    [HttpGet]
    public async Task<Layout[]> GetLayouts([FromQuery] AwpClientTypes clientTypes, [FromQuery] Guid userRoleId)
    {
        using var mlh = this.CreateMethodLogHelper(Logger).WithArgs("clientTypes={clientTypes}, userRoleId={userRoleId}", clientTypes, userRoleId);

        return await mlh.ExecuteWithTryAsync(async () => await _configurationRepository.GetLayouts(userRoleId));
    }

    [HttpGet]
    public async Task<Workflow[]> GetWorkflows([FromQuery] AwpClientTypes clientTypes, [FromQuery] Guid userRoleId)
    {
        using var mlh = this.CreateMethodLogHelper(Logger).WithArgs("clientTypes={clientTypes}, userRoleId={userRoleId}", clientTypes, userRoleId);

        return await mlh.ExecuteWithTryAsync(async () =>
        {
            return await _cache.GetOrCreateAsync(WellKnownCacheKeys.CreateWorkflowListKey(clientTypes, userRoleId), 
                async token => await _configurationRepository.GetWorkflows(userRoleId, clientTypes), 
                new HybridCacheEntryOptions { Expiration = _cacheOptions.ConfigurationCacheDuration },
                ["awp", "configuration"]);
        });
    }

    /// <summary>
    /// Gets server items' versions
    /// </summary>
    /// <returns>
    /// Dictionary of items' keys and corresponding versions
    /// </returns>
    [HttpGet]
    public async Task<IDictionary<string, Guid>> GetConfigurationsVersions()
    {
        using var mlh = this.CreateMethodLogHelper(Logger).WithNoArgs();

        return await mlh.ExecuteWithTryAsync(async () => await _configurationRepository.GetConfigurationsVersions());
    }
}
