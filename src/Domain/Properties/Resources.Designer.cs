//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Product.AWP.Infrastructure.Domain.Properties {
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "17.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    internal class Resources {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal Resources() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("Platform.AWP.DataContracts.Properties.Resources", typeof(Resources).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Контрол.
        /// </summary>
        internal static string ApplicationType_Control {
            get {
                return ResourceManager.GetString("ApplicationType_Control", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Win32 приложение.
        /// </summary>
        internal static string ApplicationType_External {
            get {
                return ResourceManager.GetString("ApplicationType_External", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Win32 Java приложение.
        /// </summary>
        internal static string ApplicationType_ExternalJava {
            get {
                return ResourceManager.GetString("ApplicationType_ExternalJava", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Win32 приложение поддерживающее интеграцию через UI Automation(WPF).
        /// </summary>
        internal static string ApplicationType_ExternalUIAutomation {
            get {
                return ResourceManager.GetString("ApplicationType_ExternalUIAutomation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Web приложение (IE).
        /// </summary>
        internal static string ApplicationType_Web {
            get {
                return ResourceManager.GetString("ApplicationType_Web", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Web приложение (Chrome).
        /// </summary>
        internal static string ApplicationType_WebChrome {
            get {
                return ResourceManager.GetString("ApplicationType_WebChrome", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Web приложение (Chrome control).
        /// </summary>
        internal static string ApplicationType_WebChromeControl {
            get {
                return ResourceManager.GetString("ApplicationType_WebChromeControl", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Web приложение, запускаемое в элементе управления WPF WebBrowser.
        /// </summary>
        internal static string ApplicationType_WebControl {
            get {
                return ResourceManager.GetString("ApplicationType_WebControl", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Web приложение (FireFox).
        /// </summary>
        internal static string ApplicationType_WebFirefox {
            get {
                return ResourceManager.GetString("ApplicationType_WebFirefox", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Web приложение (FireFox control).
        /// </summary>
        internal static string ApplicationType_WebFirefoxControl {
            get {
                return ResourceManager.GetString("ApplicationType_WebFirefoxControl", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Web приложение поддерживающее интеграцию через UI Automation(Silverlight).
        /// </summary>
        internal static string ApplicationType_WebUIAutomation {
            get {
                return ResourceManager.GetString("ApplicationType_WebUIAutomation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Вниз.
        /// </summary>
        internal static string ExpandDirection_Down {
            get {
                return ResourceManager.GetString("ExpandDirection_Down", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Влево.
        /// </summary>
        internal static string ExpandDirection_Left {
            get {
                return ResourceManager.GetString("ExpandDirection_Left", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Вправо.
        /// </summary>
        internal static string ExpandDirection_Right {
            get {
                return ResourceManager.GetString("ExpandDirection_Right", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Вверх.
        /// </summary>
        internal static string ExpandDirection_Up {
            get {
                return ResourceManager.GetString("ExpandDirection_Up", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to На одном уровне с приложениями.
        /// </summary>
        internal static string ExpandDisplayMode_Integral {
            get {
                return ResourceManager.GetString("ExpandDisplayMode_Integral", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Поверх приложений.
        /// </summary>
        internal static string ExpandDisplayMode_Popup {
            get {
                return ResourceManager.GetString("ExpandDisplayMode_Popup", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Тип:\s(.*); Операция:\s(.*); Продолжительность:\s([0-9]*)\sмсек.
        /// </summary>
        internal static string PerformanceObjectParsePattern {
            get {
                return ResourceManager.GetString("PerformanceObjectParsePattern", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Доступ запрещен.
        /// </summary>
        internal static string PermissionType_Denied {
            get {
                return ResourceManager.GetString("PermissionType_Denied", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Только просмотр.
        /// </summary>
        internal static string PermissionType_Read {
            get {
                return ResourceManager.GetString("PermissionType_Read", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Просмотр и редактирование.
        /// </summary>
        internal static string PermissionType_ReadAndWrite {
            get {
                return ResourceManager.GetString("PermissionType_ReadAndWrite", resourceCulture);
            }
        }
    }
}
