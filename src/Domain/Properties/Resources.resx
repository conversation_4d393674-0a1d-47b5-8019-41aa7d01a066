<?xml version="1.0" encoding="utf-8"?>
<root>
    <!-- 
      Microsoft ResX Schema 
      
      Version 2.0
      
      The primary goals of this format is to allow a simple XML format 
      that is mostly human readable. The generation and parsing of the 
      various data types are done through the TypeConverter classes 
      associated with the data types.
      
      Example:
      
      ... ado.net/XML headers & schema ...
      <resheader name="resmimetype">text/microsoft-resx</resheader>
      <resheader name="version">2.0</resheader>
      <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
      <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
      <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
      <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
      <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
          <value>[base64 mime encoded serialized .NET Framework object]</value>
      </data>
      <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
          <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
          <comment>This is a comment</comment>
      </data>
                  
      There are any number of "resheader" rows that contain simple 
      name/value pairs.
      
      Each data row contains a name, and value. The row also contains a 
      type or mimetype. Type corresponds to a .NET class that support 
      text/value conversion through the TypeConverter architecture. 
      Classes that don't support this are serialized and stored with the 
      mimetype set.
      
      The mimetype is used for serialized objects, and tells the 
      ResXResourceReader how to depersist the object. This is currently not 
      extensible. For a given mimetype the value must be set accordingly:
      
      Note - application/x-microsoft.net.object.binary.base64 is the format 
      that the ResXResourceWriter will generate, however the reader can 
      read any of the formats listed below.
      
      mimetype: application/x-microsoft.net.object.binary.base64
      value   : The object must be serialized with 
              : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
              : and then encoded with base64 encoding.
      
      mimetype: application/x-microsoft.net.object.soap.base64
      value   : The object must be serialized with 
              : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
              : and then encoded with base64 encoding.
  
      mimetype: application/x-microsoft.net.object.bytearray.base64
      value   : The object must be serialized into a byte array 
              : using a System.ComponentModel.TypeConverter
              : and then encoded with base64 encoding.
      -->
    <xsd:schema
            xmlns:xsd="http://www.w3.org/2001/XMLSchema"
            xmlns:msdata="urn:schemas-microsoft-com:xml-msdata"
            id="root"
            xmlns="">
        <xsd:import
                namespace="http://www.w3.org/XML/1998/namespace"/>
        <xsd:element
                name="root"
                msdata:IsDataSet="true">
            <xsd:complexType>
                <xsd:choice
                        maxOccurs="unbounded">
                    <xsd:element
                            name="metadata">
                        <xsd:complexType>
                            <xsd:sequence>
                                <xsd:element
                                        name="value"
                                        type="xsd:string"
                                        minOccurs="0"/>
                            </xsd:sequence>
                            <xsd:attribute
                                    name="name"
                                    use="required"
                                    type="xsd:string"/>
                            <xsd:attribute
                                    name="type"
                                    type="xsd:string"/>
                            <xsd:attribute
                                    name="mimetype"
                                    type="xsd:string"/>
                            <xsd:attribute
                                    ref="xml:space"/>
                        </xsd:complexType>
                    </xsd:element>
                    <xsd:element
                            name="assembly">
                        <xsd:complexType>
                            <xsd:attribute
                                    name="alias"
                                    type="xsd:string"/>
                            <xsd:attribute
                                    name="name"
                                    type="xsd:string"/>
                        </xsd:complexType>
                    </xsd:element>
                    <xsd:element
                            name="data">
                        <xsd:complexType>
                            <xsd:sequence>
                                <xsd:element
                                        name="value"
                                        type="xsd:string"
                                        minOccurs="0"
                                        msdata:Ordinal="1"/>
                                <xsd:element
                                        name="comment"
                                        type="xsd:string"
                                        minOccurs="0"
                                        msdata:Ordinal="2"/>
                            </xsd:sequence>
                            <xsd:attribute
                                    name="name"
                                    type="xsd:string"
                                    use="required"
                                    msdata:Ordinal="1"/>
                            <xsd:attribute
                                    name="type"
                                    type="xsd:string"
                                    msdata:Ordinal="3"/>
                            <xsd:attribute
                                    name="mimetype"
                                    type="xsd:string"
                                    msdata:Ordinal="4"/>
                            <xsd:attribute
                                    ref="xml:space"/>
                        </xsd:complexType>
                    </xsd:element>
                    <xsd:element
                            name="resheader">
                        <xsd:complexType>
                            <xsd:sequence>
                                <xsd:element
                                        name="value"
                                        type="xsd:string"
                                        minOccurs="0"
                                        msdata:Ordinal="1"/>
                            </xsd:sequence>
                            <xsd:attribute
                                    name="name"
                                    type="xsd:string"
                                    use="required"/>
                        </xsd:complexType>
                    </xsd:element>
                </xsd:choice>
            </xsd:complexType>
        </xsd:element>
    </xsd:schema>
    <resheader
            name="resmimetype">
        <value>
            text/microsoft-resx
        </value>
    </resheader>
    <resheader
            name="version">
        <value>
            2.0
        </value>
    </resheader>
    <resheader
            name="reader">
        <value>
            System.Resources.ResXResourceReader,
            System.Windows.Forms,
            Version=4.0.0.0,
            Culture=neutral,
            PublicKeyToken=b77a5c561934e089
        </value>
    </resheader>
    <resheader
            name="writer">
        <value>
            System.Resources.ResXResourceWriter,
            System.Windows.Forms,
            Version=4.0.0.0,
            Culture=neutral,
            PublicKeyToken=b77a5c561934e089
        </value>
    </resheader>
    <data name="ApplicationType_Control"
          xml:space="preserve">
    <value>Контрол</value>
  </data>
    <data name="ApplicationType_External"
          xml:space="preserve">
    <value>Win32 приложение</value>
  </data>
    <data name="ApplicationType_ExternalJava"
          xml:space="preserve">
    <value>Win32 Java приложение</value>
  </data>
    <data name="ApplicationType_ExternalUIAutomation"
          xml:space="preserve">
    <value>Win32 приложение поддерживающее интеграцию через UI Automation(WPF)</value>
  </data>
    <data name="ApplicationType_Web"
          xml:space="preserve">
    <value>Web приложение (IE)</value>
  </data>
    <data name="ApplicationType_WebControl"
          xml:space="preserve">
    <value>Web приложение, запускаемое в элементе управления WPF WebBrowser</value>
  </data>
    <data name="ApplicationType_WebFirefox"
          xml:space="preserve">
    <value>Web приложение (FireFox)</value>
  </data>
    <data name="ApplicationType_WebUIAutomation"
          xml:space="preserve">
    <value>Web приложение поддерживающее интеграцию через UI Automation(Silverlight)</value>
  </data>
    <data name="ExpandDirection_Down"
          xml:space="preserve">
    <value>Вниз</value>
  </data>
    <data name="ExpandDirection_Left"
          xml:space="preserve">
    <value>Влево</value>
  </data>
    <data name="ExpandDirection_Right"
          xml:space="preserve">
    <value>Вправо</value>
  </data>
    <data name="ExpandDirection_Up"
          xml:space="preserve">
    <value>Вверх</value>
  </data>
    <data name="ExpandDisplayMode_Popup"
          xml:space="preserve">
    <value>Поверх приложений</value>
  </data>
    <data name="ExpandDisplayMode_Integral"
          xml:space="preserve">
    <value>На одном уровне с приложениями</value>
  </data>
    <data name="PerformanceObjectParsePattern"
          xml:space="preserve">
    <value>Тип:\s(.*); Операция:\s(.*); Продолжительность:\s([0-9]*)\sмсек</value>
  </data>
    <data name="PermissionType_Denied"
          xml:space="preserve">
    <value>Доступ запрещен</value>
  </data>
    <data name="PermissionType_Read"
          xml:space="preserve">
    <value>Только просмотр</value>
  </data>
    <data name="PermissionType_ReadAndWrite"
          xml:space="preserve">
    <value>Просмотр и редактирование</value>
  </data>
    <data name="ApplicationType_WebChrome"
          xml:space="preserve">
    <value>Web приложение (Chrome)</value>
  </data>
    <data name="ApplicationType_WebChromeControl"
          xml:space="preserve">
    <value>Web приложение (Chrome control)</value>
  </data>
    <data name="ApplicationType_WebFirefoxControl"
          xml:space="preserve">
    <value>Web приложение (FireFox control)</value>
  </data>
</root>