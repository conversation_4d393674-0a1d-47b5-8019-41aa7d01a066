<root>
    <resheader
            name="resmimetype">
        <value>
            text/microsoft-resx
        </value>
    </resheader>
    <resheader
            name="version">
        <value>
            1.3
        </value>
    </resheader>
    <resheader
            name="reader">
        <value>
            System.Resources.ResXResourceReader,
            System.Windows.Forms,
            Version=2.0.0.0,
            Culture=neutral,
            PublicKeyToken=b77a5c561934e089
        </value>
    </resheader>
    <resheader
            name="writer">
        <value>
            System.Resources.ResXResourceWriter,
            System.Windows.Forms,
            Version=2.0.0.0,
            Culture=neutral,
            PublicKeyToken=b77a5c561934e089
        </value>
    </resheader>
    <data name="ApplicationType_Control"
          xml:space="preserve">
		<value>Контрол</value>
	</data>
    <data name="ApplicationType_External"
          xml:space="preserve">
		<value>Win32 қосымшасы</value>
	</data>
    <data name="ApplicationType_ExternalJava"
          xml:space="preserve">
		<value>Win32 Java қосымшасы</value>
	</data>
    <data name="ApplicationType_ExternalUIAutomation"
          xml:space="preserve">
		<value> UI Automation(WPF) арқылы интеграцияны қолдайтын Win32 қосымшасы</value>
	</data>
    <data name="ApplicationType_Web"
          xml:space="preserve">
		<value>Web қосымша (IE)</value>
	</data>
    <data name="ApplicationType_WebChrome"
          xml:space="preserve">
		<value>Web қосымша (Chrome)</value>
	</data>
    <data name="ApplicationType_WebChromeControl"
          xml:space="preserve">
		<value>Web қосымша (Chrome control)</value>
	</data>
    <data name="ApplicationType_WebControl"
          xml:space="preserve">
		<value> WPF WebBrowser басқару элементінде қосылатын Web қосымшасы</value>
	</data>
    <data name="ApplicationType_WebFirefox"
          xml:space="preserve">
		<value>Web қосымша (FireFox)</value>
	</data>
    <data name="ApplicationType_WebFirefoxControl"
          xml:space="preserve">
		<value>Web қосымша (FireFox control)</value>
	</data>
    <data name="ApplicationType_WebUIAutomation"
          xml:space="preserve">
		<value>UI Automation(Silverlight) арқылы интеграцияны қолдайтын Web қосымшасы</value>
	</data>
    <data name="ExpandDirection_Down"
          xml:space="preserve">
		<value>Төмен</value>
	</data>
    <data name="ExpandDirection_Left"
          xml:space="preserve">
		<value>Солға</value>
	</data>
    <data name="ExpandDirection_Right"
          xml:space="preserve">
		<value>Оңға</value>
	</data>
    <data name="ExpandDirection_Up"
          xml:space="preserve">
		<value>Жоғары</value>
	</data>
    <data name="ExpandDisplayMode_Integral"
          xml:space="preserve">
		<value>Қосымшалармен бір деңгейде</value>
	</data>
    <data name="ExpandDisplayMode_Popup"
          xml:space="preserve">
		<value>Қосымшалар үстінен</value>
	</data>
    <data name="PerformanceObjectParsePattern"
          xml:space="preserve">
		<value>Тип:\s(.*); Операция:\s(.*); Ұзақтылығы:\s([0-9]*)\sмсек</value>
	</data>
    <data name="PermissionType_Denied"
          xml:space="preserve">
		<value>Рұқсат жоқ</value>
	</data>
    <data name="PermissionType_Read"
          xml:space="preserve">
		<value>Тек көру үшін</value>
	</data>
    <data name="PermissionType_ReadAndWrite"
          xml:space="preserve">
		<value>Көру және редакциялау</value>
	</data>
</root>