using Platform.AWP.DataContracts.Infrastructure;

namespace Product.AWP.Infrastructure.Domain.Interfaces;

/// <summary>
/// Contact for service that retrives permissions from database
/// </summary>
public interface IPermissionsWebApi
{
    /// <summary>
    /// Gets list of permissions for current user
    /// </summary>
    Task<Permission[]> GetPermissionsForCurrentUser(Guid userRoleId);

    /// <summary>
    /// Checks if current user has permission.
    /// </summary>
    Task<bool> CheckPermission(string permission, Guid userRoleId);
}
