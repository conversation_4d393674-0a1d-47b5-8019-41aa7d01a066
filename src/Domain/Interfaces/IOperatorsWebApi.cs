using Platform.AWP.DataContracts.Infrastructure;
using Product.AWP.Infrastructure.Domain.Entities;

namespace Product.AWP.Infrastructure.Domain.Interfaces;

public interface IOperatorsWebApi
{
    Task<Operator> GetOperatorInfo(Guid operatorId);
    Task<Operator[]> FindByUserNames(string[] userNames);
    Task<Operator[]> FindByActiveDirectoryIds(Guid[] adIds);

    /// <summary>
    /// Получить кастомные атрибуты для указаного списка операторов	
    /// </summary>
    /// <param name="operatorIds">Список идентификаторов операторов для которых требуется получить кастомные атрибуты</param>
    /// <param name="caCodes">Список кодов кастомных атрибутов которые нужно получить. Если null - получить все какие есть</param>
    /// <returns>Список кастомных атрибутов</returns>
    Task<OperatorCustomAttribute[]> GetCustomAttributesForOperators(Guid[] operatorIds, string[] caCodes);

    Task AddOrUpdateCustomAttributes(OperatorCustomAttribute[] attributes);

    Task RemoveCustomAttribute(Guid operatorId, string[] caCodes);

    Task<OperatorCustomAttribute[]> GetCustomAttributes(Guid operatorId, string[] caCodes);


    Task<Operator[]> FindByCustomAttributeStringValue(string customAttributeCode, string customAttributeValue);
    Task<Operator[]> FindByCustomAttributeGuidValue(string customAttributeCode, Guid? customAttributeValue);
    Task<Operator[]> FindByCustomAttributeLongValue(string customAttributeCode, long? customAttributeValue);
    Task<Operator[]> FindByCustomAttributeDecimalValue(string customAttributeCode, decimal? customAttributeValue);
    Task<Operator[]> FindByCustomAttributeBoolValue(string customAttributeCode, bool? customAttributeValue);
    Task<Operator[]> FindByCustomAttributeDateTimeValue(string customAttributeCode, DateTime? customAttributeValue);

    Task RemoveCustomAttributes(RemoveOperatorsCustomAttributesData removeOperatorsCustomAttributesData);
}
