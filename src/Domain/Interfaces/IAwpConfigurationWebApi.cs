using Platform.AWP.DataContracts.Infrastructure;

namespace Product.AWP.Infrastructure.Domain.Interfaces;

public interface IAwpConfigurationWebApi
{
    Task<ProfileData[]> GetAvailableProfiles();
    Task<ProfilesAndEssentialsData> GetAvailableProfilesWithAllEssentials();

    Task<ModuleDescription[]> GetModules(AwpClientTypes clientTypes, Guid userRoleId, Guid? serviceAreaId);
    Task<Application[]> GetHostedApplications(AwpClientTypes clientTypes, Guid userRoleId, Guid? serviceAreaId);
    Task<LoginWorkflowWithNoAppInfo[]> GetLoginWorkflowsForApplication(AwpClientTypes clientTypes, Guid applicationId);
    Task<LoginWorkflowWithNoAppInfo[]> GetLoginWorkflowForApplicationWithStringId(AwpClientTypes clientTypes, string applicationId);
    Task<Layout[]> GetLayouts(AwpClientTypes clientTypes, Guid userRoleId);
    Task<Workflow[]> GetWorkflows(AwpClientTypes clientTypes, Guid userRoleId);
    Task<IDictionary<string, Guid>> GetConfigurationsVersions();
}
