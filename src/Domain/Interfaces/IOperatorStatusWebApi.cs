using Platform.AWP.DataContracts.Infrastructure;
using Product.AWP.Infrastructure.Domain.Entities;

namespace Product.AWP.Infrastructure.Domain.Interfaces;

/// <summary>
/// Интерфейс работы со статусами операторов.
/// </summary>
public interface IOperatorStatusWebApi
{
    /// <summary>
    /// Возвращает все возможные статусы
    /// </summary>
    Task<OperatorStatus[]> GetAvailableStatuses(Guid userRoleId);

    /// <summary>
    /// Возвращает список статусов на которые возможен переход из текущего
    /// </summary>
    Task<OperatorStatus[]> GetAvailableFrom(Guid currentStatusId, Guid userRoleId);

    /// <summary>
    /// Возращает список кастомных аттрибутов для указанного статуса
    /// </summary>
    /// <param name="statusId">Id статуса оператора</param>
    /// <param name="attributeNames">Список имён аттрибутов которые нужно возвращать. Если указан null - возвращает все атрибуты</param>
    /// <returns></returns>
    Task<CustomAttribute[]> GetCustomAttributes(Guid statusId, NullableWebApiStringArray attributeNames);
}
