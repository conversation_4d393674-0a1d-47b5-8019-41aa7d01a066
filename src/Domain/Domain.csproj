<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
        <AssemblyName>Product.AWP.Infrastructure.Domain</AssemblyName>
        <RootNamespace>Product.AWP.Infrastructure.Domain</RootNamespace>
        <LangVersion>default</LangVersion>
        <Product>Product</Product>
        <GenerateDocumentationFile>True</GenerateDocumentationFile>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Platform.AWP.Core.DataContracts"/>
        <PackageReference Include="Platform.Common"/>
        <PackageReference Include="Platform.WebApi.WebProxies.AuthTokenProviders.Oidc" />
        <PackageReference Include="Product.Sdk.Core"/>
    </ItemGroup>

    <ItemGroup>
        <Compile Update="Properties\Resources.Designer.cs">
            <DesignTime>True</DesignTime>
            <AutoGen>True</AutoGen>
            <DependentUpon>Resources.resx</DependentUpon>
        </Compile>
        <EmbeddedResource Update="Properties\Resources.resx">
            <Generator>ResXFileCodeGenerator</Generator>
            <LastGenOutput>Resources.Designer.cs</LastGenOutput>
        </EmbeddedResource>
        <EmbeddedResource Update="Properties\Resources.en.resx">
            <DependentUpon>Resources.resx</DependentUpon>
        </EmbeddedResource>
        <EmbeddedResource Update="Properties\Resources.kk-KZ.resx">
            <DependentUpon>Resources.resx</DependentUpon>
        </EmbeddedResource>
    </ItemGroup>

</Project>
