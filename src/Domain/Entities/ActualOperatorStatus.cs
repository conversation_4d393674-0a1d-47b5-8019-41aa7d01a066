using Platform.Common;

namespace Product.AWP.Infrastructure.Domain.Entities;

/// <summary>
/// Информация о текущем статусе оператора
/// </summary>
[Obsolete("Этот класс - обсолетный, остался, потому что относительно долго его выпиливать. На самом деле эту информацию предоставляет класс OperatorStatusInfo")]
public class ActualOperatorStatus : IHasReadOnlyId
{
    /// <summary>
    /// Дата установки статуса
    /// </summary>
    public DateTimeOffset DateFrom { get; set; }

    /// <summary>
    /// Текущий статус валиден до
    /// </summary>
    public DateTimeOffset? ValidUntil { get; set; }

    /// <summary>
    /// Текущий статус
    /// </summary>
    public Guid CurrentStatusId { get; set; }

    /// <summary>
    /// Заказанный статус
    /// </summary>
    public Guid? PendingStatusId { get; set; }

    /// <summary>
    /// Описание изменения статуса
    /// </summary>
    public string ActualStatusSetCode { get; set; }

    /// <summary>
    /// Предыдущий статус
    /// </summary>
    public Guid? PreviousStatusId { get; set; }

    /// <summary>
    /// Предыдущий заказанный статус
    /// </summary>
    public Guid? PreviousPendingStatusId { get; set; }

    /// <summary>
    /// Оператор
    /// </summary>
    public Guid OperatorId { get; set; }

    /// <summary>
    /// Идентификатор рабочей сессии оператора
    /// </summary>
    public Guid? WorkSessionId { get; set; }

    public Guid Id => OperatorId;
}
