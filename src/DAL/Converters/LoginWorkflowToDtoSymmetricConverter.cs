using Platform.AWP.DataContracts.Infrastructure;
using LoginWorkflow = Product.AWP.Infrastructure.DAL.Entities.LoginWorkflow;

namespace Product.AWP.Infrastructure.DAL.Converters;

public sealed class LoginWorkflowToDtoSymmetricConverter : ISymmetricConverter<LoginWorkflow, Platform.AWP.DataContracts.Infrastructure.LoginWorkflow>
{
    private readonly ApplicationToDtoSymmetricConverter _applicationSymmetricConverter = new();

    public Platform.AWP.DataContracts.Infrastructure.LoginWorkflow ConvertEntityToDto(LoginWorkflow entity)
    {
        if (entity == null) return null;

        var result = new Platform.AWP.DataContracts.Infrastructure.LoginWorkflow
        {
            Application = _applicationSymmetricConverter.ConvertEntityToDto(entity.Application),
            Id = entity.Id,
            WorkflowDefinition = entity.WorkflowDefinition,
            ClientTypes = (AwpClientTypes) entity.ClientTypes
        };

        return result;
    }

    public void MergeChanges(Platform.AWP.DataContracts.Infrastructure.LoginWorkflow newValue, LoginWorkflow oldValue)
    {
        oldValue.Id = newValue.Id;
        oldValue.ApplicationId = newValue.Application.Id;
        oldValue.WorkflowDefinition = newValue.WorkflowDefinition;
        oldValue.ClientTypes = (short) newValue.ClientTypes;
    }
}
