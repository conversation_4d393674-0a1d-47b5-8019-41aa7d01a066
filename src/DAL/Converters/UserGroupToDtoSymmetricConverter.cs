using Product.AWP.Infrastructure.DAL.Entities;
using Identity = Platform.AWP.DataContracts.Infrastructure.Identity;

namespace Product.AWP.Infrastructure.DAL.Converters;

public sealed class UserGroupToDtoSymmetricConverter : ISymmetricConverter<UserGroup, Platform.AWP.DataContracts.Infrastructure.UserGroup>
{
    public Platform.AWP.DataContracts.Infrastructure.UserGroup ConvertEntityToDto(UserGroup entity)
    {
        if (entity == null)
        {
            return null;
        }

        var result = new Platform.AWP.DataContracts.Infrastructure.UserGroup
        {
            Id = entity.Id,
            Name = entity.Name,
            Description = entity.Description,
            Identities = entity.Identities?.Select(id => new Identity {Id = id.Id, Login = id.Login}).ToList()
        };

        return result;
    }

    public void MergeChanges(Platform.AWP.DataContracts.Infrastructure.UserGroup newValue, UserGroup oldValue)
    {
        oldValue.Id = newValue.Id;
        oldValue.Description = newValue.Description;
        oldValue.Name = newValue.Name;
    }

    public Platform.AWP.DataContracts.Infrastructure.UserGroupWithNoIdentities ConvertEntityToDtoWithNoIdentities(UserGroup entity)
    {
        if (entity == null)
        {
            return null;
        }

        var result = new Platform.AWP.DataContracts.Infrastructure.UserGroupWithNoIdentities
        {
            Id = entity.Id,
            Name = entity.Name,
            Description = entity.Description
        };

        return result;
    }
}
