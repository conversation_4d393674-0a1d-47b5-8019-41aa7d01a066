using System.Diagnostics;
using Platform.AWP.DataContracts.Infrastructure;
using Platform.AWP.DataContracts.Infrastructure.ShortcutActions;
using Platform.Common;
using Shortcut = Product.AWP.Infrastructure.DAL.Entities.Shortcut;

namespace Product.AWP.Infrastructure.DAL.Converters;

public sealed class ShortcutToDtoSymmetricConverter : ISymmetricConverter<Shortcut, Platform.AWP.DataContracts.Infrastructure.Shortcut>
{
    private readonly ApplicationToDtoSymmetricConverter _applicationSymmetricConverter = new();

    public Platform.AWP.DataContracts.Infrastructure.Shortcut ConvertEntityToDto(Shortcut entity)
    {
        if (entity == null) return null;

        var result = new Platform.AWP.DataContracts.Infrastructure.Shortcut
        {
            Action = GetDeserializeAction((ShortcutType) entity.Type, entity.Action),
            Application = _applicationSymmetricConverter.ConvertEntityToDto(entity.Application),
            Disabled = entity.Disabled,
            Description = entity.Description,
            Id = entity.Id,
            Name = entity.Name,
            SortOrder = entity.SortOrder,
            Type = (ShortcutType) entity.Type
        };

        return result;
    }

    public void MergeChanges(Platform.AWP.DataContracts.Infrastructure.Shortcut newValue, Shortcut oldValue)
    {
        oldValue.Id = newValue.Id;
        oldValue.Action = GetAction(newValue);
        oldValue.Description = newValue.Description;
        oldValue.ApplicationId = newValue.Application.IfNotNull(p => (Guid?) p.Id);
        oldValue.Disabled = newValue.Disabled;
        oldValue.Name = newValue.Name;
        oldValue.SortOrder = newValue.SortOrder;
        oldValue.Type = (int) newValue.Type;
    }

    private static ShortcutAction GetDeserializeAction(ShortcutType shortcutType, string action)
    {
        var type = GetType(shortcutType);

        if (type == null)
        {
            return null;
        }

        try
        {
            return SerializationHelper.XmlDeserialize(type, action) as ShortcutAction;
        }
        catch (Exception exc)
        {
            Debug.WriteLine(exc, "ShortcutActionValueDeserializer.Resolve");
            return null;
        }
    }

    private static string GetAction(ShortcutBase dataToAdd)
    {
        return dataToAdd.Action.XmlSerialize();
    }

    public static Type GetType(ShortcutType shortcutType)
    {
        switch (shortcutType)
        {
            case ShortcutType.CallAdapterFunction:
                return typeof(CallAdapterAction);
            case ShortcutType.OpenWebAppInNewWindow:
                return typeof(WebNewWindowAction);
            case ShortcutType.StartExternalApp:
                return typeof(ExternalAppAction);
            case ShortcutType.LaunchWorkflow:
                return typeof(LaunchWorkflowAction);
            case ShortcutType.LaunchAppAndLogin:
                return typeof(LaunchAppAction);
            case ShortcutType.CustomUserShortcut:
                return typeof(CustomUserShortcutAction);
            default:
                return null;
        }
    }
}
