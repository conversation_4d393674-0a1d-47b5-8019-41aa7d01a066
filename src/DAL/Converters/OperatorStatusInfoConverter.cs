using Platform.AWP.DataContracts.Infrastructure;

namespace Product.AWP.Infrastructure.DAL.Converters;

public class OperatorStatusInfoConverter : ISymmetricConverter<Entities.ActualOperatorStatus, OperatorStatusInfo>
{
    public OperatorStatusInfo ConvertEntityToDto(Entities.ActualOperatorStatus entity)
    {
        if (entity == null) return null;

        return new OperatorStatusInfo
        {
            OperatorId = entity.OperatorId,
            CurrentStatusSetTime = entity.DateFrom,
            CurrentStatusSetCode = entity.ActualStatusSetCode,
            CurrentStatusId = entity.ActualStatusId,
            PreviousStatusId = entity.PreviousStatusId,
            CurrentPendingStatusId = entity.ActualPendingStatusId,
            PreviousPendingStatusId = entity.PreviousPendingStatusId,
            ValidUntil = entity.ValidUntil
        };
    }

    public void MergeChanges(OperatorStatusInfo newValue, Entities.ActualOperatorStatus oldValue)
    {
        throw new NotImplementedException();
    }
}
