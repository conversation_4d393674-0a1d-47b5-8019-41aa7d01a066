using Platform.Common.Collections;
using Product.AWP.Infrastructure.DAL.Entities;

namespace Product.AWP.Infrastructure.DAL.Converters;

public sealed class OperatorConverter : ISymmetricConverter<Operator, Platform.AWP.DataContracts.Infrastructure.Operator>
{
    private readonly OperatorCustomAttributeConverter OperatorCustomAttributeConverter = new();

    public Platform.AWP.DataContracts.Infrastructure.Operator ConvertEntityToDto(Operator entity)
    {
        if (entity == null) return null;

        return new Platform.AWP.DataContracts.Infrastructure.Operator
        {
            Id = entity.Id,
            UserName = entity.UserName,
            FirstName = entity.FirstName,
            LastName = entity.LastName,
            MiddleName = entity.MiddleName,
            Nickname = entity.Nickname,
            ActiveDirectoryId = entity.ActiveDirectoryId,
            OperatorGroupsIds = entity.OperatorGroupLinks.EmptyIfNull().Select(x => x.OperatorGroupId).ToList(),
            CustomAttributes = entity.CustomAttributes.EmptyIfNull().Select(p => OperatorCustomAttributeConverter.ConvertEntityToDto(p)).ToList()
        };
    }

    public void MergeChanges(Platform.AWP.DataContracts.Infrastructure.Operator newValue, Operator oldValue)
    {
        oldValue.Id = newValue.Id;
        oldValue.UserName = newValue.UserName;
        oldValue.FirstName = newValue.FirstName ?? string.Empty;
        oldValue.LastName = newValue.LastName ?? string.Empty;
        oldValue.MiddleName = newValue.MiddleName ?? string.Empty;
        oldValue.Nickname = newValue.Nickname ?? string.Empty;
        oldValue.ActiveDirectoryId = newValue.ActiveDirectoryId;


        var custAttrsConverter = new OperatorCustomAttributeConverter();
        var caToDelete = new List<OperatorCustomAttribute>();

        if (oldValue.CustomAttributes == null)
        {
            oldValue.CustomAttributes = new List<OperatorCustomAttribute>();
        }

        foreach (var existingCustomAttribute in oldValue.CustomAttributes)
        {
            var updatedCaInDc = newValue.CustomAttributes.EmptyIfNull().FirstOrDefault(ca => ca.Code == existingCustomAttribute.Code);
            if (updatedCaInDc != null)
            {
                //	атрибут есть в новом значении - нужно обновить
                custAttrsConverter.MergeChanges(updatedCaInDc, existingCustomAttribute);
            }
            else
            {
                //	атрибута нет в новом значении - нужно удалить
                caToDelete.Add(existingCustomAttribute);
            }
        }

        foreach (var attrToDelete in caToDelete)
        {
            oldValue.CustomAttributes.Remove(attrToDelete);
        }

        foreach (var newCaInDc in newValue.CustomAttributes.EmptyIfNull())
        {
            var existingCaInOldVal = oldValue.CustomAttributes.FirstOrDefault(ca => ca.Code == newCaInDc.Code);
            //обновление было выше, тут только добавляем
            if (existingCaInOldVal == null)
            {
                var newCaInEnt = new OperatorCustomAttribute();
                custAttrsConverter.MergeChanges(newCaInDc, newCaInEnt);
                oldValue.CustomAttributes.Add(newCaInEnt);
            }
        }
    }
}
