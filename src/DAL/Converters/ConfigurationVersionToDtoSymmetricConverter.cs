using Product.AWP.Infrastructure.DAL.Entities;

namespace Product.AWP.Infrastructure.DAL.Converters;

public class ConfigurationVersionToDtoSymmetricConverter : ISymmetricConverter<ConfigurationVersion, Platform.AWP.DataContracts.Infrastructure.ConfigurationVersion>
{
    public Platform.AWP.DataContracts.Infrastructure.ConfigurationVersion ConvertEntityToDto(ConfigurationVersion from)
    {
        return new Platform.AWP.DataContracts.Infrastructure.ConfigurationVersion
        {
            Id = from.Id,
            Name = from.Name,
            VersionMarker = from.VersionMarker
        };
    }

    public void MergeChanges(Platform.AWP.DataContracts.Infrastructure.ConfigurationVersion newValue, ConfigurationVersion oldValue)
    {
        oldValue.Id = newValue.Id;
        oldValue.Name = newValue.Name;
        oldValue.VersionMarker = newValue.VersionMarker;
    }
}
