using Product.AWP.Infrastructure.DAL.Entities;

namespace Product.AWP.Infrastructure.DAL.Converters;

public sealed class ConfigurationToInfrastructureConfigurationDtoSymmetricConverter : ISymmetricConverter<Configuration, Platform.AWP.DataContracts.Infrastructure.Configuration>
{
    public Platform.AWP.DataContracts.Infrastructure.Configuration ConvertEntityToDto(Configuration entity)
    {
        if (entity == null)
            return null;

        var result = new Platform.AWP.DataContracts.Infrastructure.Configuration
        {
            Id = entity.Id,
            Name = entity.Name,
            Value = entity.Value,
            Description = entity.Description
        };

        return result;
    }

    public void MergeChanges(Platform.AWP.DataContracts.Infrastructure.Configuration newValue, Configuration oldValue)
    {
        oldValue.Id = newValue.Id;
        oldValue.Name = newValue.Name;
        oldValue.Value = newValue.Value;
        oldValue.Description = newValue.Description;
    }
}
