using Platform.Common;
using Product.AWP.Infrastructure.DAL.Entities;

namespace Product.AWP.Infrastructure.DAL.Converters;

public sealed class TypeInfoToDtoSymmetricConverter : ISymmetricConverter<TypeInfo, Platform.AWP.DataContracts.Infrastructure.TypeInfo>
{
    public Platform.AWP.DataContracts.Infrastructure.TypeInfo ConvertEntityToDto(TypeInfo entity)
    {
        return entity.IfNotNull(p => new Platform.AWP.DataContracts.Infrastructure.TypeInfo
        {
            Assembly = p.Assembly,
            TypeName = p.TypeName
        });
    }

    public void MergeChanges(Platform.AWP.DataContracts.Infrastructure.TypeInfo newValue, TypeInfo oldValue)
    {
        oldValue.Assembly = newValue.Assembly;
        oldValue.TypeName = newValue.TypeName;
    }
}
