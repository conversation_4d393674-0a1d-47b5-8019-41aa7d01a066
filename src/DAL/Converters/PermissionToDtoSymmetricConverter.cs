using Product.AWP.Infrastructure.DAL.Entities;

namespace Product.AWP.Infrastructure.DAL.Converters;

public sealed class PermissionToDtoSymmetricConverter : ISymmetricConverter<Permission, Platform.AWP.DataContracts.Infrastructure.Permission>
{
    public Platform.AWP.DataContracts.Infrastructure.Permission ConvertEntityToDto(Permission entity)
    {
        if (entity == null) return null;

        var result = new Platform.AWP.DataContracts.Infrastructure.Permission
        {
            Description = entity.Description,
            Id = entity.Id,
            Name = entity.Name
        };

        return result;
    }

    public void MergeChanges(Platform.AWP.DataContracts.Infrastructure.Permission newValue, Permission oldValue)
    {
        oldValue.Id = newValue.Id;
        oldValue.Description = newValue.Description;
        oldValue.Name = newValue.Name;
    }
}
