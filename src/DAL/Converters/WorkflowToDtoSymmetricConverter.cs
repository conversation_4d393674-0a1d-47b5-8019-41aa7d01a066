using Platform.AWP.DataContracts.Infrastructure;
using Platform.Common;
using CrossWorkflow = Product.AWP.Infrastructure.DAL.Entities.CrossWorkflow;
using TypeInfo = Product.AWP.Infrastructure.DAL.Entities.TypeInfo;
using Workflow = Product.AWP.Infrastructure.DAL.Entities.Workflow;
using WorkflowGroup = Product.AWP.Infrastructure.DAL.Entities.WorkflowGroup;

namespace Product.AWP.Infrastructure.DAL.Converters;

public class WorkflowToDtoSymmetricConverter : ISymmetricConverter<Workflow, Platform.AWP.DataContracts.Infrastructure.Workflow>
{
    private readonly ISymmetricConverter<CrossWorkflow, Platform.AWP.DataContracts.Infrastructure.CrossWorkflow> _crossWorkflowSymmetricConverter;
    private readonly ISymmetricConverter<TypeInfo, Platform.AWP.DataContracts.Infrastructure.TypeInfo> _typeInfoSymmetricConverter;
    private readonly ISymmetricConverter<WorkflowGroup, Platform.AWP.DataContracts.Infrastructure.WorkflowGroup> _workflowGroupSymmetricConverter;

    public WorkflowToDtoSymmetricConverter()
    {
        _typeInfoSymmetricConverter = new TypeInfoToDtoSymmetricConverter();
        _workflowGroupSymmetricConverter = new WorkflowGroupToDtoSymmetricConverter();
        _crossWorkflowSymmetricConverter = new CrossWorkflowToDtoConverter();
    }

    public Platform.AWP.DataContracts.Infrastructure.Workflow ConvertEntityToDto(Workflow entity)
    {
        if (entity == null) return null;

        return new Platform.AWP.DataContracts.Infrastructure.Workflow
        {
            Code = entity.Code,
            Configuration = _typeInfoSymmetricConverter.ConvertEntityToDto(entity.Configuration),
            ConfigurationValue = entity.ConfigurationValue,
            CrossWorkflows = entity.CrossWorkflows.Select(_crossWorkflowSymmetricConverter.ConvertEntityToDto).ToArray(),
            Description = entity.Description,
            Event = _typeInfoSymmetricConverter.ConvertEntityToDto(entity.Event),
            EventThreadOption = entity.EventThreadOption,
            Group = entity.Group,
            Icon = entity.Icon,
            Id = entity.Id,
            InvokeAsync = entity.InvokeAsync,
            IsBusinessScenario = entity.IsBusinessScenario,
            IsService = entity.IsService,
            Name = entity.Name,
            ClientTypes = (AwpClientTypes) entity.ClientTypes,
            Order = entity.Order,
            SerializedValue = entity.SerializedValue,
            SummaryControl = _typeInfoSymmetricConverter.ConvertEntityToDto(entity.SummaryControl),
            UseConfiguration = entity.UseConfiguration,
            WorkflowGroup = _workflowGroupSymmetricConverter.ConvertEntityToDto(entity.WorkflowGroup)
        };
    }

    public void MergeChanges(Platform.AWP.DataContracts.Infrastructure.Workflow newValue, Workflow oldValue)
    {
        oldValue.Id = newValue.Id;
        oldValue.Group = newValue.Group;
        oldValue.Icon = newValue.Icon;
        oldValue.Description = newValue.Description;
        oldValue.SerializedValue = newValue.SerializedValue;
        oldValue.Code = newValue.Code;
        oldValue.EventThreadOption = newValue.EventThreadOption;
        oldValue.Name = newValue.Name;
        oldValue.ClientTypes = (short) newValue.ClientTypes;
        oldValue.InvokeAsync = newValue.InvokeAsync;
        oldValue.IsBusinessScenario = newValue.IsBusinessScenario;
        oldValue.IsService = newValue.IsService;
        oldValue.Order = newValue.Order;
        oldValue.ConfigurationValue = newValue.ConfigurationValue;
        oldValue.UseConfiguration = newValue.UseConfiguration;
        _typeInfoSymmetricConverter.MergeChanges(newValue.Event, oldValue.Event ?? (oldValue.Event = new TypeInfo()));
        _typeInfoSymmetricConverter.MergeChanges(newValue.SummaryControl, oldValue.SummaryControl ?? (oldValue.SummaryControl = new TypeInfo()));
        _typeInfoSymmetricConverter.MergeChanges(newValue.Configuration, oldValue.Configuration ?? (oldValue.Configuration = new TypeInfo()));
        oldValue.WorkflowGroupId = newValue.WorkflowGroup.IfNotNull(p => (Guid?) p.Id);
    }
}
