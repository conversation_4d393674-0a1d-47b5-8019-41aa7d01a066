using Platform.Common.Collections;
using Product.AWP.Infrastructure.DAL.Entities;
using Product.AWP.Infrastructure.DAL.Interfaces;

namespace Product.AWP.Infrastructure.DAL.Converters;

public class ServiceAreaConverter : ISymmetricConverter<ServiceArea, Platform.AWP.DataContracts.Infrastructure.ServiceArea>
{
    private IInfrastructureDataModel _objectContext;

    public Platform.AWP.DataContracts.Infrastructure.ServiceArea ConvertEntityToDto(ServiceArea entity)
    {
        return ConvertEntityToDto(entity, true, true, true);
    }

    public void MergeChanges(Platform.AWP.DataContracts.Infrastructure.ServiceArea newValue, ServiceArea oldValue)
    {
        oldValue.Id = newValue.Id;
        oldValue.ParentId = newValue.ParentServiceAreaId;
        oldValue.Name = newValue.Name;
        oldValue.ServiceAreaTypeId = newValue.ServiceAreaTypeId;
        oldValue.UtcOffset = newValue.UtcOffset;
        oldValue.Code = newValue.Code;
        oldValue.Id = newValue.Id;

        var custAttrsConverter = new CustomAttributeToDtoSymmetricConverter();
        var caToDelete = new List<CustomAttribute>();
        if (oldValue.CustomAttributes == null)
        {
            oldValue.CustomAttributes = new List<CustomAttribute>();
        }

        foreach (var existingCustomAttribute in oldValue.CustomAttributes)
        {
            var updatedCaInDc = newValue.CustomAttributes.EmptyIfNull().FirstOrDefault(ca => ca.Id == existingCustomAttribute.Id);
            if (updatedCaInDc != null)
            {
                //	атрибут есть в новом значении - нужно обновить
                custAttrsConverter.MergeChanges(updatedCaInDc, existingCustomAttribute);
            }
            else
            {
                //	атрибута нет в новом значении - нужно удалить
                caToDelete.Add(existingCustomAttribute);
            }
        }

        foreach (var attrToDelete in caToDelete)
        {
            oldValue.CustomAttributes.Remove(attrToDelete);
        }

        foreach (var newCaInDc in newValue.CustomAttributes.EmptyIfNull())
        {
            var existingCaInOldVal = oldValue.CustomAttributes.FirstOrDefault(ca => ca.Id == newCaInDc.Id);
            if (existingCaInOldVal != null)
            {
                //	этот случай - выше обработан уже, просто с другой стороны нашли этот кейс
            }
            else
            {
                var newCaInEnt = new CustomAttribute();
                custAttrsConverter.MergeChanges(newCaInDc, newCaInEnt);
                newCaInEnt.ServiceAreaId = newValue.Id;
                oldValue.CustomAttributes.Add(newCaInEnt);
                _objectContext.Add(newCaInEnt);
            }
        }
    }

    public void SetDataContext(IInfrastructureDataModel objectContext)
    {
        _objectContext = objectContext;
    }

    public Platform.AWP.DataContracts.Infrastructure.ServiceArea ConvertEntityToDto(ServiceArea entity, bool mapChildren, bool mapCustomAttributes, bool mapUserGroups)
    {
        if (entity == null) return null;

        var result = new Platform.AWP.DataContracts.Infrastructure.ServiceArea
        {
            Id = entity.Id,
            ParentServiceAreaId = entity.ParentId,
            Name = entity.Name,
            ServiceAreaTypeId = entity.ServiceAreaTypeId,
            UtcOffset = entity.UtcOffset,
            Code = entity.Code
        };
        if (mapChildren)
        {
            result.ChildrenAreas = entity.Children.EmptyIfNull().Select(x => new Tuple<Guid, string>(x.Id, x.Name)).ToList();
        }

        if (mapCustomAttributes)
        {
            var custAttrsConverter = new CustomAttributeToDtoSymmetricConverter();
            result.CustomAttributes = entity.CustomAttributes.Select(custAttrsConverter.ConvertEntityToDto).ToList();
        }

        if (mapUserGroups)
        {
            var userGroupConverter = new UserGroupToDtoSymmetricConverter();
            result.UserGroups = entity.UserGroups.EmptyIfNull().Select(userGroupConverter.ConvertEntityToDtoWithNoIdentities).ToArray();
        }

        return result;
    }
}
