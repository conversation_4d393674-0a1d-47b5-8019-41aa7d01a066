using Platform.Common.Collections;
using Product.AWP.Infrastructure.DAL.Entities;
using Product.AWP.Infrastructure.DAL.Interfaces;

namespace Product.AWP.Infrastructure.DAL.Converters;

public sealed class OperatorStatusConverter : ISymmetricConverter<OperatorStatus, Platform.AWP.DataContracts.Infrastructure.OperatorStatus>
{
    private IInfrastructureDataModel _dataModel;

    public Platform.AWP.DataContracts.Infrastructure.OperatorStatus ConvertEntityToDto(OperatorStatus entity)
    {
        if (entity == null) return null;

        var customAttributesConverter = new CustomAttributeToDtoSymmetricConverter();
        return new Platform.AWP.DataContracts.Infrastructure.OperatorStatus
        {
            Code = entity.Code,
            Id = entity.Id,
            IsStarting = entity.IsStarting,
            IsTerminating = entity.IsTerminating,
            Name = entity.Name,
            ValidityDuration = entity.ValidityDuration,
            CustomAttributes = entity.CustomAttributes.Select(customAttributesConverter.ConvertEntityToDto).ToList(),
            StatusTransitions = entity.StatusTransitionsFromHere.Select(s => s.OperatorStatusToId).Distinct().ToList() // TODO: Тут просто обновить контракт
        };
    }

    public void MergeChanges(Platform.AWP.DataContracts.Infrastructure.OperatorStatus newValue, OperatorStatus oldValue)
    {
        MergeChanges(newValue, oldValue, _dataModel);
    }

    public void MergeChanges(Platform.AWP.DataContracts.Infrastructure.OperatorStatus newValue, OperatorStatus oldValue, IInfrastructureDataModel dataModel)
    {
        oldValue.Id = newValue.Id;
        oldValue.Code = newValue.Code;
        oldValue.IsStarting = newValue.IsStarting;
        oldValue.IsTerminating = newValue.IsTerminating;
        oldValue.Name = newValue.Name;
        oldValue.ValidityDuration = newValue.ValidityDuration;

        var custAttrsConverter = new CustomAttributeToDtoSymmetricConverter();
        var caToDelete = new List<CustomAttribute>();
        if (oldValue.CustomAttributes == null)
        {
            oldValue.CustomAttributes = new List<CustomAttribute>();
        }

        foreach (var existingCustomAttribute in oldValue.CustomAttributes)
        {
            var updatedCaInDc = newValue.CustomAttributes.EmptyIfNull().FirstOrDefault(ca => ca.Id == existingCustomAttribute.Id);
            if (updatedCaInDc != null)
            {
                //	атрибут есть в новом значении - нужно обновить
                custAttrsConverter.MergeChanges(updatedCaInDc, existingCustomAttribute);
            }
            else
            {
                //	атрибута нет в новом значении - нужно удалить
                caToDelete.Add(existingCustomAttribute);
            }
        }

        foreach (var attrToDelete in caToDelete)
        {
            oldValue.CustomAttributes.Remove(attrToDelete);
        }

        foreach (var newCaInDc in newValue.CustomAttributes.EmptyIfNull())
        {
            var existingCaInOldVal = oldValue.CustomAttributes.FirstOrDefault(ca => ca.Id == newCaInDc.Id);
            if (existingCaInOldVal != null)
            {
                //	этот случай - выше обработан уже, просто с другой стороны нашли этот кейс
            }
            else
            {
                if (dataModel == null) throw new NotSupportedException("Adding Custom Attributes supported only when passing data model!");

                var newCaInEnt = new CustomAttribute();
                newCaInDc.OperatorStatusId = newValue.Id;
                custAttrsConverter.MergeChanges(newCaInDc, newCaInEnt);
                dataModel.Add(newCaInEnt);
                oldValue.CustomAttributes.Add(newCaInEnt);
            }
        }
    }

    public void SetDataModel(IInfrastructureDataModel dataModel)
    {
        _dataModel = dataModel;
    }
}
