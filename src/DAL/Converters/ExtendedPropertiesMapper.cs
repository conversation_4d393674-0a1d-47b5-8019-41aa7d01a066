using Platform.AWP.DataContracts.Infrastructure;
using Platform.Common.Collections;
using Product.AWP.Infrastructure.DAL.Entities;
using Application = Platform.AWP.DataContracts.Infrastructure.Application;
using ApplicationInitialization = Platform.AWP.DataContracts.Infrastructure.ApplicationInitialization;
using Configuration = Platform.AWP.DataContracts.Infrastructure.Configuration;
using ControlApplicationInitialization = Platform.AWP.DataContracts.Infrastructure.ApplicationInitializations.ControlApplicationInitialization;
using ExpanderSettings = Platform.AWP.DataContracts.Infrastructure.ExpanderSettings;
using ExternalApplicationInfo = Platform.AWP.DataContracts.Infrastructure.ExternalApplicationInfo;
using ExternalApplicationInitialization = Platform.AWP.DataContracts.Infrastructure.ApplicationInitializations.ExternalApplicationInitialization;
using Shortcut = Platform.AWP.DataContracts.Infrastructure.Shortcut;
using SizeSettings = Platform.AWP.DataContracts.Infrastructure.SizeSettings;
using TypeInfo = Platform.AWP.DataContracts.Infrastructure.TypeInfo;
using WebApplicationInitialization = Platform.AWP.DataContracts.Infrastructure.ApplicationInitializations.WebApplicationInitialization;

namespace Product.AWP.Infrastructure.DAL.Converters;

public static class ExtendedPropertiesMapper
{
    public static string FillProperties(List<ServiceAreaConfiguration> serviceAreaConfigurations, Guid configurationId)
    {
        if (serviceAreaConfigurations == null)
        {
            return null;
        }

        var saConfigsForItem = GetServiceAreaConfigurations(serviceAreaConfigurations, configurationId: configurationId);
        string result = null;
        FillPropertyByOverridenValueIfExists(
            saConfigsForItem,
            nameof(Configuration.Value),
            x => result = x);
        return result;
    }

    public static void FillProperties(List<ServiceAreaConfiguration> serviceAreaConfigurations, IEnumerable<Application> items)
    {
        if (serviceAreaConfigurations == null)
        {
            return;
        }

        foreach (var application in items.EmptyIfNull())
        {
            var saConfigsForItem = GetServiceAreaConfigurations(serviceAreaConfigurations, application.Id);

            FillPropertyByOverridenValueIfExists(
                saConfigsForItem,
                nameof(Application.ManualStart),
                x => application.ManualStart = x);

            FillPropertyByOverridenValueIfExists(
                saConfigsForItem,
                nameof(Application.IsService),
                x => application.IsService = x);

            FillPropertyByOverridenValueIfExists(
                saConfigsForItem,
                nameof(Application.SortOrder),
                x => application.SortOrder = x);

            FillPropertyByOverridenValueIfExists(
                saConfigsForItem,
                nameof(Application.LaunchOrder),
                x => application.LaunchOrder = x);

            FillPropertyByOverridenValueIfExists(
                saConfigsForItem,
                nameof(Application.EnableAutoSignOn),
                x => application.EnableAutoSignOn = x);

            FillPropertyByOverridenValueIfExists(
                saConfigsForItem,
                nameof(Application.Disabled),
                x => application.Disabled = x);

            FillPropertyByOverridenValueIfExists(
                saConfigsForItem,
                nameof(Application.CredentialsConfigurationName),
                x => application.CredentialsConfigurationName = x);

            FillProperties(saConfigsForItem, application.Initialization, nameof(Application.Initialization));
        }
    }

    public static void FillProperties(List<ServiceAreaConfiguration> serviceAreaConfigurations, IEnumerable<ModuleDescription> modules)
    {
        if (serviceAreaConfigurations == null)
        {
            return;
        }

        foreach (var module in modules.EmptyIfNull())
        {
            var saConfigsForItem = GetServiceAreaConfigurations(serviceAreaConfigurations, moduleId: module.Id);

            FillPropertyByOverridenValueIfExists(
                saConfigsForItem,
                nameof(ModuleDescription.Initialization),
                x => module.Initialization = x);

            FillPropertyByOverridenValueIfExists(
                saConfigsForItem,
                nameof(ModuleDescription.IsOptional),
                x => module.IsOptional = x);

            FillPropertyByOverridenValueIfExists(
                saConfigsForItem,
                nameof(ModuleDescription.IsDisabled),
                x => module.IsDisabled = x);
        }
    }

    public static void FillProperties(List<ServiceAreaConfiguration> serviceAreaConfigurations, IEnumerable<Shortcut> shortcuts)
    {
        if (serviceAreaConfigurations == null)
        {
            return;
        }

        foreach (var shortcut in shortcuts)
        {
            var saConfigsForItem = GetServiceAreaConfigurations(serviceAreaConfigurations, shortcurId: shortcut.Id);

            FillPropertyByOverridenValueIfExists(
                saConfigsForItem,
                nameof(ShortcutBase.SortOrder),
                x => shortcut.SortOrder = x);

            FillPropertyByOverridenValueIfExists(
                saConfigsForItem,
                nameof(Shortcut.Disabled),
                x => shortcut.Disabled = x);
        }
    }


    private static void FillProperties(List<ServiceAreaConfiguration> serviceAreaConfigurationsForItem, ApplicationInitialization appInit, string propertyCodePrefix)
    {
        FillProperties(
            serviceAreaConfigurationsForItem,
            appInit.Adapter,
            propertyCodePrefix + "." + nameof(ApplicationInitialization.Adapter));

        FillPropertyByOverridenValueIfExists(
            serviceAreaConfigurationsForItem,
            nameof(ApplicationInitialization.DisplayGroup),
            x => appInit.DisplayGroup = x,
            propertyCodePrefix);

        FillPropertyByOverridenValueIfExists(
            serviceAreaConfigurationsForItem,
            nameof(ApplicationInitialization.AdapterInitialization),
            x => appInit.AdapterInitialization = x,
            propertyCodePrefix);

        FillPropertyByOverridenValueIfExists(
            serviceAreaConfigurationsForItem,
            nameof(ApplicationInitialization.StartAsync),
            x => appInit.StartAsync = x,
            propertyCodePrefix);

        FillProperties(
            serviceAreaConfigurationsForItem,
            appInit.SizeSettings,
            propertyCodePrefix + "." + nameof(ApplicationInitialization.SizeSettings));

        FillProperties(
            serviceAreaConfigurationsForItem,
            appInit.ExpanderSettings,
            propertyCodePrefix + "." + nameof(ApplicationInitialization.ExpanderSettings));

        FillPropertyByOverridenValueIfExists(
            serviceAreaConfigurationsForItem,
            nameof(ApplicationInitialization.HideInTaskbar),
            x => appInit.HideInTaskbar = x,
            propertyCodePrefix);

        FillPropertyByOverridenValueIfExists(
            serviceAreaConfigurationsForItem,
            nameof(ApplicationInitialization.HideOnStartup),
            x => appInit.HideOnStartup = x,
            propertyCodePrefix);

        FillPropertiesForSpecificAppinit(
            serviceAreaConfigurationsForItem,
            appInit as ExternalApplicationInitialization,
            propertyCodePrefix);
        FillPropertiesForSpecificAppinit(
            serviceAreaConfigurationsForItem,
            appInit as ControlApplicationInitialization,
            propertyCodePrefix);
        FillPropertiesForSpecificAppinit(
            serviceAreaConfigurationsForItem,
            appInit as WebApplicationInitialization,
            propertyCodePrefix);
    }

    private static void FillPropertiesForSpecificAppinit(List<ServiceAreaConfiguration> serviceAreaConfigurationsForItem, ExternalApplicationInitialization externalAppInit, string propertyCodePrefix)
    {
        if (externalAppInit == null)
        {
            return;
        }

        FillProperties(
            serviceAreaConfigurationsForItem,
            externalAppInit.ExternalApplication,
            propertyCodePrefix + "." + nameof(ExternalApplicationInitialization.ExternalApplication));
        FillProperties(
            serviceAreaConfigurationsForItem,
            externalAppInit.UseTopLevelWindow,
            propertyCodePrefix + "." + nameof(ExternalApplicationInitialization.UseTopLevelWindow));
    }

    private static void FillPropertiesForSpecificAppinit(List<ServiceAreaConfiguration> serviceAreaConfigurationsForItem, ControlApplicationInitialization controlAppInit, string propertyCodePrefix)
    {
        if (controlAppInit == null)
        {
            return;
        }

        FillProperties(
            serviceAreaConfigurationsForItem,
            controlAppInit.Control,
            propertyCodePrefix + "." + nameof(ControlApplicationInitialization.Control));
        FillPropertyByOverridenValueIfExists(
            serviceAreaConfigurationsForItem,
            nameof(ControlApplicationInitialization.ControlInitialization),
            x => controlAppInit.ControlInitialization = x,
            propertyCodePrefix);
        FillPropertyByOverridenValueIfExists(
            serviceAreaConfigurationsForItem,
            nameof(ControlApplicationInitialization.ControlPerformsLogin),
            x => controlAppInit.ControlPerformsLogin = x,
            propertyCodePrefix);
    }

    private static void FillPropertiesForSpecificAppinit(List<ServiceAreaConfiguration> serviceAreaConfigurationsForItem, WebApplicationInitialization webAppInit, string propertyCodePrefix)
    {
        if (webAppInit == null)
        {
            return;
        }

        FillPropertyByOverridenValueIfExists(
            serviceAreaConfigurationsForItem,
            nameof(WebApplicationInitialization.StartUrl),
            x => webAppInit.StartUrl = x,
            propertyCodePrefix);
        FillPropertyByOverridenValueIfExists(
            serviceAreaConfigurationsForItem,
            nameof(WebApplicationInitialization.SuppressScriptErrors),
            x => webAppInit.SuppressScriptErrors = x,
            propertyCodePrefix);
    }

    private static void FillProperties(List<ServiceAreaConfiguration> serviceAreaConfigurationsForItem, TypeInfo typeInfo, string propertyCodePrefix)
    {
        FillPropertyByOverridenValueIfExists(
            serviceAreaConfigurationsForItem,
            nameof(TypeInfo.Assembly),
            x => typeInfo.Assembly = x,
            propertyCodePrefix);

        FillPropertyByOverridenValueIfExists(
            serviceAreaConfigurationsForItem,
            nameof(TypeInfo.TypeName),
            x => typeInfo.TypeName = x,
            propertyCodePrefix);
    }

    private static void FillProperties(List<ServiceAreaConfiguration> serviceAreaConfigurationsForItem, SizeSettings sizeSettings, string propertyCodePrefix)
    {
        FillPropertyByOverridenValueIfExists(
            serviceAreaConfigurationsForItem,
            nameof(SizeSettings.Width),
            new Action<double?>(x => sizeSettings.Width = x),
            propertyCodePrefix);

        FillPropertyByOverridenValueIfExists(
            serviceAreaConfigurationsForItem,
            nameof(SizeSettings.Height),
            new Action<double?>(x => sizeSettings.Height = x),
            propertyCodePrefix);

        FillPropertyByOverridenValueIfExists(
            serviceAreaConfigurationsForItem,
            nameof(SizeSettings.MinWidth),
            new Action<double?>(x => sizeSettings.MinWidth = x),
            propertyCodePrefix);

        FillPropertyByOverridenValueIfExists(
            serviceAreaConfigurationsForItem,
            nameof(SizeSettings.MinHeight),
            new Action<double?>(x => sizeSettings.MinHeight = x),
            propertyCodePrefix);

        FillPropertyByOverridenValueIfExists(
            serviceAreaConfigurationsForItem,
            nameof(SizeSettings.MaxWidth),
            new Action<double?>(x => sizeSettings.MaxWidth = x),
            propertyCodePrefix);

        FillPropertyByOverridenValueIfExists(
            serviceAreaConfigurationsForItem,
            nameof(SizeSettings.MaxHeight),
            new Action<double?>(x => sizeSettings.MaxHeight = x),
            propertyCodePrefix);
    }

    private static void FillProperties(List<ServiceAreaConfiguration> serviceAreaConfigurationsForItem, ExpanderSettings expanderSettings, string propertyCodePrefix)
    {
        FillPropertyByOverridenValueIfExists(
            serviceAreaConfigurationsForItem,
            nameof(ExpanderSettings.WrapInExpander),
            x => expanderSettings.WrapInExpander = x,
            propertyCodePrefix);

        FillPropertyByOverridenValueIfExists(
            serviceAreaConfigurationsForItem,
            nameof(ExpanderSettings.InitiallyExpanded),
            x => expanderSettings.InitiallyExpanded = x,
            propertyCodePrefix);

        FillPropertyByOverridenValueIfExists(
            serviceAreaConfigurationsForItem,
            nameof(ExpanderSettings.Header),
            x => expanderSettings.Header = x,
            propertyCodePrefix);
    }

    private static void FillProperties(List<ServiceAreaConfiguration> serviceAreaConfigurationsForItem, ExternalApplicationInfo externalAppInfo, string propertyCodePrefix)
    {
        FillPropertyByOverridenValueIfExists(
            serviceAreaConfigurationsForItem,
            nameof(ExternalApplicationInfo.Arguments),
            x => externalAppInfo.Arguments = x,
            propertyCodePrefix);

        FillPropertyByOverridenValueIfExists(
            serviceAreaConfigurationsForItem,
            nameof(ExternalApplicationInfo.Path),
            x => externalAppInfo.Path = x,
            propertyCodePrefix);

        FillPropertyByOverridenValueIfExists(
            serviceAreaConfigurationsForItem,
            nameof(ExternalApplicationInfo.WorkingDirectory),
            x => externalAppInfo.WorkingDirectory = x,
            propertyCodePrefix);
    }

    private static void FillProperties(List<ServiceAreaConfiguration> serviceAreaConfigurationsForItem, UseTopLevelWindow topLevelWindow, string propertyCodePrefix)
    {
        FillPropertyByOverridenValueIfExists(
            serviceAreaConfigurationsForItem,
            nameof(UseTopLevelWindow.Caption),
            x => topLevelWindow.Caption = x,
            propertyCodePrefix);
        FillPropertyByOverridenValueIfExists(
            serviceAreaConfigurationsForItem,
            nameof(UseTopLevelWindow.ClassName),
            x => topLevelWindow.ClassName = x,
            propertyCodePrefix);
        FillPropertyByOverridenValueIfExists(
            serviceAreaConfigurationsForItem,
            nameof(UseTopLevelWindow.LimitToProcess),
            x => topLevelWindow.LimitToProcess = x,
            propertyCodePrefix);
        FillPropertyByOverridenValueIfExists(
            serviceAreaConfigurationsForItem,
            nameof(UseTopLevelWindow.SearchWindowAfterLogin),
            x => topLevelWindow.SearchWindowAfterLogin = x,
            propertyCodePrefix);
        FillPropertyByOverridenValueIfExists(
            serviceAreaConfigurationsForItem,
            nameof(UseTopLevelWindow.UseProcessMainWindow),
            x => topLevelWindow.UseProcessMainWindow = x,
            propertyCodePrefix);
    }


    #region private: getting SACs

    private static List<ServiceAreaConfiguration> GetServiceAreaConfigurations(List<ServiceAreaConfiguration> serviceAreaConfigurations, Guid? applicationId = null, Guid? configurationId = null, Guid? moduleId = null, Guid? shortcurId = null, Guid? aesQueueId = null)
    {
        if (serviceAreaConfigurations == null || serviceAreaConfigurations.IsEmpty())
        {
            return null;
        }

        return serviceAreaConfigurations.Where(sac =>
            sac.ConfigurationId == configurationId &&
            sac.ApplicationId == applicationId &&
            sac.ModuleId == moduleId &&
            sac.ShortcutId == shortcurId &&
            sac.AesQueueId == aesQueueId
        ).ToList();
    }

    private static ServiceAreaConfiguration GetServiceAreaConfigurationForPropertyOfItem(List<ServiceAreaConfiguration> serviceAreaConfigurationsForItem, string propertyCode, string propertyCodePrefix = null)
    {
        if (serviceAreaConfigurationsForItem == null || serviceAreaConfigurationsForItem.IsEmpty())
        {
            return null;
        }

        var totalPropertyCode = propertyCode;
        if (!string.IsNullOrEmpty(propertyCodePrefix))
        {
            totalPropertyCode = propertyCodePrefix + "." + totalPropertyCode;
        }

        return serviceAreaConfigurationsForItem.FirstOrDefault(sac => string.Equals(sac.PropertyKey, totalPropertyCode, StringComparison.InvariantCultureIgnoreCase));
    }

    #endregion

    #region filling props generic

    private static void FillPropertyByOverridenValueIfExists(List<ServiceAreaConfiguration> serviceAreaConfigurationsForItem, string propertyCode, Action<string> propertySetter, string propertyCodePrefix = null)
    {
        var overridenValue = GetServiceAreaConfigurationForPropertyOfItem(serviceAreaConfigurationsForItem, propertyCode, propertyCodePrefix);
        if (overridenValue != null)
        {
            propertySetter(overridenValue.Value);
        }
    }

    private static void FillPropertyByOverridenValueIfExists(List<ServiceAreaConfiguration> serviceAreaConfigurationsForItem, string propertyCode, Action<bool> propertySetter, string propertyCodePrefix = null)
    {
        var overridenValue = GetServiceAreaConfigurationForPropertyOfItem(serviceAreaConfigurationsForItem, propertyCode, propertyCodePrefix);
        if (overridenValue != null && overridenValue.BoolValue.HasValue)
        {
            propertySetter(overridenValue.BoolValue.Value);
        }
    }

    private static void FillPropertyByOverridenValueIfExists(List<ServiceAreaConfiguration> serviceAreaConfigurationsForItem, string propertyCode, Action<int> propertySetter, string propertyCodePrefix = null)
    {
        var overridenValue = GetServiceAreaConfigurationForPropertyOfItem(serviceAreaConfigurationsForItem, propertyCode, propertyCodePrefix);
        if (overridenValue != null && overridenValue.IntValue.HasValue)
        {
            propertySetter(overridenValue.IntValue.Value);
        }
    }

    private static void FillPropertyByOverridenValueIfExists(List<ServiceAreaConfiguration> serviceAreaConfigurationsForItem, string propertyCode, Action<double?> propertySetter, string propertyCodePrefix = null)
    {
        var overridenValue = GetServiceAreaConfigurationForPropertyOfItem(serviceAreaConfigurationsForItem, propertyCode, propertyCodePrefix);
        if (overridenValue != null)
        {
            propertySetter(overridenValue.DoubleValue);
        }
    }

    #endregion
}
