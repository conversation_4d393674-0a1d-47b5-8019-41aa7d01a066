using Product.AWP.Infrastructure.DAL.Entities;

namespace Product.AWP.Infrastructure.DAL.Converters;

public sealed class LayoutToDtoSymmetricConverter : ISymmetricConverter<Layout, Platform.AWP.DataContracts.Infrastructure.Layout>
{
    public Platform.AWP.DataContracts.Infrastructure.Layout ConvertEntityToDto(Layout entity)
    {
        if (entity == null) return null;

        var result = new Platform.AWP.DataContracts.Infrastructure.Layout
        {
            Description = entity.Description,
            Id = entity.Id,
            Order = entity.Order,
            Name = entity.Name,
            Value = entity.Value
        };

        return result;
    }

    public void MergeChanges(Platform.AWP.DataContracts.Infrastructure.Layout newValue, Layout oldValue)
    {
        oldValue.Id = newValue.Id;
        oldValue.Description = newValue.Description;
        oldValue.Order = newValue.Order;
        oldValue.Name = newValue.Name;
        oldValue.Value = newValue.Value;
    }
}
