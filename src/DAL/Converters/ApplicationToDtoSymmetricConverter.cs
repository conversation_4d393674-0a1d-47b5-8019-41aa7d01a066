using Platform.AWP.DataContracts.Infrastructure;
using Product.AWP.Infrastructure.DAL.Entities;
using Application = Platform.AWP.DataContracts.Infrastructure.Application;
using ApplicationInitialization = Product.AWP.Infrastructure.DAL.Entities.ApplicationInitialization;

namespace Product.AWP.Infrastructure.DAL.Converters;

public sealed class ApplicationToDtoSymmetricConverter : ISymmetricConverter<Product.AWP.Infrastructure.DAL.Entities.Application, Application>
{
    private readonly ControlApplicationInitializationToDtoSymmetricConverter _controlApplicationInitializationSymmetricConverter = new();
    private readonly ExternalApplicationInitializationToDtoSymmetricConverter _externalApplicationInitializationSymmetricConverter = new();
    private readonly WebApplicationInitializationToDtoSymmetricConverter _webApplicationInitializationSymmetricConverter = new();

    public Application ConvertEntityToDto(Product.AWP.Infrastructure.DAL.Entities.Application entity)
    {
        if (entity == null)
        {
            return null;
        }

        var result = new Application
        {
            Id = entity.Id,
            Name = entity.Name,
            IsService = entity.IsService,
            SortOrder = entity.SortOrder,
            LaunchOrder = entity.LaunchOrder,
            ApplicationType = (ApplicationType) entity.Type,
            CredentialsConfigurationName = entity.CredentialsConfigurationName,
            Disabled = entity.Disabled ?? false,
            EnableAutoSignOn = entity.EnableAutoSignOn ?? false,
            ManualStart = entity.ManualStart,
            ToolbarImage = entity.ToolbarImage,
            ClientTypes = (AwpClientTypes) entity.ClientTypes
        };

        result.Initialization = ConvertApplicationInitialization(entity.ApplicationInitialization, result.ApplicationType);
        result.WebClientInitialization = ConvertWebClientApplicationInitialization(entity.WebClientApplicationInitialization);
        return result;
    }

    public void MergeChanges(Application newValue, Product.AWP.Infrastructure.DAL.Entities.Application oldValue)
    {
        oldValue.Id = newValue.Id;
        oldValue.Name = newValue.Name;
        oldValue.IsService = newValue.IsService;
        oldValue.SortOrder = newValue.SortOrder;
        oldValue.LaunchOrder = newValue.LaunchOrder;
        oldValue.CredentialsConfigurationName = newValue.CredentialsConfigurationName;
        oldValue.Disabled = newValue.Disabled;
        oldValue.EnableAutoSignOn = newValue.EnableAutoSignOn;
        oldValue.ManualStart = newValue.ManualStart;
        oldValue.Type = (int) newValue.ApplicationType;
        oldValue.ToolbarImage = newValue.ToolbarImage;
        oldValue.ClientTypes = (short) newValue.ClientTypes;

        switch (newValue.ApplicationType)
        {
            case ApplicationType.Control:
            {
                var newInit = newValue.Initialization as Platform.AWP.DataContracts.Infrastructure.ApplicationInitializations.ControlApplicationInitialization;
                if (!(oldValue.ApplicationInitialization is ControlApplicationInitialization))
                {
                    oldValue.ApplicationInitialization = new ControlApplicationInitialization {Id = oldValue.Id};
                }

                _controlApplicationInitializationSymmetricConverter.MergeChanges(newInit, oldValue.ApplicationInitialization as ControlApplicationInitialization);
                break;
            }

            case ApplicationType.External:
            case ApplicationType.ExternalJava:
            case ApplicationType.ExternalUIAutomation:
            {
                var newInit = newValue.Initialization as Platform.AWP.DataContracts.Infrastructure.ApplicationInitializations.ExternalApplicationInitialization;
                if (!(oldValue.ApplicationInitialization is ExternalApplicationInitialization))
                {
                    oldValue.ApplicationInitialization = new ExternalApplicationInitialization {Id = oldValue.Id};
                }

                _externalApplicationInitializationSymmetricConverter.MergeChanges(newInit, oldValue.ApplicationInitialization as ExternalApplicationInitialization);
                break;
            }
            case ApplicationType.Web:
            case ApplicationType.WebFirefox:
            case ApplicationType.WebFirefoxControl:
            case ApplicationType.WebChrome:
            case ApplicationType.WebUIAutomation:
            case ApplicationType.WebControl:
            case ApplicationType.WebChromeControl:
            {
                var newInit = newValue.Initialization as Platform.AWP.DataContracts.Infrastructure.ApplicationInitializations.WebApplicationInitialization;
                if (!(oldValue.ApplicationInitialization is WebApplicationInitialization))
                {
                    oldValue.ApplicationInitialization = new WebApplicationInitialization {Id = oldValue.Id};
                }

                _webApplicationInitializationSymmetricConverter.MergeChanges(newInit, oldValue.ApplicationInitialization as WebApplicationInitialization);
                break;
            }
        }

        oldValue.WebClientApplicationInitialization = newValue.WebClientInitialization != null
            ? new Entities.WebClientApplicationInitialization
            {
                Id = newValue.Id,
                ComponentName = newValue.WebClientInitialization.ComponentName,
                ComponentInit = newValue.WebClientInitialization.ComponentInit,
                DisplayGroup = newValue.WebClientInitialization.DisplayGroup
            }
            : null;
    }

    public ApplicationBaseInfo ConvertEntityToDtoBaseInfo(Product.AWP.Infrastructure.DAL.Entities.Application entity)
    {
        if (entity == null)
        {
            return null;
        }

        var result = new ApplicationBaseInfo
        {
            Id = entity.Id,
            Name = entity.Name
        };

        return result;
    }

    private Platform.AWP.DataContracts.Infrastructure.ApplicationInitialization ConvertApplicationInitialization(ApplicationInitialization appInitData, ApplicationType appType)
    {
        if (appInitData == null) return null;

        switch (appType)
        {
            case ApplicationType.Control:
                if (appInitData is not ControlApplicationInitialization cai) throw new InvalidOperationException($"appType='{appType}', but appInitData is NOT '{nameof(ControlApplicationInitialization)}' (Id='{appInitData.Id}')");
                return _controlApplicationInitializationSymmetricConverter.ConvertEntityToDto(cai);
            case ApplicationType.External:
            case ApplicationType.ExternalJava:
            case ApplicationType.ExternalUIAutomation:
                if (appInitData is not ExternalApplicationInitialization eai) throw new InvalidOperationException($"appType='{appType}', but appInitData is NOT '{nameof(ExternalApplicationInitialization)}' (Id='{appInitData.Id}')");
                return _externalApplicationInitializationSymmetricConverter.ConvertEntityToDto(eai);
            case ApplicationType.Web:
            case ApplicationType.WebFirefox:
            case ApplicationType.WebFirefoxControl:
            case ApplicationType.WebChrome:
            case ApplicationType.WebUIAutomation:
            case ApplicationType.WebControl:
            case ApplicationType.WebChromeControl:
                if (appInitData is not WebApplicationInitialization wai) throw new InvalidOperationException($"appType='{appType}', but appInitData is NOT '{nameof(WebApplicationInitialization)}' (Id='{appInitData.Id}')");
                return _webApplicationInitializationSymmetricConverter.ConvertEntityToDto(wai);
            default:
                throw new NotSupportedException($"application type='{appType}' not supported");
        }
    }

    private Platform.AWP.DataContracts.Infrastructure.WebClientApplicationInitialization ConvertWebClientApplicationInitialization(Entities.WebClientApplicationInitialization appInitData)
    {
        if (appInitData == null) return null;

        return new Platform.AWP.DataContracts.Infrastructure.WebClientApplicationInitialization
        {
            DisplayGroup = appInitData.DisplayGroup,
            ComponentName = appInitData.ComponentName,
            ComponentInit = appInitData.ComponentInit
        };
    }
}
