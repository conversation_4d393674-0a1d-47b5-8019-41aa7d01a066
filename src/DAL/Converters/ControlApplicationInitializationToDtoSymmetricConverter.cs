using Product.AWP.Infrastructure.DAL.Entities;

namespace Product.AWP.Infrastructure.DAL.Converters;

public sealed class ControlApplicationInitializationToDtoSymmetricConverter : ApplicationInitializationConverterBase, ISymmetricConverter<ControlApplicationInitialization, Platform.AWP.DataContracts.Infrastructure.ApplicationInitializations.ControlApplicationInitialization>
{
    public Platform.AWP.DataContracts.Infrastructure.ApplicationInitializations.ControlApplicationInitialization ConvertEntityToDto(ControlApplicationInitialization entity)
    {
        var result = new Platform.AWP.DataContracts.Infrastructure.ApplicationInitializations.ControlApplicationInitialization();

        ConvertBase(result, entity);

        result.Control = TypeInfoSymmetricConverter.ConvertEntityToDto(entity.Control);
        result.ControlInitialization = entity.ControlInitialization;
        result.ControlPerformsLogin = entity.ControlPerformsLogin;

        return result;
    }

    public void MergeChanges(Platform.AWP.DataContracts.Infrastructure.ApplicationInitializations.ControlApplicationInitialization newValue, ControlApplicationInitialization oldValue)
    {
        MergeBase(newValue, oldValue);
        oldValue.ControlPerformsLogin = newValue.ControlPerformsLogin;
        TypeInfoSymmetricConverter.MergeChanges(newValue.Control, oldValue.Control ?? (oldValue.Control = new TypeInfo()));
        oldValue.ControlInitialization = newValue.ControlInitialization;
    }
}
