using UserGroup = Product.AWP.Infrastructure.DAL.Entities.UserGroup;

namespace Product.AWP.Infrastructure.DAL.Converters;

public class ProfileConverter : ISymmetricConverter<UserGroup, Platform.AWP.DataContracts.Infrastructure.ProfileData>
{
    private readonly ServiceAreaConverter _serviceAreaConverter = new();
    private readonly UserRoleToDtoSymmetricConverter _userRoleToConverter = new();
    private readonly WorkplaceToDtoConverter _workplaceConverter = new();

    public Platform.AWP.DataContracts.Infrastructure.ProfileData ConvertEntityToDto(UserGroup entity)
    {
        return new Platform.AWP.DataContracts.Infrastructure.ProfileData
        {
            Id = entity.Id,
            Name = entity.Name,
            UserRoles = entity.UserRoles.ToArray().Select(x => _userRoleToConverter.ConvertEntityToDto(x, false, true, false)).ToArray(),
            ServiceAreas = entity.ServiceAreas.ToArray().Select(x => _serviceAreaConverter.ConvertEntityToDto(x, false, true, false)).ToArray(),
            Workplaces = entity.Workplaces.ToArray().Select(x => _workplaceConverter.ConvertEntityToDto(x, true, false)).ToArray()
        };
    }

    public void MergeChanges(Platform.AWP.DataContracts.Infrastructure.ProfileData newValue, UserGroup oldValue)
    {
        throw new NotImplementedException();
    }
}
