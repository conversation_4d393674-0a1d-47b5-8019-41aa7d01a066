using Product.AWP.Infrastructure.DAL.Entities;

namespace Product.AWP.Infrastructure.DAL.Converters;

public sealed class ConfigurationToDtoSymmetricConverter : ISymmetricConverter<Configuration, string>
{
    public string ConvertEntityToDto(Configuration entity)
    {
        return entity?.Value;
    }

    public void MergeChanges(string newValue, Configuration oldValue)
    {
        oldValue.Value = newValue;
    }
}
