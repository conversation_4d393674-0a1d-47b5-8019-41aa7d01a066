using Product.AWP.Infrastructure.DAL.Entities;

namespace Product.AWP.Infrastructure.DAL.Converters;

public class WorkflowGroupToDtoSymmetricConverter : ISymmetricConverter<WorkflowGroup, Platform.AWP.DataContracts.Infrastructure.WorkflowGroup>
{
    public Platform.AWP.DataContracts.Infrastructure.WorkflowGroup ConvertEntityToDto(WorkflowGroup entity)
    {
        if (entity == null) return null;

        var result = new Platform.AWP.DataContracts.Infrastructure.WorkflowGroup
        {
            Description = entity.Description,
            Id = entity.Id,
            Image = entity.Image,
            Name = entity.Name,
            Order = entity.Order
        };

        return result;
    }

    public void MergeChanges(Platform.AWP.DataContracts.Infrastructure.WorkflowGroup newValue, WorkflowGroup oldValue)
    {
        oldValue.Id = newValue.Id;
        oldValue.Name = newValue.Name;
        oldValue.Order = newValue.Order;
        oldValue.Description = newValue.Description;
        oldValue.Image = newValue.Image;
    }
}
