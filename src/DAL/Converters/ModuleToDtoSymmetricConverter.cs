using Platform.AWP.DataContracts.Infrastructure;
using Product.AWP.Infrastructure.DAL.Entities;

namespace Product.AWP.Infrastructure.DAL.Converters;

public sealed class ModuleToDtoSymmetricConverter : ISymmetricConverter<Module, ModuleDescription>
{
    public ModuleDescription ConvertEntityToDto(Module entity)
    {
        if (entity == null) return null;

        var result = new ModuleDescription
        {
            Description = entity.Description,
            Id = entity.Id,
            Initialization = entity.Initialization,
            IsDisabled = entity.IsDisabled,
            IsOptional = entity.IsOptional,
            Name = entity.Name,
            ClientTypes = (AwpClientTypes) entity.ClientTypes
        };

        return result;
    }

    public void MergeChanges(ModuleDescription newValue, Module oldValue)
    {
        oldValue.Id = newValue.Id;
        oldValue.Description = newValue.Description;
        oldValue.Initialization = newValue.Initialization;
        oldValue.IsDisabled = newValue.IsDisabled;
        oldValue.IsOptional = newValue.IsOptional;
        oldValue.Name = newValue.Name;
        oldValue.ClientTypes = (short) newValue.ClientTypes;
    }
}
