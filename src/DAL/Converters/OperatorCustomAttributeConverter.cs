using Product.AWP.Infrastructure.DAL.Entities;

namespace Product.AWP.Infrastructure.DAL.Converters;

public sealed class OperatorCustomAttributeConverter : ISymmetricConverter<OperatorCustomAttribute, Platform.AWP.DataContracts.Infrastructure.OperatorCustomAttribute>
{
    public Platform.AWP.DataContracts.Infrastructure.OperatorCustomAttribute ConvertEntityToDto(OperatorCustomAttribute entity)
    {
        if (entity == null)
            return null;

        return new Platform.AWP.DataContracts.Infrastructure.OperatorCustomAttribute
        {
            OperatorId = entity.OperatorId,
            Code = entity.Code,
            BoolValue = entity.BoolValue,
            DecimalValue = entity.DecimalValue,
            StringValue = entity.StringValue,
            LongValue = entity.LongValue,
            DateTimeValue = entity.DateTimeValue,
            GuidValue = entity.GuidValue,
            BinaryValue = entity.BinaryValue
        };
    }

    public void MergeChanges(Platform.AWP.DataContracts.Infrastructure.OperatorCustomAttribute newValue, OperatorCustomAttribute oldValue)
    {
        oldValue.OperatorId = newValue.OperatorId;
        oldValue.Code = newValue.Code;
        oldValue.BoolValue = newValue.BoolValue;
        oldValue.DecimalValue = newValue.DecimalValue;
        oldValue.StringValue = newValue.StringValue;
        oldValue.LongValue = newValue.LongValue;
        oldValue.DateTimeValue = newValue.DateTimeValue;
        oldValue.GuidValue = newValue.GuidValue;
        oldValue.BinaryValue = newValue.BinaryValue;
    }
}
