using Platform.Common.Collections;
using Product.AWP.Infrastructure.DAL.Entities;

namespace Product.AWP.Infrastructure.DAL.Converters;

public class OperatorGroupConverter : ISymmetricConverter<OperatorGroup, Platform.AWP.DataContracts.Infrastructure.OperatorGroup>
{
    private readonly OperatorGroupCustomAttributeConverter _attributeConverter = new();

    public Platform.AWP.DataContracts.Infrastructure.OperatorGroup ConvertEntityToDto(OperatorGroup entity)
    {
        if (entity == null) return null;

        return new Platform.AWP.DataContracts.Infrastructure.OperatorGroup
        {
            Id = entity.Id,
            Name = entity.Name,
            ParentId = entity.ParentId,
            OperatorsIds = entity.OperatorLinks.EmptyIfNull().Select(x => x.OperatorId).ToList(),
            CustomAttributes = entity.CustomAttributes.EmptyIfNull().Select(p => _attributeConverter.ConvertEntityToDto(p)).ToList()
        };
    }

    public void MergeChanges(Platform.AWP.DataContracts.Infrastructure.OperatorGroup newValue, OperatorGroup oldValue)
    {
        oldValue.Id = newValue.Id;
        oldValue.Name = newValue.Name;
        oldValue.ParentId = newValue.ParentId;

        var custAttrsConverter = new OperatorGroupCustomAttributeConverter();
        var caToDelete = new List<OperatorGroupCustomAttribute>();

        if (oldValue.CustomAttributes == null)
        {
            oldValue.CustomAttributes = new List<OperatorGroupCustomAttribute>();
        }

        foreach (var existingCustomAttribute in oldValue.CustomAttributes)
        {
            var updatedCaInDc = newValue.CustomAttributes.EmptyIfNull().FirstOrDefault(ca => ca.Code == existingCustomAttribute.Code);
            if (updatedCaInDc != null)
            {
                //	атрибут есть в новом значении - нужно обновить
                custAttrsConverter.MergeChanges(updatedCaInDc, existingCustomAttribute);
            }
            else
            {
                //	атрибута нет в новом значении - нужно удалить
                caToDelete.Add(existingCustomAttribute);
            }
        }

        foreach (var attrToDelete in caToDelete)
        {
            oldValue.CustomAttributes.Remove(attrToDelete);
        }

        foreach (var newCaInDc in newValue.CustomAttributes.EmptyIfNull())
        {
            var existingCaInOldVal = oldValue.CustomAttributes.FirstOrDefault(ca => ca.Code == newCaInDc.Code);
            //обновление было выше, тут только добавляем
            if (existingCaInOldVal == null)
            {
                var newCaInEnt = new OperatorGroupCustomAttribute();
                custAttrsConverter.MergeChanges(newCaInDc, newCaInEnt);
                oldValue.CustomAttributes.Add(newCaInEnt);
            }
        }
    }
}
