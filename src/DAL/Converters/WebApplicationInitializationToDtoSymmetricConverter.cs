using Platform.AWP.DataContracts.Infrastructure;
using Product.AWP.Infrastructure.DAL.Entities;
using WebAppProxySetting = Product.AWP.Infrastructure.DAL.Entities.WebAppProxySetting;

namespace Product.AWP.Infrastructure.DAL.Converters;

public sealed class WebApplicationInitializationToDtoSymmetricConverter : ApplicationInitializationConverterBase, ISymmetricConverter<WebApplicationInitialization, Platform.AWP.DataContracts.Infrastructure.ApplicationInitializations.WebApplicationInitialization>
{
    public Platform.AWP.DataContracts.Infrastructure.ApplicationInitializations.WebApplicationInitialization ConvertEntityToDto(WebApplicationInitialization entity)
    {
        var result = new Platform.AWP.DataContracts.Infrastructure.ApplicationInitializations.WebApplicationInitialization();
        ConvertBase(result, entity);

        result.StartUrl = entity.StartUrl;
        result.SuppressScriptErrors = entity.SuppressScriptErrors;
        if (entity.WebAppProxySettings?.Count > 0)
        {
            var proxySetting = entity.WebAppProxySettings.First();
            result.ProxyMapping = new WebAppProxyMapping
            {
                Id = proxySetting.Id,
                WebApplicationId = proxySetting.WebApplicationId,
                ProxyPort = proxySetting.ProxyPort
            };
            try
            {
                var uri = new Uri(entity.StartUrl);
                result.ProxyMapping.OriginalUrl = uri.GetLeftPart(UriPartial.Authority);
            }
            catch (Exception exc)
            {
                //	TODO: ANB: блин, и что делать? ))) логгера нет, записать инфу некуда, а дохнуть из-за этого неправильно как-то...
                result.ProxyMapping.OriginalUrl = entity.StartUrl;
            }
        }

        return result;
    }

    public void MergeChanges(Platform.AWP.DataContracts.Infrastructure.ApplicationInitializations.WebApplicationInitialization newValue, WebApplicationInitialization oldValue)
    {
        MergeBase(newValue, oldValue);
        oldValue.StartUrl = newValue.StartUrl;
        oldValue.SuppressScriptErrors = newValue.SuppressScriptErrors;
        if (newValue.ProxyMapping != null)
        {
            if (oldValue.WebAppProxySettings?.Count > 0)
            {
                var curSetting = oldValue.WebAppProxySettings.First();
                curSetting.OriginalUrl = null;
                curSetting.ProxyPort = newValue.ProxyMapping.ProxyPort;
            }
            else
            {
                var itemToAdd = new WebAppProxySetting
                {
                    WebApplicationId = oldValue.Id,
                    ProxyPort = newValue.ProxyMapping.ProxyPort
                };
                if (oldValue.WebAppProxySettings != null)
                {
                    oldValue.WebAppProxySettings.Add(itemToAdd);
                }
                else
                {
                    oldValue.WebAppProxySettings = new List<WebAppProxySetting> {itemToAdd};
                }
            }
        }
        else
        {
            if (oldValue.WebAppProxySettings?.Count > 0)
            {
                oldValue.WebAppProxySettings.Clear();
            }
        }
    }
}
