using Platform.Common.Collections;
using Product.AWP.Infrastructure.DAL.Entities;
using Product.AWP.Infrastructure.DAL.Interfaces;

namespace Product.AWP.Infrastructure.DAL.Converters;

public class WorkplaceToDtoConverter : ISymmetricConverter<Workplace, Platform.AWP.DataContracts.Infrastructure.Workplace>
{
    private readonly CustomAttributeToDtoSymmetricConverter _customAttributeConverter;
    private readonly UserGroupToDtoSymmetricConverter _userGroupConverter;

    private IInfrastructureDataModel _objectContext;

    public WorkplaceToDtoConverter()
    {
        _customAttributeConverter = new CustomAttributeToDtoSymmetricConverter();
        _userGroupConverter = new UserGroupToDtoSymmetricConverter();
    }

    public Platform.AWP.DataContracts.Infrastructure.Workplace ConvertEntityToDto(Workplace from)
    {
        return ConvertEntityToDto(from, true, true);
    }

    public void MergeChanges(Platform.AWP.DataContracts.Infrastructure.Workplace newValue, Workplace oldValue)
    {
        oldValue.Id = newValue.Id;
        oldValue.Name = newValue.Name;
        oldValue.Code = newValue.Code;
        oldValue.UtcOffset = newValue.UtcOffset;
        oldValue.ParentId = newValue.ParentId;
        oldValue.Id = newValue.Id;

        var custAttrsConverter = new CustomAttributeToDtoSymmetricConverter();
        var caToDelete = new List<CustomAttribute>();
        if (oldValue.CustomAttributes == null)
        {
            oldValue.CustomAttributes = new List<CustomAttribute>();
        }

        foreach (var existingCustomAttribute in oldValue.CustomAttributes)
        {
            var updatedCaInDc = newValue.CustomAttributes.EmptyIfNull().FirstOrDefault(ca => ca.Id == existingCustomAttribute.Id);
            if (updatedCaInDc != null)
            {
                //	атрибут есть в новом значении - нужно обновить
                custAttrsConverter.MergeChanges(updatedCaInDc, existingCustomAttribute);
            }
            else
            {
                //	атрибута нет в новом значении - нужно удалить
                caToDelete.Add(existingCustomAttribute);
            }
        }

        foreach (var attrToDelete in caToDelete)
        {
            oldValue.CustomAttributes.Remove(attrToDelete);
        }

        foreach (var newCaInDc in newValue.CustomAttributes.EmptyIfNull())
        {
            var existingCaInOldVal = oldValue.CustomAttributes.FirstOrDefault(ca => ca.Id == newCaInDc.Id);
            if (existingCaInOldVal != null)
            {
                //	этот случай - выше обработан уже, просто с другой стороны нашли этот кейс
            }
            else
            {
                var newCaInEnt = new CustomAttribute();
                custAttrsConverter.MergeChanges(newCaInDc, newCaInEnt);
                newCaInDc.WorkplaceId = oldValue.Id;
                oldValue.CustomAttributes.Add(newCaInEnt);
                _objectContext.Add(newCaInEnt);
            }
        }
    }

    public void SetDataContext(IInfrastructureDataModel objectContext)
    {
        _objectContext = objectContext;
    }

    public Platform.AWP.DataContracts.Infrastructure.Workplace ConvertEntityToDto(Workplace from, bool mapCustomAttributes, bool mapUserGroups)
    {
        if (from == null) return null;

        var result = new Platform.AWP.DataContracts.Infrastructure.Workplace
        {
            Id = from.Id,
            Name = from.Name,
            Code = from.Code,
            UtcOffset = from.UtcOffset,
            ParentId = from.ParentId
        };
        if (mapCustomAttributes)
        {
            result.CustomAttributes = from.CustomAttributes.Select(_customAttributeConverter.ConvertEntityToDto).ToList();
        }

        if (mapUserGroups)
        {
            result.UserGroups = from.UserGroups.Select(_userGroupConverter.ConvertEntityToDtoWithNoIdentities).ToArray();
        }

        return result;
    }
}
