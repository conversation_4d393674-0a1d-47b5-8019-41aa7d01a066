using Platform.AWP.DataContracts.Infrastructure;
using Product.AWP.Infrastructure.DAL.Entities;
using ExternalApplicationInfo = Product.AWP.Infrastructure.DAL.Entities.ExternalApplicationInfo;

namespace Product.AWP.Infrastructure.DAL.Converters;

public sealed class ExternalApplicationInitializationToDtoSymmetricConverter : ApplicationInitializationConverterBase, ISymmetricConverter<ExternalApplicationInitialization, Platform.AWP.DataContracts.Infrastructure.ApplicationInitializations.ExternalApplicationInitialization>
{
    public Platform.AWP.DataContracts.Infrastructure.ApplicationInitializations.ExternalApplicationInitialization ConvertEntityToDto(ExternalApplicationInitialization entity)
    {
        if (entity == null) return null;

        var result = new Platform.AWP.DataContracts.Infrastructure.ApplicationInitializations.ExternalApplicationInitialization();

        ConvertBase(result, entity);

        result.AcquisitionTimeout = entity.AcquisitionTimeout.HasValue
            ? new TimeSpan(0, 0, 0, 0, entity.AcquisitionTimeout.Value)
            : null;
        result.ExternalApplication = entity.ExternalApplication == null
            ? null
            : new Platform.AWP.DataContracts.Infrastructure.ExternalApplicationInfo
            {
                Path = entity.ExternalApplication.Path,
                WorkingDirectory = entity.ExternalApplication.WorkingDirectory,
                Arguments = entity.ExternalApplication.Arguments
            };
        result.UseTopLevelWindow = entity.UseTopLevelWindow == null
            ? null
            : new UseTopLevelWindow
            {
                UseProcessMainWindow = entity.UseTopLevelWindow.UseProcessMainWindow,
                Caption = entity.UseTopLevelWindow.Caption,
                ClassName = entity.UseTopLevelWindow.ClassName,
                LimitToProcess = entity.UseTopLevelWindow.LimitToProcess,
                SearchWindowAfterLogin = entity.UseTopLevelWindow.SearchWindowAfterLogin
            };

        return result;
    }

    public void MergeChanges(Platform.AWP.DataContracts.Infrastructure.ApplicationInitializations.ExternalApplicationInitialization newValue, ExternalApplicationInitialization oldValue)
    {
        MergeBase(newValue, oldValue);
        oldValue.AcquisitionTimeout = newValue.AcquisitionTimeout == null
            ? null
            : (int) newValue.AcquisitionTimeout.Value.TotalMilliseconds;

        if (newValue.ExternalApplication == null)
        {
            oldValue.ExternalApplication = null;
        }
        else
        {
            if (oldValue.ExternalApplication == null)
            {
                oldValue.ExternalApplication = new ExternalApplicationInfo();
            }

            oldValue.ExternalApplication.Path = newValue.ExternalApplication.Path;
            oldValue.ExternalApplication.WorkingDirectory = newValue.ExternalApplication.WorkingDirectory;
            oldValue.ExternalApplication.Arguments = newValue.ExternalApplication.Arguments;
        }

        if (newValue.UseTopLevelWindow == null)
        {
            oldValue.UseTopLevelWindow = null;
        }
        else
        {
            if (oldValue.UseTopLevelWindow == null)
            {
                oldValue.UseTopLevelWindow = new UseTopLevelWindowInfo();
            }

            oldValue.UseTopLevelWindow.UseProcessMainWindow = newValue.UseTopLevelWindow.UseProcessMainWindow;
            oldValue.UseTopLevelWindow.Caption = newValue.UseTopLevelWindow.Caption;
            oldValue.UseTopLevelWindow.ClassName = newValue.UseTopLevelWindow.ClassName;
            oldValue.UseTopLevelWindow.LimitToProcess = newValue.UseTopLevelWindow.LimitToProcess;
            oldValue.UseTopLevelWindow.SearchWindowAfterLogin = newValue.UseTopLevelWindow.SearchWindowAfterLogin;
        }
    }
}
