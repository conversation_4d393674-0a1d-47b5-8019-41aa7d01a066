using Product.AWP.Infrastructure.Domain.Entities;

namespace Product.AWP.Infrastructure.DAL.Converters;

public sealed class ActualOperatorStatusConverter : ISymmetricConverter<Product.AWP.Infrastructure.DAL.Entities.ActualOperatorStatus, ActualOperatorStatus>
{
    public ActualOperatorStatus ConvertEntityToDto(Product.AWP.Infrastructure.DAL.Entities.ActualOperatorStatus entity)
    {
        if (entity == null) return null;

        return new ActualOperatorStatus
        {
            CurrentStatusId = entity.ActualStatusId,
            PreviousStatusId = entity.PreviousStatusId,
            PendingStatusId = entity.ActualPendingStatusId,
            PreviousPendingStatusId = entity.PreviousPendingStatusId,
            ActualStatusSetCode = entity.ActualStatusSetCode,
            OperatorId = entity.OperatorId,
            DateFrom = entity.DateFrom,
            ValidUntil = entity.ValidUntil,
            WorkSessionId = entity.WorkSessionId
        };
    }

    public void MergeChanges(ActualOperatorStatus newValue, Product.AWP.Infrastructure.DAL.Entities.ActualOperatorStatus oldValue)
    {
        oldValue.Id = newValue.Id;
        oldValue.ActualStatusId = newValue.CurrentStatusId;
        oldValue.PreviousStatusId = newValue.PreviousStatusId;
        oldValue.ActualPendingStatusId = newValue.PendingStatusId;
        oldValue.PreviousPendingStatusId = newValue.PreviousPendingStatusId;
        oldValue.ActualStatusSetCode = newValue.ActualStatusSetCode;
        oldValue.DateFrom = newValue.DateFrom;
        oldValue.ValidUntil = newValue.ValidUntil;
        oldValue.OperatorId = newValue.OperatorId;
        oldValue.WorkSessionId = newValue.WorkSessionId;
    }
}
