using Product.AWP.Infrastructure.DAL.Entities;

namespace Product.AWP.Infrastructure.DAL.Converters;

public abstract class ApplicationInitializationConverterBase
{
    private readonly ExpanderSettingsToDtoSymmetricConverter _expanderSettingsSymmetricConverter = new();
    private readonly ISymmetricConverter<SizeSettings, Platform.AWP.DataContracts.Infrastructure.SizeSettings> _sizeSettingsSymmetricConverter = new SizeSettingsToDtoSymmetricConverter();
    protected readonly TypeInfoToDtoSymmetricConverter TypeInfoSymmetricConverter = new();


    protected void ConvertBase(Platform.AWP.DataContracts.Infrastructure.ApplicationInitialization entityDto, ApplicationInitialization entity)
    {
        entityDto.DisplayGroup = entity.DisplayGroup;
        entityDto.StartAsync = entity.StartAsync;
        entityDto.AdapterInitialization = entity.AdapterInitialization;
        entityDto.Adapter = TypeInfoSymmetricConverter.ConvertEntityToDto(entity.Adapter);
        entityDto.SizeSettings = _sizeSettingsSymmetricConverter.ConvertEntityToDto(entity.SizeSettings);
        entityDto.ExpanderSettings = _expanderSettingsSymmetricConverter.ConvertEntityToDto(entity.ExpanderSettings);
        entityDto.HideInTaskbar = entity.HideInTaskbar;
        entityDto.HideOnStartup = entity.HideOnStartup;
    }

    protected void MergeBase(Platform.AWP.DataContracts.Infrastructure.ApplicationInitialization newValue, ApplicationInitialization oldValue)
    {
        oldValue.DisplayGroup = newValue.DisplayGroup;
        oldValue.StartAsync = newValue.StartAsync;
        oldValue.AdapterInitialization = newValue.AdapterInitialization;
        TypeInfoSymmetricConverter.MergeChanges(newValue.Adapter, oldValue.Adapter ?? (oldValue.Adapter = new TypeInfo()));
        _sizeSettingsSymmetricConverter.MergeChanges(newValue.SizeSettings, oldValue.SizeSettings ?? (oldValue.SizeSettings = new SizeSettings()));
        _expanderSettingsSymmetricConverter.MergeChanges(newValue.ExpanderSettings, oldValue.ExpanderSettings ?? (oldValue.ExpanderSettings = new ExpanderSettings()));
        oldValue.HideInTaskbar = newValue.HideInTaskbar;
        oldValue.HideOnStartup = newValue.HideOnStartup;
    }
}
