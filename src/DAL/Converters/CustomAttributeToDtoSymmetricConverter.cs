using Product.AWP.Infrastructure.DAL.Entities;

namespace Product.AWP.Infrastructure.DAL.Converters;

public class CustomAttributeToDtoSymmetricConverter : ISymmetricConverter<CustomAttribute, Platform.AWP.DataContracts.Infrastructure.CustomAttribute>
{
    public Platform.AWP.DataContracts.Infrastructure.CustomAttribute ConvertEntityToDto(CustomAttribute entity)
    {
        if (entity == null) return null;

        var result = new Platform.AWP.DataContracts.Infrastructure.CustomAttribute
        {
            Id = entity.Id,

            Name = entity.Name,
            Value = entity.Value,
            Image = entity.Image,
            OperatorStatusId = entity.OperatorStatusId,
            ServiceAreaId = entity.ServiceAreaId,
            UserRoleId = entity.UserRoleId,
            WorkplaceId = entity.WorkplaceId
        };

        return result;
    }

    public void MergeChanges(Platform.AWP.DataContracts.Infrastructure.CustomAttribute newValue, CustomAttribute oldValue)
    {
        oldValue.Id = newValue.Id;
        oldValue.Name = newValue.Name;
        oldValue.Value = newValue.Value;
        oldValue.Image = newValue.Image;
        oldValue.OperatorStatusId = newValue.OperatorStatusId;
        oldValue.ServiceAreaId = newValue.ServiceAreaId;
        oldValue.UserRoleId = newValue.UserRoleId;
        oldValue.WorkplaceId = newValue.WorkplaceId;
    }
}
