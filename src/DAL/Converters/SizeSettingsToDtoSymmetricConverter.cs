using Product.AWP.Infrastructure.DAL.Entities;

namespace Product.AWP.Infrastructure.DAL.Converters;

public sealed class SizeSettingsToDtoSymmetricConverter : ISymmetricConverter<SizeSettings, Platform.AWP.DataContracts.Infrastructure.SizeSettings>
{
    public Platform.AWP.DataContracts.Infrastructure.SizeSettings ConvertEntityToDto(SizeSettings entity)
    {
        if (entity == null) return new Platform.AWP.DataContracts.Infrastructure.SizeSettings();

        return new Platform.AWP.DataContracts.Infrastructure.SizeSettings
        {
            Width = entity.Width,
            Height = entity.Height,
            MinWidth = entity.MinWidth,
            MinHeight = entity.MinHeight,
            MaxWidth = entity.MaxWidth,
            MaxHeight = entity.MaxHeight
        };
    }

    public void MergeChanges(Platform.AWP.DataContracts.Infrastructure.SizeSettings newValue, SizeSettings oldValue)
    {
        oldValue.Width = newValue.Width;
        oldValue.Height = newValue.Height;
        oldValue.MinWidth = newValue.MinWidth;
        oldValue.MinHeight = newValue.MinHeight;
        oldValue.MaxWidth = newValue.MaxWidth;
        oldValue.MaxHeight = newValue.MaxHeight;
    }
}
