using Platform.AWP.DataContracts.Infrastructure;
using ExpanderSettings = Product.AWP.Infrastructure.DAL.Entities.ExpanderSettings;

namespace Product.AWP.Infrastructure.DAL.Converters;

public sealed class ExpanderSettingsToDtoSymmetricConverter : ISymmetricConverter<ExpanderSettings, Platform.AWP.DataContracts.Infrastructure.ExpanderSettings>
{
    public Platform.AWP.DataContracts.Infrastructure.ExpanderSettings ConvertEntityToDto(ExpanderSettings entity)
    {
        if (entity == null) return new Platform.AWP.DataContracts.Infrastructure.ExpanderSettings {WrapInExpander = false};

        return new Platform.AWP.DataContracts.Infrastructure.ExpanderSettings
        {
            WrapInExpander = entity.WrapInExpander,
            InitiallyExpanded = entity.InitiallyExpanded,
            ExpandDisplayMode = (ExpandDisplayMode) entity.ExpandDisplayMode,
            Header = entity.Header,
            ExpandDirection = (ExpandDirection) entity.ExpandDirection
        };
    }

    public void MergeChanges(Platform.AWP.DataContracts.Infrastructure.ExpanderSettings newValue, ExpanderSettings oldValue)
    {
        oldValue.WrapInExpander = newValue.WrapInExpander;
        oldValue.InitiallyExpanded = newValue.InitiallyExpanded;
        oldValue.ExpandDisplayMode = (int) newValue.ExpandDisplayMode;
        oldValue.Header = newValue.Header;
        oldValue.ExpandDirection = (int) newValue.ExpandDirection;
    }
}
