using Product.AWP.Infrastructure.DAL.Entities;

namespace Product.AWP.Infrastructure.DAL.Converters;

public sealed class OperatorGroupCustomAttributeConverter : ISymmetricConverter<OperatorGroupCustomAttribute, Platform.AWP.DataContracts.Infrastructure.OperatorGroupCustomAttribute>
{
    public Platform.AWP.DataContracts.Infrastructure.OperatorGroupCustomAttribute ConvertEntityToDto(OperatorGroupCustomAttribute entity)
    {
        if (entity == null) return null;

        return new Platform.AWP.DataContracts.Infrastructure.OperatorGroupCustomAttribute
        {
            OperatorGroupId = entity.OperatorGroupId,
            Code = entity.Code,
            BoolValue = entity.BoolValue,
            DecimalValue = entity.DecimalValue,
            StringValue = entity.StringValue,
            LongValue = entity.LongValue,
            DateTimeValue = entity.DateTimeValue,
            GuidValue = entity.GuidValue,
            BinaryValue = entity.BinaryValue
        };
    }

    public void MergeChanges(Platform.AWP.DataContracts.Infrastructure.OperatorGroupCustomAttribute newValue, OperatorGroupCustomAttribute oldValue)
    {
        oldValue.OperatorGroupId = newValue.OperatorGroupId;
        oldValue.Code = newValue.Code;
        oldValue.BoolValue = newValue.BoolValue;
        oldValue.DecimalValue = newValue.DecimalValue;
        oldValue.StringValue = newValue.StringValue;
        oldValue.LongValue = newValue.LongValue;
        oldValue.DateTimeValue = newValue.DateTimeValue;
        oldValue.GuidValue = newValue.GuidValue;
        oldValue.BinaryValue = newValue.BinaryValue;
    }
}
