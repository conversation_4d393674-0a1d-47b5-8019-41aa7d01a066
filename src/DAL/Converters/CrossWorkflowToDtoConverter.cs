using Product.AWP.Infrastructure.DAL.Entities;

namespace Product.AWP.Infrastructure.DAL.Converters;

public sealed class CrossWorkflowToDtoConverter : ISymmetricConverter<CrossWorkflow, Platform.AWP.DataContracts.Infrastructure.CrossWorkflow>
{
    public Platform.AWP.DataContracts.Infrastructure.CrossWorkflow ConvertEntityToDto(CrossWorkflow entity)
    {
        return new Platform.AWP.DataContracts.Infrastructure.CrossWorkflow
        {
            Id = entity.TargetWorkflowId,
            Order = entity.Order,
            CrossWorkflowId = entity.TargetWorkflowId
        };
    }

    public void MergeChanges(Platform.AWP.DataContracts.Infrastructure.CrossWorkflow newValue, CrossWorkflow oldValue)
    {
        throw new NotImplementedException();
    }
}
