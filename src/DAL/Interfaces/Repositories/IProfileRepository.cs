using Platform.AWP.DataContracts.Infrastructure;

namespace Product.AWP.Infrastructure.DAL.Interfaces.Repositories;

public interface IProfileRepository
{
    Task<ProfileData[]> GetProfiles(Guid[] userGroupId);
    Task<UserRoleWithNoUserGroups[]> GetUserRolesWithNoUserGroups();
    Task<ServiceAreaWithNoUserGroups[]> GetServiceAreasWithNoUserGroups();
    Task<WorkplaceWithNoUserGroups[]> GetWorkplacesWithNoUserGroups();
}
