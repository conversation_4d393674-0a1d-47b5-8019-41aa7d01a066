using Platform.AWP.DataContracts.Infrastructure;

namespace Product.AWP.Infrastructure.DAL.Interfaces.Repositories;

public interface IShortcutsRepository
{
    Task<Shortcut[]> GetShortcutsForCurrentUser(Guid? serviceAreaId, Guid userRolesId);
    Task<UserShortcut[]> GetCurrentUserCustomShortcuts(string userName);
    Task AddCurrentUserCustomShortcut(UserShortcut shortcut, string userName);
    Task UpdateCurrentUserCustomShortcut(UserShortcut shortcut);
    Task RemoveCurrentUserCustomShortcut(Guid userShortcutId);
}
