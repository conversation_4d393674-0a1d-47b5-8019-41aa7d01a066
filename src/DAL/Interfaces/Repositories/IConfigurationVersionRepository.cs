namespace Product.AWP.Infrastructure.DAL.Interfaces.Repositories;

public interface IConfigurationVersionRepository
{
    /// <summary>
    ///     Updates version of specified key
    /// </summary>
    /// <param name="key">
    ///     The key.
    /// </param>
    /// <param name="useStartsWith">
    ///     Flag indicating whether to update all keys starting with specified string
    /// </param>
    Task UpdateVersion(string key, bool useStartsWith = false);
}
