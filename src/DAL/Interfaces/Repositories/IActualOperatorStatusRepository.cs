using Platform.AWP.DataContracts;
using Platform.AWP.DataContracts.Infrastructure;

namespace Product.AWP.Infrastructure.DAL.Interfaces.Repositories;

public interface IActualOperatorStatusRepository
{
    Task<OperatorStatusInfo[]> GetActualOperatorsStatuses(ActualOperatorStatusFilter actualOperatorStatusFilter);
    Task SetActualOperatorStatus(Guid operatorId, Guid currentStatusId, Guid? pendingStatusId, DateTimeOffset dateFrom, DateTimeOffset? tillDate, string actualStatusSetCode, Guid? workSessionId);
    Task<bool> ProlongateOperatorStatus(Guid operatorId, Guid actualStatusId, DateTimeOffset? tillDate);
}
