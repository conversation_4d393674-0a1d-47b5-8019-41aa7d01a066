using Platform.AWP.DataContracts.Infrastructure;

namespace Product.AWP.Infrastructure.DAL.Interfaces.Repositories;

public interface IConfigurationRepository
{
    Task<string> GetConfiguration(string configurationName, Guid? serviceAreaId);
    Task<Dictionary<string, string>> GetConfigurations(string[] configurationNames, Guid? serviceAreaId);
    Task<IList<ModuleDescription>> GetModulesForCurrentUser(Guid? serviceAreaId, Guid userRoleId, AwpClientTypes clientTypes);
    Task<IList<Application>> GetHostedApplications(Guid? serviceAreaId, Guid userRoleId, AwpClientTypes clientTypes);
    Task<LoginWorkflow[]> GetLoginWorkflowsForApplication(Guid applicationId, AwpClientTypes clientTypes);
    Task<Layout[]> GetLayouts(Guid userRoleId);
    Task<Workflow[]> GetWorkflows(Guid userRoleId, AwpClientTypes clientTypes);
    Task<IDictionary<string, Guid>> GetConfigurationsVersions();
}
