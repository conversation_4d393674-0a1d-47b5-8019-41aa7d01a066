using Platform.AWP.DataContracts.Infrastructure;

namespace Product.AWP.Infrastructure.DAL.Interfaces.Repositories;

public interface IPermissionRepository
{
    /// <summary>
    /// Gets list of permissions for current user
    /// </summary>
    Task<Permission[]> GetPermissionsForCurrentUser(Guid userRoleId);

    /// <summary>
    /// Checks if current user has permission.
    /// </summary>
    Task<bool> RequestPermission(string permission, Guid userRoleId);
}
