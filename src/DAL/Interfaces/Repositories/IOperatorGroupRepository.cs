using Platform.AWP.DataContracts.Infrastructure;

namespace Product.AWP.Infrastructure.DAL.Interfaces.Repositories;

public interface IOperatorGroupRepository
{
    Task<OperatorGroup[]> GetList();
    Task Add(OperatorGroup dataContract);
    Task Update(OperatorGroup dataContract);
    Task AddOrUpdateCustomAttributes(IList<OperatorGroupCustomAttribute> attributes);
    Task RemoveCustomAttribute(Guid operatorGroupId, IList<string> codes);
    Task<OperatorGroupCustomAttribute[]> GetCustomAttributes(IList<Guid> operatorGroupIds, IList<string> codes);
    Task AddOperatorToGroups(Guid operatorId, params Guid[] groupsIds);
    Task RemoveOperatorFromGroup(Guid operatorId, Guid operatorGroupId);
    Task<bool> IsOperatorInGroup(Guid operatorId, Guid operatorGroupId, bool searchInChilds = false);
    Task<Guid[]> GetOperatorGroupsIdsWithParentGroupsInНierarchy(Guid operatorId);
    Task<Guid[]> GetOperatorGroupIds(string[] groupNames);
}
