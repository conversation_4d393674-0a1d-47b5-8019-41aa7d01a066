namespace Product.AWP.Infrastructure.DAL.Interfaces.Repositories;

public interface IOperatorStatusAvailabilityRepository
{
    Task<Platform.AWP.DataContracts.Infrastructure.OperatorStatus[]> GetAvailableForUser(Guid userRoleId, Func<string, Task<bool>> currentUserIsInGroupPredicate);
    Task<Platform.AWP.DataContracts.Infrastructure.UserRole[]> GetAvailableMappings(Guid itemId);
    Task<Platform.AWP.DataContracts.Infrastructure.OperatorStatus[]> GetAvailableFrom(Guid statusId, Guid userRoleId, Func<string, Task<bool>> currentUserIsInGroupPredicate);
}
