using Platform.AWP.DataContracts.Infrastructure;

namespace Product.AWP.Infrastructure.DAL.Interfaces.Repositories;

public interface IOperatorDataRepository
{
    Task Add(Operator dataContract);
    Task<Operator[]> FindByUserNameContains(string userName);
    Task<Operator> FindByActiveDirectoryId(Guid activeDirectoryId);
    Task<Operator[]> FindByActiveDirectoryIds(Guid[] activeDirectoryIds);
    Task<Operator[]> GetOperatorsInGroups(IList<Guid> operatorGroupsIds);
    Task AddOrUpdateCustomAttributes(IList<OperatorCustomAttribute> attributes);
    Task RemoveCustomAttribute(Guid operatorId, IList<string> codes);
    Task<OperatorCustomAttribute[]> GetCustomAttributes(IList<Guid> operatorIds, IList<string> codes);
    Task<Operator[]> FindByStringCa(string caCode, string caValue);
    Task<Operator[]> FindByGuidCa(string caCode, Guid? caValue);
    Task<Operator[]> FindByLongCa(string caCode, long? caValue);
    Task<Operator[]> FindByDecimalCa(string caCode, decimal? caValue);
    Task<Operator[]> FindByBoolCa(string caCode, bool? caValue);
    Task<Operator[]> FindByDateTimeCa(string caCode, DateTime? caValue);
    Task RemoveCustomAttributes(Guid[] operatorsIds, string[] caCodes);
    Task<Operator> Get(Guid operatorId);
}
