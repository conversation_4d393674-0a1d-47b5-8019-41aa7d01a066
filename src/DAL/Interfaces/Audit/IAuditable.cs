namespace Product.AWP.Infrastructure.DAL.Interfaces.Audit;

public interface IAuditable
{
    /// <summary>
    /// Устанавливает данные операции, в рамках которой выполняются последующие изменения
    /// </summary>
    /// <param name="dns">Адрес машины</param>
    /// <param name="login">Логин того кто инициализировал выполнение операции</param>
    /// <param name="operationName">Наименование операции</param>
    void InitAuditData(string dns, string login, string operationName);

    void SetAuditor(IAuditor auditor);
}
