using Microsoft.EntityFrameworkCore;
using Product.AWP.Infrastructure.DAL.Entities.Audit;

namespace Product.AWP.Infrastructure.DAL.Interfaces.Audit;

public interface IAuditDbContext : IDisposable
{
    DbSet<Entities.Audit.Audit> Audits { get; }

    DbSet<ActualOperatorStatus> ActualOperatorStatuses { get; }
    DbSet<AdministrationObject> AdministrationObjects { get; }
    DbSet<AdministrationPermission> AdministrationPermissions { get; }
    DbSet<AesQueue> AesQueues { get; }
    DbSet<AesQueueUserRole> AesQueueUserRoles { get; }
    DbSet<Application> Applications { get; }
    DbSet<ApplicationUserRole> ApplicationUserRoles { get; }
    DbSet<ApplicationInitialization> ApplicationInitializations { get; }
    DbSet<ControlApplicationInitialization> ControlApplicationInitializations { get; }
    DbSet<ExternalApplicationInitialization> ExternalApplicationInitializations { get; }
    DbSet<WebApplicationInitialization> WebApplicationInitializations { get; }
    DbSet<BaseShortcut> BaseShortcuts { get; }
    DbSet<Configuration> Configurations { get; }
    DbSet<CrossWorkflow> CrossWorkflows { get; }
    DbSet<CustomAttribute> CustomAttributes { get; }
    DbSet<Layout> Layouts { get; }
    DbSet<LayoutUserRole> LayoutUserRoles { get; }
    DbSet<LoginWorkflow> LoginWorkflows { get; }
    DbSet<Module> Modules { get; }
    DbSet<ModuleUserRole> ModuleUserRoles { get; }
    DbSet<Operator> Operators { get; }
    DbSet<OperatorCustomAttribute> OperatorCustomAttributes { get; }
    DbSet<OperatorGroup> OperatorGroups { get; }
    DbSet<OperatorOperatorGroup> OperatorOperatorGroups { get; }
    DbSet<OperatorStatus> OperatorStatuses { get; }
    DbSet<OperatorStatusUserRole> OperatorStatuseUserRoles { get; }
    DbSet<Permission> Permissions { get; }
    DbSet<PermissionUserRole> PermissionUserRoles { get; }
    DbSet<ServiceArea> ServiceAreas { get; }
    DbSet<ServiceAreaConfiguration> ServiceAreaConfigurations { get; }
    DbSet<ServiceAreaType> ServiceAreaTypes { get; }
    DbSet<Shortcut> Shortcuts { get; }
    DbSet<ShortcutUserRole> ShortcutUserRoles { get; }
    DbSet<StatusTransition> StatusTransitions { get; }
    DbSet<UserGroup> UserGroups { get; }
    DbSet<Identity> Identities { get; }
    DbSet<UserGroupIdentity> UserGroupIdentities { get; }
    DbSet<UserGroupServiceArea> UserGroupServiceAreas { get; }
    DbSet<UserGroupUserRole> UserGroupUserRoles { get; }
    DbSet<UserGroupWorkplace> UserGroupWorkplaces { get; }
    DbSet<UserRole> UserRoles { get; }
    DbSet<UserShortcut> UserShortcuts { get; }
    DbSet<Workflow> Workflows { get; }
    DbSet<WorkflowUserRole> WorkflowUserRoles { get; }
    DbSet<WorkflowGroup> WorkflowGroups { get; }
    DbSet<Workplace> Workplaces { get; }
    DbSet<OperatorGroupCustomAttribute> OperatorGroupCustomAttributes { get; set; }

    Task<int> SaveChangesAsync();
}
