using Product.AWP.Infrastructure.DAL.Entities;

namespace Product.AWP.Infrastructure.DAL.Interfaces.Audit;

public interface IAuditor
{
    /// <summary>
    ///  Инициализирует данные которые будут использованы при записи аудита 
    /// </summary>
    /// <param name="dns">Адрес машины</param>
    /// <param name="login">Логин того кто инициализировал выполнение операции</param>
    /// <param name="operationName">Наименование операции</param>
    void InitAudit(string dns, string login, string operationName);

    Task SaveChangesAsync();

    void WriteAudit(UserRole userRole, Product.AWP.Infrastructure.DAL.Entities.Audit.AuditableEntityState state);
    void WriteAudit(ActualOperatorStatus entity, Product.AWP.Infrastructure.DAL.Entities.Audit.AuditableEntityState state);
    void WriteAudit(AdministrationObject entity, Product.AWP.Infrastructure.DAL.Entities.Audit.AuditableEntityState state);
    void WriteAudit(AdministrationPermission entity, Product.AWP.Infrastructure.DAL.Entities.Audit.AuditableEntityState state);
    void WriteAudit(AesQueue entity, Product.AWP.Infrastructure.DAL.Entities.Audit.AuditableEntityState state);
    void WriteAesQueueMappingAudit(Guid aesQueueId, Guid userRoleId, Product.AWP.Infrastructure.DAL.Entities.Audit.AuditableEntityState state);
    void WriteAudit(Application entity, Product.AWP.Infrastructure.DAL.Entities.Audit.AuditableEntityState state);
    void WriteApplicationMappingAudit(Guid applicationId, Guid userRoleId, Product.AWP.Infrastructure.DAL.Entities.Audit.AuditableEntityState state);
    void WriteAudit(ApplicationInitialization entity, Product.AWP.Infrastructure.DAL.Entities.Audit.AuditableEntityState state);
    void WriteAudit(ControlApplicationInitialization entity, Product.AWP.Infrastructure.DAL.Entities.Audit.AuditableEntityState state);
    void WriteAudit(ExternalApplicationInitialization entity, Product.AWP.Infrastructure.DAL.Entities.Audit.AuditableEntityState state);
    void WriteAudit(WebApplicationInitialization entity, Product.AWP.Infrastructure.DAL.Entities.Audit.AuditableEntityState state);
    void WriteAudit(Configuration entity, Product.AWP.Infrastructure.DAL.Entities.Audit.AuditableEntityState state);
    void WriteAudit(CrossWorkflow entity, Product.AWP.Infrastructure.DAL.Entities.Audit.AuditableEntityState state);
    void WriteAudit(CustomAttribute entity, Product.AWP.Infrastructure.DAL.Entities.Audit.AuditableEntityState state);
    void WriteAudit(Layout entity, Product.AWP.Infrastructure.DAL.Entities.Audit.AuditableEntityState state);
    void WriteLayoutMappingAudit(Guid layoutId, Guid userRoleId, Product.AWP.Infrastructure.DAL.Entities.Audit.AuditableEntityState state);
    void WriteAudit(LoginWorkflow entity, Product.AWP.Infrastructure.DAL.Entities.Audit.AuditableEntityState state);
    void WriteAudit(Module entity, Product.AWP.Infrastructure.DAL.Entities.Audit.AuditableEntityState state);
    void WriteModuleMappingAudit(Guid moduleId, Guid userRoleId, Product.AWP.Infrastructure.DAL.Entities.Audit.AuditableEntityState state);
    void WriteAudit(Operator entity, Product.AWP.Infrastructure.DAL.Entities.Audit.AuditableEntityState state);
    void WriteAudit(OperatorCustomAttribute entity, Product.AWP.Infrastructure.DAL.Entities.Audit.AuditableEntityState state);
    void WriteAudit(OperatorGroupCustomAttribute entity, Product.AWP.Infrastructure.DAL.Entities.Audit.AuditableEntityState state);
    void WriteAudit(OperatorGroup entity, Product.AWP.Infrastructure.DAL.Entities.Audit.AuditableEntityState state);
    void WriteOperatorOperatorGroupMappingAudit(Guid operatorId, Guid operatorGroupId, Product.AWP.Infrastructure.DAL.Entities.Audit.AuditableEntityState state);
    void WriteAudit(OperatorStatus entity, Product.AWP.Infrastructure.DAL.Entities.Audit.AuditableEntityState state);
    void WriteOperatorStatusMappingAudit(Guid operatorStatusId, Guid userRoleId, Product.AWP.Infrastructure.DAL.Entities.Audit.AuditableEntityState state);
    void WriteAudit(Permission entity, Product.AWP.Infrastructure.DAL.Entities.Audit.AuditableEntityState state);
    void WritePermissionMappingAudit(Guid permissionId, Guid userRoleId, Product.AWP.Infrastructure.DAL.Entities.Audit.AuditableEntityState state);
    void WriteAudit(ServiceArea entity, Product.AWP.Infrastructure.DAL.Entities.Audit.AuditableEntityState state);
    void WriteAudit(ServiceAreaConfiguration entity, Product.AWP.Infrastructure.DAL.Entities.Audit.AuditableEntityState state);
    void WriteAudit(ServiceAreaType entity, Product.AWP.Infrastructure.DAL.Entities.Audit.AuditableEntityState state);
    void WriteAudit(UserGroup entity, Product.AWP.Infrastructure.DAL.Entities.Audit.AuditableEntityState state);
    void WriteAudit(Identity entity, Product.AWP.Infrastructure.DAL.Entities.Audit.AuditableEntityState state);
    void WriteUserGroupIdentityMappingAudit(Guid userGroupId, Guid identityId, Product.AWP.Infrastructure.DAL.Entities.Audit.AuditableEntityState state);
    void WriteUserGroupServiceAreaMappingAudit(Guid userGroupId, Guid serviceAreaId, Product.AWP.Infrastructure.DAL.Entities.Audit.AuditableEntityState state);
    void WriteUserGroupUserRoleMappingAudit(Guid userGroupId, Guid userRoleId, Product.AWP.Infrastructure.DAL.Entities.Audit.AuditableEntityState state);
    void WriteUserGroupWorkplaceMappingAudit(Guid userGroupId, Guid workplaceId, Product.AWP.Infrastructure.DAL.Entities.Audit.AuditableEntityState state);
    void WriteAudit(Shortcut entity, Product.AWP.Infrastructure.DAL.Entities.Audit.AuditableEntityState state);
    void WriteShortcutMappingAudit(Guid shortcutId, Guid userRoleId, Product.AWP.Infrastructure.DAL.Entities.Audit.AuditableEntityState state);
    void WriteAudit(UserShortcut entity, Product.AWP.Infrastructure.DAL.Entities.Audit.AuditableEntityState state);
    void WriteAudit(Workflow entity, Product.AWP.Infrastructure.DAL.Entities.Audit.AuditableEntityState state);
    void WriteWorkflowMappingAudit(Guid workflowId, Guid userRoleId, Product.AWP.Infrastructure.DAL.Entities.Audit.AuditableEntityState state);
    void WriteAudit(WorkflowGroup entity, Product.AWP.Infrastructure.DAL.Entities.Audit.AuditableEntityState state);
    void WriteAudit(Workplace entity, Product.AWP.Infrastructure.DAL.Entities.Audit.AuditableEntityState state);
}
