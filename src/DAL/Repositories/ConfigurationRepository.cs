using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Platform.AWP.DataContracts.Infrastructure;
using Platform.Logging.MicrosoftExtensions;
using Product.AWP.Infrastructure.DAL.Converters;
using Product.AWP.Infrastructure.DAL.Entities;
using Product.AWP.Infrastructure.DAL.Interfaces;
using Product.AWP.Infrastructure.DAL.Interfaces.Repositories;
using Application = Platform.AWP.DataContracts.Infrastructure.Application;
using Configuration = Product.AWP.Infrastructure.DAL.Entities.Configuration;
using Layout = Platform.AWP.DataContracts.Infrastructure.Layout;
using LoginWorkflow = Platform.AWP.DataContracts.Infrastructure.LoginWorkflow;
using Workflow = Platform.AWP.DataContracts.Infrastructure.Workflow;

namespace Product.AWP.Infrastructure.DAL.Repositories;

internal sealed class ConfigurationRepository : BaseRepository<Configuration, Platform.AWP.DataContracts.Infrastructure.Configuration>, IConfigurationRepository
{
    private readonly ApplicationToDtoSymmetricConverter _applicationSymmetricConverter = new();
    private readonly ConfigurationToDtoSymmetricConverter _configurationSymmetricConverter = new();
    private readonly LayoutToDtoSymmetricConverter _layoutSymmetricConverter = new();
    private readonly LoginWorkflowToDtoSymmetricConverter _loginWorkflowSymmetricConverter = new();
    private readonly ModuleToDtoSymmetricConverter _moduleSymmetricConverter = new();
    private readonly WorkflowToDtoSymmetricConverter _workflowSymmetricConverter = new();

    public ConfigurationRepository(ILogger<ConfigurationRepository> logger, IInfrastructureDataModel objectContext) : base(logger, objectContext, new ConfigurationToInfrastructureConfigurationDtoSymmetricConverter())
    {
    }

    public async Task<string> GetConfiguration(string configurationName, Guid? serviceAreaId)
    {
        using var mlh = this.CreateMethodLogHelper(Logger).WithArgs("configurationName={configurationName}, serviceAreaId={serviceAreaId}", configurationName, serviceAreaId);
        try
        {
            var entity = await ObjectContext.Configurations.AsNoTracking().FirstOrDefaultAsync(p => p.Name == configurationName);
            if (entity == null)
            {
                return null;
            }

            var result = _configurationSymmetricConverter.ConvertEntityToDto(entity);

            var serviceAreaConfigs = await GetServiceAreaConfigurations(serviceAreaId, new List<Guid> {entity.Id}, AddFilterByConfigurationList);
            result = ExtendedPropertiesMapper.FillProperties(serviceAreaConfigs, entity.Id) ?? result;
            return result;
        }
        catch (Exception exc)
        {
            mlh.LogMethodError(exc, "GetConfiguration({configurationName})", configurationName);
            throw;
        }
    }

    public async Task<Dictionary<string, string>> GetConfigurations(string[] configurationNames, Guid? serviceAreaId)
    {
        if (configurationNames == null) throw new ArgumentNullException(nameof(configurationNames));

        var configEntities = await ObjectContext.Configurations
            .AsNoTracking()
            .Where(x => configurationNames.Contains(x.Name))
            .ToArrayAsync();

        var result = new Dictionary<string, string>();

        if (configEntities.Length == 0)
            return result;

        var configIds = configEntities.Select(c => c.Id).ToList();
        var allServiceAreaConfigs = await GetServiceAreaConfigurations(serviceAreaId, configIds, AddFilterByConfigurationList);

        foreach (var configEntity in configEntities)
        {
            var configValue = _configurationSymmetricConverter.ConvertEntityToDto(configEntity);

            var serviceAreaConfigs = allServiceAreaConfigs?.Where(sac =>
                sac.ConfigurationId.HasValue && sac.ConfigurationId.Value == configEntity.Id).ToList();

            configValue = ExtendedPropertiesMapper.FillProperties(serviceAreaConfigs, configEntity.Id) ?? configValue;
            result.Add(configEntity.Name, configValue);
        }

        return result;
    }

    public async Task<IList<ModuleDescription>> GetModulesForCurrentUser(Guid? serviceAreaId, Guid userRoleId, AwpClientTypes clientTypes)
    {
        using var mlh = this.CreateMethodLogHelper(Logger).WithArgs("serviceAreaId={serviceAreaId}, userRoleId={userRoleId}, clientTypes={clientTypes}", serviceAreaId, userRoleId, clientTypes);

        try
        {
            var entities = await ObjectContext.Modules
                .AsNoTracking()
                .Include(x => x.UserRoles)
                .Where(x => !x.IsDisabled)
                .Where(x => (x.ClientTypes & (int) clientTypes) != 0)
                .ToArrayAsync();

            // Фильтрация по ролям остается в памяти из-за сложной бизнес-логики
            var filteredEntities = entities
                .Where(x => IsEntityAvailableToUserRole(x, userRoleId))
                .ToArray();

            mlh.LogMethodDetails("GetModulesForCurrentUser. Result = {entities.Length}", filteredEntities.Length);
            var result = filteredEntities.Select(_moduleSymmetricConverter.ConvertEntityToDto).ToArray();

            var serviceAreaConfigs = await GetServiceAreaConfigurations(serviceAreaId, result.Select(mod => mod.Id).ToList(), AddFilterByModuleList);
            ExtendedPropertiesMapper.FillProperties(serviceAreaConfigs, result);

            return result.Where(m => !m.IsDisabled).ToArray();
        }
        catch (Exception exc)
        {
            mlh.LogMethodError(exc, "GetModulesForCurrentUser");
            throw;
        }
    }

    public async Task<IList<Application>> GetHostedApplications(Guid? serviceAreaId, Guid userRoleId, AwpClientTypes clientTypes)
    {
        using var mlh = this.CreateMethodLogHelper(Logger).WithArgs("serviceAreaId={serviceAreaId}, userRoleId={userRoleId}, clientTypes={clientTypes}", serviceAreaId, userRoleId, clientTypes);

        try
        {
            var query = ObjectContext.Applications
                .AsNoTracking()
                .Include(app => app.ApplicationInitialization)
                .Include(app => app.WebClientApplicationInitialization)
                .Include(app => app.UserRoles)
                .Include(app => app.LoginWorkflows)
                .Where(x => x.Disabled != true);

            var entities = (await query.ToArrayAsync())
                .Where(x => IsEntityAvailableToUserRole(x, userRoleId))
                .Where(x => (AwpClientTypes) x.ClientTypes == clientTypes || ((AwpClientTypes) x.ClientTypes).HasFlag(clientTypes))
                .ToArray();

            var result = new List<Application>();
            foreach (var applicationEntity in entities)
            {
                var applicationDataContract = _applicationSymmetricConverter.ConvertEntityToDto(applicationEntity);
                applicationDataContract.LoginWorkflows = applicationEntity.LoginWorkflows?
                    .Where(x => (AwpClientTypes) x.ClientTypes == clientTypes || ((AwpClientTypes) x.ClientTypes).HasFlag(clientTypes))
                    .Select(_loginWorkflowSymmetricConverter.ConvertEntityToDto).Cast<LoginWorkflowWithNoAppInfo>().ToArray();
                result.Add(applicationDataContract);
            }

            var serviceAreaConfigs = await GetServiceAreaConfigurations(serviceAreaId, result.Select(app => app.Id).ToList(), AddFilterByApplicationList);
            ExtendedPropertiesMapper.FillProperties(serviceAreaConfigs, result);

            return result;
        }
        catch (Exception exc)
        {
            mlh.LogMethodError(exc, "GetHostedApplications", this, null);
            throw;
        }
    }

    public async Task<LoginWorkflow[]> GetLoginWorkflowsForApplication(Guid applicationId, AwpClientTypes clientTypes)
    {
        using var mlh = this.CreateMethodLogHelper(Logger).WithArgs("applicationId={applicationId}, clientTypes={clientTypes}", applicationId, clientTypes);

        try
        {
            var workflows = await ObjectContext
                .LoginWorkflows
                .AsNoTracking()
                .Include(x => x.Application)
                .Where(lwf => lwf.Application.Id == applicationId)
                .ToArrayAsync();

            var loginWorkflows =
                workflows.Where(x =>
                        (AwpClientTypes) x.ClientTypes == clientTypes ||
                        ((AwpClientTypes) x.ClientTypes).HasFlag(clientTypes))
                    .ToArray();

            return loginWorkflows.Select(_loginWorkflowSymmetricConverter.ConvertEntityToDto).ToArray();
        }
        catch (Exception exc)
        {
            mlh.LogMethodError(exc, "GetLoginWorkflowForApplication()");
            throw;
        }
    }

    public async Task<Layout[]> GetLayouts(Guid userRoleId)
    {
        using var mlh = this.CreateMethodLogHelper(Logger).WithArgs("userRoleId={userRoleId}", userRoleId);

        try
        {
            var allLayouts = await ObjectContext.Layouts
                .AsNoTracking()
                .Include(l => l.UserRoles)
                .ToArrayAsync();

            var layoutList = allLayouts
                .Where(x => IsEntityAvailableToUserRole(x, userRoleId))
                .ToArray();

            return layoutList.Distinct().Select(_layoutSymmetricConverter.ConvertEntityToDto).ToArray();
        }
        catch (Exception exc)
        {
            mlh.LogMethodError(exc, "GetLayouts");
            throw;
        }
    }

    public async Task<Workflow[]> GetWorkflows(Guid userRoleId, AwpClientTypes clientTypes)
    {
        using var mlh = this.CreateMethodLogHelper(Logger).WithArgs("userRoleId='{userRoleId}', clientTypes='{clientTypes}'", userRoleId, clientTypes);
        try
        {
            var allWorkflows = await ObjectContext.Workflows
                .AsNoTracking()
                .Include(wf => wf.UserRoles)
                .Include(wf => wf.CrossWorkflows)
                .Include(wf => wf.WorkflowGroup)
                .ToListAsync();

            var wfList = allWorkflows
                .Where(x => IsEntityAvailableToUserRole(x, userRoleId))
                .Where(x => (AwpClientTypes) x.ClientTypes == clientTypes || ((AwpClientTypes) x.ClientTypes).HasFlag(clientTypes))
                .ToList();

            return wfList.Distinct().Select(_workflowSymmetricConverter.ConvertEntityToDto).ToArray();
        }
        catch (Exception exc)
        {
            mlh.LogMethodFailed(exc);
            throw;
        }
    }

    public async Task<IDictionary<string, Guid>> GetConfigurationsVersions()
    {
        using var mlh = this.CreateMethodLogHelper(Logger).WithNoArgs();

        try
        {
            var configVersions = await ObjectContext.ConfigurationVersions.AsNoTracking().ToArrayAsync();

            return configVersions.ToDictionary(p => p.Name, p => p.VersionMarker);
        }
        catch (Exception exc)
        {
            mlh.LogMethodError(exc, "GetConfigurationsVersions");
            return null;
        }
    }

    #region private: getting ServiceAreaConfigurations

    private static IQueryable<ServiceAreaConfiguration> AddFilterByApplicationList(IQueryable<ServiceAreaConfiguration> currentQuery, Guid[] applicationIds)
    {
        return currentQuery?.Where(sac => sac.ApplicationId.HasValue && applicationIds.Contains(sac.ApplicationId.Value));
    }

    private static IQueryable<ServiceAreaConfiguration> AddFilterByConfigurationList(IQueryable<ServiceAreaConfiguration> currentQuery, Guid[] configurationIds)
    {
        return currentQuery?.Where(sac => sac.ConfigurationId.HasValue && configurationIds.Contains(sac.ConfigurationId.Value));
    }

    private static IQueryable<ServiceAreaConfiguration> AddFilterByModuleList(IQueryable<ServiceAreaConfiguration> currentQuery, Guid[] moduleIds)
    {
        return currentQuery?.Where(sac => sac.ModuleId.HasValue && moduleIds.Contains(sac.ModuleId.Value));
    }

    #endregion
}
