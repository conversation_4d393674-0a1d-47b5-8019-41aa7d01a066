using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Platform.Common.Collections;
using Product.AWP.Infrastructure.DAL.Converters;
using Product.AWP.Infrastructure.DAL.Interfaces;
using Product.AWP.Infrastructure.DAL.Interfaces.Repositories;
using PermissionDc = Platform.AWP.DataContracts.Infrastructure.Permission;
using PermissionDo = Product.AWP.Infrastructure.DAL.Entities.Permission;

namespace Product.AWP.Infrastructure.DAL.Repositories;

internal sealed class PermissionRepository : BaseRepository<PermissionDo, PermissionDc>, IPermissionRepository
{
    public PermissionRepository(ILogger<PermissionRepository> logger, IInfrastructureDataModel objectContext) : base(logger, objectContext, new PermissionToDtoSymmetricConverter())
    {
    }

    public async Task<PermissionDc[]> GetPermissionsForCurrentUser(Guid userRoleId)
    {
        var permissions = new List<PermissionDo>();

        var allPermissions = await ObjectSet.AsNoTracking().Include(x => x.UserRoles).ToArrayAsync();

        foreach (var item in allPermissions)
        {
            if (IsEntityAvailableToUserRole(item, userRoleId))
            {
                permissions.Add(item);
            }
        }

        var result = permissions.Select(Converter.ConvertEntityToDto);
        return result.Distinct().ToArray();
    }

    public async Task<bool> RequestPermission(string permission, Guid userRoleId)
    {
        return (await GetPermissionsForCurrentUser(userRoleId)).EmptyIfNull().Any(x => string.Equals(x.Name, permission, StringComparison.InvariantCultureIgnoreCase));
    }
}
