using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Product.AWP.Infrastructure.DAL.Converters;
using Product.AWP.Infrastructure.DAL.Entities;
using Product.AWP.Infrastructure.DAL.Interfaces;
using Product.AWP.Infrastructure.DAL.Interfaces.Repositories;

namespace Product.AWP.Infrastructure.DAL.Repositories;

internal sealed class OperatorStatusRepository : BaseRepository<OperatorStatus, Platform.AWP.DataContracts.Infrastructure.OperatorStatus>, IOperatorStatusRepository
{
    public OperatorStatusRepository(ILogger<OperatorStatusRepository> logger, IInfrastructureDataModel objectContext) : base(logger, objectContext, new OperatorStatusConverter())
    {
        (Converter as OperatorStatusConverter)?.SetDataModel(ObjectContext);
    }

    public async Task<IList<Platform.AWP.DataContracts.Infrastructure.OperatorStatus>> GetAllStatuses()
    {
        var result = await ObjectContext.OperatorStatuses.AsNoTracking().ToListAsync();
        return result.Select(Converter.ConvertEntityToDto).ToArray();
    }

    public async Task<Platform.AWP.DataContracts.Infrastructure.CustomAttribute[]> GetCustomAttributes(Guid statusId, string[]? attributeNames)
    {
        var status = await ObjectContext.OperatorStatuses.Where(x => x.Id == statusId)
            .Include(x => x.CustomAttributes)
            .FirstAsync();

        var attributeConverter = new CustomAttributeToDtoSymmetricConverter();
        if (attributeNames == null)
        {
            return status.CustomAttributes.Select(attributeConverter.ConvertEntityToDto).ToArray();
        }

        return status.CustomAttributes
            .Where(x => attributeNames.Any(attr => string.Equals(x.Name, attr, StringComparison.InvariantCultureIgnoreCase)))
            .Select(attributeConverter.ConvertEntityToDto).ToArray();
    }

    private IQueryable<OperatorStatus> IncludeLinkedEntities(IQueryable<OperatorStatus> query)
    {
        return query
            .Include(x => x.UserRoles)
            .Include(x => x.CustomAttributes);
    }

    private async Task BeforeDelete(OperatorStatus deletingEntity)
    {
        var transitions = await ObjectContext.StatusTransitions.Where(p => p.OperatorStatusFromId == deletingEntity.Id || p.OperatorStatusToId == deletingEntity.Id).ToArrayAsync();

        foreach (var statusTransition in transitions)
        {
            ObjectContext.Delete(statusTransition);
        }

        await base.BeforeDelete(deletingEntity);
    }
}
