using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Platform.Logging.MicrosoftExtensions;
using Product.AWP.Infrastructure.DAL.Converters;
using Product.AWP.Infrastructure.DAL.Entities;
using Product.AWP.Infrastructure.DAL.Interfaces;
using Product.AWP.Infrastructure.DAL.Interfaces.Repositories;

namespace Product.AWP.Infrastructure.DAL.Repositories;

internal sealed class OperatorStatusAvailabilityRepository : BaseRepository<OperatorStatus, Platform.AWP.DataContracts.Infrastructure.OperatorStatus>, IOperatorStatusAvailabilityRepository
{
    private readonly UserRoleToDtoSymmetricConverter _userRoleSymmetricConverter = new();

    public OperatorStatusAvailabilityRepository(ILogger<OperatorStatusAvailabilityRepository> logger, IInfrastructureDataModel objectContext) : base(logger, objectContext, new OperatorStatusConverter())
    {
        (Converter as OperatorStatusConverter)?.SetDataModel(ObjectContext);
    }

    public async Task<Platform.AWP.DataContracts.Infrastructure.OperatorStatus[]> GetAvailableForUser(Guid userRoleId, Func<string, Task<bool>> currentUserIsInGroupPredicate)
    {
        var userRolesIds = await GetRoleIdWithParentRolesIds(userRoleId);
        var query = await GetOperatorStatusesForRoles(userRolesIds, currentUserIsInGroupPredicate);
        var result = await query.AsNoTracking().ToArrayAsync();
        return result.Select(Converter.ConvertEntityToDto).ToArray();
    }

    public async Task<Platform.AWP.DataContracts.Infrastructure.UserRole[]> GetAvailableMappings(Guid itemId)
    {
        using var mlh = this.CreateMethodLogHelper(Logger).WithArgs("itemId={itemId}", itemId);

        try
        {
            return (await ObjectContext.UserRoles
                    .AsNoTracking()
                    .Where(ur => ur.OperatorStatuses.All(p => p.Id != itemId))
                    .ToArrayAsync())
                .Select(_userRoleSymmetricConverter.ConvertEntityToDto)
                .ToArray();
        }
        catch (Exception exc)
        {
            mlh.LogMethodError(exc, "GetAvailableMappings()");
            throw;
        }
    }

    public async Task<Platform.AWP.DataContracts.Infrastructure.OperatorStatus[]> GetAvailableFrom(Guid statusId, Guid userRoleId, Func<string, Task<bool>> currentUserIsInGroupPredicate)
    {
        var current = await ObjectContext.OperatorStatuses.AsNoTracking().Include(x => x.StatusTransitionsFromHere).FirstAsync(p => p.Id == statusId);

        var transitions = current.StatusTransitionsFromHere.ToArray();
        var operatorStatusToIds = transitions.Select(x => x.OperatorStatusToId).ToArray();
        var userRolesIds = await GetRoleIdWithParentRolesIds(userRoleId);
        var statuses = userRolesIds?.Any() == true ? await GetOperatorStatusesForRoles(userRolesIds, currentUserIsInGroupPredicate) : ObjectContext.OperatorStatuses;
        var result = (await statuses.AsNoTracking().Where(x => operatorStatusToIds.Contains(x.Id))
                .Include(x => x.CustomAttributes)
                .ToListAsync())
            .Select(Converter.ConvertEntityToDto)
            .ToArray();

        var defaultTransitions = transitions.FirstOrDefault(p => p.IsDefault);
        if (defaultTransitions == null)
        {
            return result;
        }

        var defaultState = result.FirstOrDefault(p => p.Id == defaultTransitions.OperatorStatusToId);
        if (defaultState == null)
        {
            result.First().IsDefault = true;
        }
        else
        {
            defaultState.IsDefault = true;
        }

        return result;
    }

    protected override IQueryable<OperatorStatus> IncludeLinkedEntities(IQueryable<OperatorStatus> query)
    {
        return query
            .Include(x => x.UserRoles)
            .Include(x => x.CustomAttributes);
    }

    private async Task<IQueryable<OperatorStatus>> GetOperatorStatusesForRoles(Guid[] userRolesIds, Func<string, Task<bool>> currentUserIsInGroupPredicate)
    {
        var mappings = await ObjectContext.UserRoles.AsNoTracking().Include(x => x.UserGroups).Where(x => userRolesIds.Contains(x.Id)).ToListAsync();

        var mappingsArray = new List<Guid>();

        foreach (var mapping in mappings)
        {
            var groups = mapping.UserGroups.SelectMany(ug => ug.Identities.Select(ident => ident.Login)).ToArray();
            if (await UserIsInGroups(groups, currentUserIsInGroupPredicate))
            {
                mappingsArray.Add(mapping.Id);
            }
        }

        return ObjectContext.OperatorStatuses
            .Where(p => p.UserRoles.Any(u => mappingsArray.Contains(u.Id)))
            .Include(x => x.CustomAttributes);
    }

    private async Task<Guid[]> GetRoleIdWithParentRolesIds(Guid roleId)
    {
        var result = new List<Guid>(10);

        var allRoles = await ObjectContext.UserRoles
            .AsNoTracking()
            .Select(r => new {r.Id, r.ParentId})
            .ToListAsync();

        var rolesDict = allRoles.ToDictionary(r => r.Id, r => r.ParentId);

        Guid? currentRoleId = roleId;
        var visitedRoles = new HashSet<Guid>();
        while (currentRoleId.HasValue && rolesDict.ContainsKey(currentRoleId.Value))
        {
            if (visitedRoles.Contains(currentRoleId.Value))
            {
                break;
            }

            visitedRoles.Add(currentRoleId.Value);
            result.Add(currentRoleId.Value);
            currentRoleId = rolesDict[currentRoleId.Value];
        }

        return result.ToArray();
    }

    private async Task<bool> UserIsInGroups(IEnumerable<string> adGroupNames, Func<string, Task<bool>> currentUserIsInGroupPredicate)
    {
        using var mlh = this.CreateMethodLogHelper(Logger).WithArgs("adGroupNames={adGroupNames}", adGroupNames);

        foreach (var adGroupName in adGroupNames)
        {
            mlh.LogMethodDetails("Try to check if current user is in role '{adGroupName}'", adGroupName);
            try
            {
                if (await currentUserIsInGroupPredicate(adGroupName))
                {
                    mlh.LogMethodDetails("Try to check if current user is in role '{adGroupName}'", adGroupName);
                    return true;
                }
            }
            catch (Exception exc)
            {
                mlh.LogMethodError(exc, $"Failed to check if current user is in role '{adGroupName}'", adGroupName);
            }

            mlh.LogMethodDetails("Check if current user is in role '{adGroupName}' = false", adGroupName);
        }

        return false;
    }
}
