using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Platform.AWP.DataContracts;
using Platform.AWP.DataContracts.Infrastructure;
using Platform.Common.Collections;
using Platform.Logging.MicrosoftExtensions;
using Product.AWP.Infrastructure.DAL.Converters;
using Product.AWP.Infrastructure.DAL.Entities;
using Product.AWP.Infrastructure.DAL.Interfaces;
using Product.AWP.Infrastructure.DAL.Interfaces.Repositories;

namespace Product.AWP.Infrastructure.DAL.Repositories;

internal sealed class ActualOperatorStatusRepository : BaseRepository<ActualOperatorStatus, Product.AWP.Infrastructure.Domain.Entities.ActualOperatorStatus>, IActualOperatorStatusRepository
{
    private static readonly OperatorStatusInfoConverter _operatorStatusInfoConverter = new();

    public ActualOperatorStatusRepository(ILogger<ActualOperatorStatusRepository> logger, IInfrastructureDataModel objectContext) : base(logger, objectContext, new ActualOperatorStatusConverter())
    {
    }

    public async Task<OperatorStatusInfo[]> GetActualOperatorsStatuses(ActualOperatorStatusFilter actualOperatorStatusFilter)
    {
        var utcNowOffset = DateTimeOffset.UtcNow;

        var statuses = ObjectContext.ActualOperatorStatuses
            .AsNoTracking()
            //	если передать не переменную, а сразу DateTimeOffset.UtcNow, то в запрос вставится "now() AT TIME ZONE 'UTC'", а это - "timestamp without timezone", а поле - "with timezone", и сравнение работает неправильно
            //	с переменной вставляется значение с таймзоной и всё сравнивается корректно
            .Where(x => x.ValidUntil == null || x.ValidUntil > utcNowOffset);

        if (actualOperatorStatusFilter != null)
        {
            //фильтруем по операторам
            if (!actualOperatorStatusFilter.OperatorIds.IsEmpty())
            {
                var operatorIdsArray = actualOperatorStatusFilter.OperatorIds.ToArray();
                statuses = statuses.Where(x => operatorIdsArray.Contains(x.OperatorId));
            }

            //фильтруем по регионам обслуживания
            if (!actualOperatorStatusFilter.ServiceAreaIds.IsEmpty())
            {
                var serviceAreaIdsArray = actualOperatorStatusFilter.ServiceAreaIds.ToArray();
                statuses = statuses.Where(aos => aos.WorkSessionId.HasValue && serviceAreaIdsArray.Contains(aos.WorkSession.ServiceAreaId));
            }

            // фильтруем по статусам
            if (!actualOperatorStatusFilter.StatusIds.IsEmpty())
            {
                var statusIdsArray = actualOperatorStatusFilter.StatusIds.ToArray();
                switch (actualOperatorStatusFilter.StatusFilterType)
                {
                    case StatusFilterType.Except:
                        statuses = statuses.Where(aos => !statusIdsArray.Contains(aos.ActualStatusId));
                        break;
                    case StatusFilterType.In:
                        statuses = statuses.Where(aos => statusIdsArray.Contains(aos.ActualStatusId));
                        break;
                    default:
                        throw new ArgumentOutOfRangeException();
                }
            }
        }

        var statusesList = await statuses.ToArrayAsync();
        return statusesList.Select(_operatorStatusInfoConverter.ConvertEntityToDto).ToArray();
    }

    public async Task SetActualOperatorStatus(Guid operatorId, Guid currentStatusId, Guid? pendingStatusId, DateTimeOffset dateFrom, DateTimeOffset? tillDate, string actualStatusSetCode, Guid? workSessionId)
    {
        var fakeDto = new Product.AWP.Infrastructure.Domain.Entities.ActualOperatorStatus
        {
            CurrentStatusId = currentStatusId,
            PendingStatusId = pendingStatusId,
            OperatorId = operatorId,
            ValidUntil = tillDate,
            DateFrom = dateFrom,
            ActualStatusSetCode = actualStatusSetCode,
            WorkSessionId = workSessionId
        };

        var opIdParameter = operatorId;
        var curVal = await ObjectSet.FirstOrDefaultAsync(x => x.OperatorId == opIdParameter);
        if (curVal != null)
        {
            fakeDto.PreviousStatusId = curVal.ActualStatusId;
            fakeDto.PreviousPendingStatusId = curVal.ActualPendingStatusId;
            await Update(fakeDto);
        }
        else
        {
            await Add(fakeDto);
        }
    }

    public async Task<bool> ProlongateOperatorStatus(Guid operatorId, Guid actualStatusId, DateTimeOffset? tillDate)
    {
        using var mlh = this.CreateMethodLogHelper(Logger).WithArgs("operatorId={operatorId}, actualStatusId={actualStatusId}, tillDate={tillDate}", operatorId, actualStatusId, tillDate);
        var currentStatus = await ObjectSet.FirstOrDefaultAsync(p => p.OperatorId == operatorId && p.ActualStatusId == actualStatusId);
        if (currentStatus == null)
        {
            mlh.LogMethodDetails("ActualOperatorStatus not found for operator id={operatorId}, actualStatusId={actualStatusId}", operatorId, actualStatusId);
            //throw new InvalidOperationException($"ActualOperatorStatus not found for operator id='{operatorId}', actualStatusId='{actualStatusId}'");
            return false;
        }

        currentStatus.ValidUntil = tillDate;
        await SaveChangesAsync();
        return true;
    }

    protected override async Task<ActualOperatorStatus> SelectEntity(Guid itemId)
    {
        return await ObjectSet.FirstOrDefaultAsync(p => p.Id == itemId);
    }
}
