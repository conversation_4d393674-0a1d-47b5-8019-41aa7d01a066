using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Product.AWP.Infrastructure.DAL.Converters;
using Product.AWP.Infrastructure.DAL.Entities;
using Product.AWP.Infrastructure.DAL.Interfaces;
using Product.AWP.Infrastructure.DAL.Interfaces.Repositories;

namespace Product.AWP.Infrastructure.DAL.Repositories;

public class UserGroupRepositor : BaseRepository<UserGroup, Platform.AWP.DataContracts.Infrastructure.UserGroup>, IUserGroupRepository
{
    public UserGroupRepositor(ILogger<UserGroupRepositor> logger, IInfrastructureDataModel objectContext) : base(logger, objectContext, new UserGroupToDtoSymmetricConverter())
    {
    }

    protected override IQueryable<UserGroup> IncludeLinkedEntities(IQueryable<UserGroup> query)
    {
        return query
            .Include(x => x.UserRoles)
            .Include(x => x.Identities);
    }
}
