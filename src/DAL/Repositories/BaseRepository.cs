using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Platform.Common;
using Platform.Common.Collections;
using Platform.Logging.MicrosoftExtensions;
using Product.AWP.Infrastructure.DAL.Converters;
using Product.AWP.Infrastructure.DAL.Entities;
using Product.AWP.Infrastructure.DAL.Interfaces;
using Product.AWP.Infrastructure.DAL.Interfaces.Audit;

namespace Product.AWP.Infrastructure.DAL.Repositories;

public abstract class BaseRepository<TEntity, TDataContract> : IDisposable, IAuditable, IHasAdditionalLogInfo
    where TEntity : class, IHasReadOnlyId, new()
    where TDataContract : class, IHasReadOnlyId
{
    protected readonly ISymmetricConverter<TEntity, TDataContract> Converter;

    private DbSet<TEntity> _objectSet;

    protected BaseRepository(ILogger logger, IInfrastructureDataModel objectContext, ISymmetricConverter<TEntity, TDataContract> converter)
    {
        AdditionalLogInfo = new AdditionalLogInfo(new Platform.Logging.LoggingCategory("DataAccess"));
        Logger = logger;
        ObjectContext = objectContext;
        Converter = converter ?? throw new ArgumentNullException(nameof(converter));
    }

    protected IInfrastructureDataModel ObjectContext { get; }
    public ILogger Logger { get; }

    protected DbSet<TEntity> ObjectSet => _objectSet ??= ObjectContext.GetDbSet<TEntity>();

    public virtual void InitAuditData(string dns, string login, string operationName)
    {
        ObjectContext?.InitAuditData(dns, login, operationName);
    }

    public virtual void SetAuditor(IAuditor auditor)
    {
        ObjectContext?.SetAuditor(auditor);
    }

    public AdditionalLogInfo AdditionalLogInfo { get; protected set; }

    protected virtual async Task<TEntity> SelectEntity(Guid itemId)
    {
        return await IncludeLinkedEntities(ObjectSet).SingleOrDefaultAsync(p => p.Id == itemId);
    }

    public virtual async Task<TDataContract> Get(Guid id)
    {
        var ent = await IncludeLinkedEntities(ObjectSet).AsNoTracking().FirstOrDefaultAsync(p => p.Id == id);
        return ent.IfNotNull(Converter.ConvertEntityToDto);
    }

    public virtual async Task<TDataContract[]> GetList()
    {
        var result = await IncludeLinkedEntities(ObjectSet).AsNoTracking().ToArrayAsync();
        return result.Select(p => Converter.ConvertEntityToDto(p)).ToArray();
    }

    protected virtual async Task DoAdd(TEntity entity)
    {
        await ObjectContext.AddAsync(entity);
    }

    protected virtual IQueryable<TEntity> IncludeLinkedEntities(IQueryable<TEntity> query)
    {
        return query;
    }

    protected virtual Task MergeChanges(TDataContract dataItem, TEntity entityItem)
    {
        Converter.MergeChanges(dataItem, entityItem);
        return Task.CompletedTask;
    }

    protected async Task SaveChangesAsync()
    {
        using var mlh = this.CreateMethodLogHelperNoImplicitLogging(Logger).WithNoArgs();

        try
        {
            await ObjectContext.SaveChangesAsync();
        }
        catch (Exception exc)
        {
            mlh.LogMethodFailed(exc);
            throw;
        }
    }

    public virtual async Task Add(TDataContract dataContract)
    {
        using var mlh = this.CreateMethodLogHelper(Logger).WithArgs("dataContract={dataContract}", dataContract);

        try
        {
            var entity = new TEntity();
            Converter.MergeChanges(dataContract, entity);
            await DoAdd(entity);
            await ObjectContext.SaveChangesAsync();
        }
        catch (Exception exc)
        {
            mlh.LogMethodError(exc, "Add() failed");
            throw;
        }
    }

    public virtual async Task Update(TDataContract itemToUpdate)
    {
        using var mlh = this.CreateMethodLogHelper(Logger).WithArgs("itemToUpdate={itemToUpdate}", itemToUpdate);

        try
        {
            var entityToUpdate = await SelectEntity(itemToUpdate.Id);
            await MergeChanges(itemToUpdate, entityToUpdate);
            await ObjectContext.SaveChangesAsync();
        }
        catch (Exception exc)
        {
            mlh.LogMethodError(exc, "Error when save item");
            throw;
        }
    }

    public virtual async Task UpdateMany(TDataContract[] itemsToUpdate)
    {
        using var mlh = this.CreateMethodLogHelper(Logger).WithArgs("itemsToUpdate={itemsToUpdate}");

        if (itemsToUpdate == null || itemsToUpdate.Length == 0)
            return;

        var ids = itemsToUpdate.Select(item => item.Id).ToArray();
        var entities = await IncludeLinkedEntities(ObjectSet)
            .Where(e => ids.Contains(e.Id))
            .ToListAsync();

        var entitiesDict = entities.ToDictionary(e => e.Id);

        foreach (var item in itemsToUpdate)
        {
            try
            {
                if (entitiesDict.TryGetValue(item.Id, out var entityToUpdate))
                {
                    await MergeChanges(item, entityToUpdate);
                }
                else
                {
                    mlh.LogMethodWarn("Entity with Id={itemId} not found for update", item.Id);
                }
            }
            catch (Exception exception)
            {
                mlh.LogMethodError(exception, "Merge changes({item})", item);
                throw;
            }
        }

        try
        {
            await ObjectContext.SaveChangesAsync();
        }
        catch (Exception exc)
        {
            var itemsToUpdateStr = string.Join(Environment.NewLine, itemsToUpdate.AsEnumerable());
            mlh.LogMethodError(exc, "Error when save items:{itemsToUpdateStr}", itemsToUpdateStr);
            throw;
        }
    }

    public virtual async Task Delete(Guid itemId)
    {
        using var mlh = this.CreateMethodLogHelper(Logger).WithArgs("itemId={itemId}", itemId);
        try
        {
            var entityToDelete = await SelectEntity(itemId);

            await BeforeDelete(entityToDelete);
            ObjectContext.Delete(entityToDelete);
            await ObjectContext.SaveChangesAsync();
        }
        catch (Exception exc)
        {
            mlh.LogMethodError(exc, "Eror when delete item");
            throw;
        }
    }

    /// <summary>
    /// Executes before SaveChanges when deleting specified entity
    /// </summary>
    /// <param name="deletingEntity"></param>
    protected virtual Task BeforeDelete(TEntity deletingEntity)
    {
        return Task.CompletedTask;
    }

    #region stuff

    /// <summary>
    /// Проверка что объект смапплен на пользовательскую роль
    /// </summary>
    /// <returns></returns>
    protected bool IsEntityAvailableToUserRole(IMappedToUserRole mappedEntity, Guid userRoleId)
    {
        if (mappedEntity.UserRoles.Any(x => userRoleId == x.Id)) return true;

        var userRole = ObjectContext.UserRoles.FirstOrDefault(x => x.Id == userRoleId);
        if (userRole == null)
        {
            throw new ArgumentException($"No role with Id='{userRoleId}' exist!");
        }

        return IsEntityAvailableToUserRole(mappedEntity, userRole);
    }

    protected bool IsEntityAvailableToUserRole(IMappedToUserRole mappedEntity, UserRole userRole)
    {
        if (mappedEntity.UserRoles.Any(x => x.Id == userRole.Id)) return true;
        if (userRole.Parent == null) return false;
        return IsEntityAvailableToUserRole(mappedEntity, userRole.Parent);
    }

    protected async Task<List<ServiceAreaConfiguration>> GetServiceAreaConfigurations(Guid? serviceAreaId, List<Guid> entitiesIds, Func<IQueryable<ServiceAreaConfiguration>, Guid[], IQueryable<ServiceAreaConfiguration>> filterConfigsByEntitiesIdsFunc)
    {
        var saHierarchyChain = await GetServiceAreaHierarchyChain(serviceAreaId);
        if (saHierarchyChain == null || saHierarchyChain.IsEmpty())
        {
            return null;
        }

        if (entitiesIds == null || entitiesIds.IsEmpty())
        {
            return null;
        }

        var applicationIdsArray = entitiesIds.ToArray(); //чтобы понял SQL generator
        var saIdsArray = saHierarchyChain.ToArray(); //чтобы понял SQL generator
        var allSaConfigsForSaChainQuery = ObjectContext.ServiceAreaConfigurations.Where(sac => saIdsArray.Contains(sac.ServiceAreaId));

        var allSaConfigsForSaChainAndEntityListQuery = filterConfigsByEntitiesIdsFunc(allSaConfigsForSaChainQuery, applicationIdsArray);
        var allSaConfigsForSpecifiedChainAndEntities = await allSaConfigsForSaChainAndEntityListQuery.ToListAsync();

        var result = new List<ServiceAreaConfiguration>();
        foreach (var groupByItemId in allSaConfigsForSpecifiedChainAndEntities.GroupBy(sac => sac.GetEntityId()))
        {
            foreach (var groupByPropertyForOneItem in groupByItemId.GroupBy(sac => sac.PropertyKey))
            {
                foreach (var saIdDeeperFirst in saHierarchyChain)
                {
                    var sacForDeeper = groupByPropertyForOneItem.FirstOrDefault(sac => sac.ServiceAreaId == saIdDeeperFirst);
                    if (sacForDeeper != null)
                    {
                        result.Add(sacForDeeper);
                        break;
                    }
                }
            }
        }

        return result;
    }

    /// <summary>
    /// Возвращает список идентификаторов регионов обслуживания в виде цепочки _заданный_ -> _родитель_ -> ... -> _родитель_. Первый эелементсписка - заданный регион, последний - самый верхний родитель.
    /// </summary>
    /// <returns></returns>
    protected async Task<IList<Guid>> GetServiceAreaHierarchyChain(Guid? serviceAreaId)
    {
        var result = new List<Guid>();
        var currentParentId = serviceAreaId;
        while (currentParentId.HasValue)
        {
            var currentParent = await ObjectContext.ServiceAreas.FirstOrDefaultAsync(x => x.Id == currentParentId);
            if (currentParent == null)
            {
                return result;
            }

            result.Add(currentParent.Id);
            currentParentId = currentParent.ParentId;
        }

        return result;
    }

    #endregion


    #region IDisposable

    protected virtual void Dispose(bool disposing)
    {
        if (!disposing) return;

        ObjectContext?.Dispose();
    }

    public void Dispose()
    {
        try
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }
        catch (Exception exc)
        {
            using var mlh = this.CreateMethodLogHelperNoImplicitLogging(Logger).WithNoArgs();
            mlh.LogMethodFailed(exc);
            throw;
        }
    }

    #endregion
}
