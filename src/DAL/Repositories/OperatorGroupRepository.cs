using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Platform.Common.Collections;
using Product.AWP.Infrastructure.DAL.Converters;
using Product.AWP.Infrastructure.DAL.Entities;
using Product.AWP.Infrastructure.DAL.Interfaces;
using Product.AWP.Infrastructure.DAL.Interfaces.Repositories;

namespace Product.AWP.Infrastructure.DAL.Repositories;

internal sealed class OperatorGroupRepository : BaseRepository<OperatorGroup, Platform.AWP.DataContracts.Infrastructure.OperatorGroup>, IOperatorGroupRepository
{
    private readonly OperatorGroupCustomAttributeConverter _customAttributeConverter;

    public OperatorGroupRepository(ILogger<OperatorGroupRepository> logger, IInfrastructureDataModel objectContext) : base(logger, objectContext, new OperatorGroupConverter())
    {
        _customAttributeConverter = new OperatorGroupCustomAttributeConverter();
    }

    public override async Task<Platform.AWP.DataContracts.Infrastructure.OperatorGroup[]> GetList()
    {
        var result = await ObjectSet
            .AsNoTracking()
            .Include(x => x.OperatorLinks)
            .Include(x => x.CustomAttributes)
            .ToListAsync();

        return result.Select(p => Converter.ConvertEntityToDto(p)).ToArray();
    }

    public override async Task Add(Platform.AWP.DataContracts.Infrastructure.OperatorGroup dataContract)
    {
        await base.Add(dataContract);
        await UpdateOperatorMapping(dataContract);
    }

    public override async Task Update(Platform.AWP.DataContracts.Infrastructure.OperatorGroup dataContract)
    {
        await base.Update(dataContract);
        await UpdateOperatorMapping(dataContract);
    }

    public async Task AddOrUpdateCustomAttributes(IList<Platform.AWP.DataContracts.Infrastructure.OperatorGroupCustomAttribute> attributes)
    {
        foreach (var attr in attributes)
        {
            var existAttribute = await ObjectContext.OperatorGroupCustomAttributes.FirstOrDefaultAsync(x => x.OperatorGroupId == attr.OperatorGroupId && x.Code == attr.Code);
            if (existAttribute != null)
            {
                _customAttributeConverter.MergeChanges(attr, existAttribute);
            }
            else
            {
                var newAttribute = new OperatorGroupCustomAttribute();
                _customAttributeConverter.MergeChanges(attr, newAttribute);
                ObjectContext.OperatorGroupCustomAttributes.Add(newAttribute);
            }
        }

        await ObjectContext.SaveChangesAsync();
    }

    public async Task RemoveCustomAttribute(Guid operatorGroupId, IList<string> codes)
    {
        var query = ObjectContext.OperatorGroupCustomAttributes.Where(x => x.OperatorGroupId == operatorGroupId);
        if (codes != null)
        {
            var localCodes = codes.ToArray();
            query = query.Where(x => localCodes.Contains(x.Code));
        }

        var attrFromDb = await query.ToArrayAsync();
        foreach (var entity in attrFromDb)
        {
            ObjectContext.OperatorGroupCustomAttributes.Remove(entity);
        }

        await ObjectContext.SaveChangesAsync();
    }

    public async Task<Platform.AWP.DataContracts.Infrastructure.OperatorGroupCustomAttribute[]> GetCustomAttributes(IList<Guid> operatorGroupIds, IList<string> codes)
    {
        var operatorIdsArray = operatorGroupIds.ToArray();

        var query = ObjectContext.OperatorGroupCustomAttributes.Where(x => operatorIdsArray.Contains(x.OperatorGroupId));
        if (codes != null)
        {
            var localCodes = codes.ToArray();
            query = query.Where(x => localCodes.Contains(x.Code));
        }

        return (await query.ToArrayAsync()).Select(x => _customAttributeConverter.ConvertEntityToDto(x)).ToArray();
    }

    public async Task AddOperatorToGroups(Guid operatorId, params Guid[] groupsIds)
    {
        var currentOperator = await ObjectContext.Operators.FirstOrDefaultAsync(x => x.Id == operatorId);
        if (currentOperator != null)
        {
            currentOperator.OperatorGroups = await ObjectContext.OperatorGroups
                .Where(x => groupsIds.Contains(x.Id))
                .ToListAsync();
            await ObjectContext.SaveChangesAsync();
        }
    }

    public async Task RemoveOperatorFromGroup(Guid operatorId, Guid operatorGroupId)
    {
        var group = await ObjectContext.OperatorGroups.SingleAsync(x => x.Id == operatorGroupId);
        var existOperator = group.Operators.EmptyIfNull().FirstOrDefault(x => x.Id == operatorId);
        if (existOperator == null)
        {
            return;
        }

        group.Operators.Remove(existOperator);
        await ObjectContext.SaveChangesAsync();
    }

    public async Task<bool> IsOperatorInGroup(Guid operatorId, Guid operatorGroupId, bool searchInChilds = false)
    {
        if (!searchInChilds)
        {
            return await ObjectContext.OperatorGroups
                .AsNoTracking()
                .Where(x => x.Id == operatorGroupId)
                .SelectMany(x => x.Operators)
                .AnyAsync(x => x.Id == operatorId);
        }

        var allGroups = await ObjectContext.OperatorGroups
            .AsNoTracking()
            .Include(x => x.Operators)
            .Select(x => new {x.Id, x.ParentId, OperatorIds = x.Operators.Select(o => o.Id).ToList()})
            .ToListAsync();

        var groupsDict = allGroups.ToDictionary(g => g.Id);
        var groupsToCheck = new Queue<Guid>();
        var checkedGroups = new HashSet<Guid>();

        groupsToCheck.Enqueue(operatorGroupId);

        while (groupsToCheck.Count > 0)
        {
            var currentGroupId = groupsToCheck.Dequeue();

            if (checkedGroups.Contains(currentGroupId))
                continue;

            checkedGroups.Add(currentGroupId);

            if (!groupsDict.TryGetValue(currentGroupId, out var group)) continue;

            // Проверяем операторов в текущей группе
            if (group.OperatorIds.Contains(operatorId))
                return true;

            // Добавляем дочерние группы для проверки
            var childGroups = allGroups.Where(g => g.ParentId == currentGroupId);
            foreach (var childGroup in childGroups)
            {
                if (!checkedGroups.Contains(childGroup.Id))
                    groupsToCheck.Enqueue(childGroup.Id);
            }
        }

        return false;
    }

    public async Task<Guid[]> GetOperatorGroupsIdsWithParentGroupsInНierarchy(Guid operatorId)
    {
        //select group first level
        var currentLevelGroupIds = await ObjectContext.Operators
            .Where(o => o.Id == operatorId)
            .SelectMany(g => g.OperatorGroups)
            .Select(sg => sg.Id)
            .ToListAsync();

        var result = new List<Guid>();
        while (currentLevelGroupIds.Count > 0)
        {
            result.AddRange(currentLevelGroupIds);

            var clg = currentLevelGroupIds.ToArray();
            currentLevelGroupIds = await ObjectContext.OperatorGroups
                .Where(p => clg.Contains(p.Id) && p.ParentId.HasValue && !result.Contains(p.ParentId.Value))
                .Select(g => g.ParentId.Value)
                .Distinct()
                .ToListAsync();
        }

        return result.ToArray();
    }

    public async Task<Guid[]> GetOperatorGroupIds(string[] groupNames)
    {
        return await ObjectSet
            .Where(p => groupNames.Contains(p.Name))
            .Select(o => o.Id)
            .ToArrayAsync();
    }

    private async Task UpdateOperatorMapping(Platform.AWP.DataContracts.Infrastructure.OperatorGroup dataContract)
    {
        var group = await ObjectContext.OperatorGroups.SingleAsync(x => x.Id == dataContract.Id);

        var opgOperatorsIds = dataContract.OperatorsIds;

        Operator[] operators;
        if (opgOperatorsIds == null || opgOperatorsIds.Count == 0)
        {
            operators = Array.Empty<Operator>();
        }
        else
        {
            operators = await ObjectContext.Operators.Where(x => opgOperatorsIds.Contains(x.Id)).ToArrayAsync();
        }

        group.Operators ??= new List<Operator>();

        foreach (var addedOperator in operators)
        {
            if (group.Operators.All(x => x.Id != addedOperator.Id))
            {
                group.Operators.Add(addedOperator);
            }
        }

        var toRemove = new List<Operator>();
        foreach (var existOperator in group.Operators)
        {
            if (operators.All(x => x.Id != existOperator.Id))
            {
                toRemove.Add(existOperator);
            }
        }

        foreach (var itemToRemove in toRemove)
        {
            group.Operators.Remove(itemToRemove);
        }

        await ObjectContext.SaveChangesAsync();
    }
}
