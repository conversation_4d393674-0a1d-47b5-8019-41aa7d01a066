using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Platform.Logging.MicrosoftExtensions;
using Product.AWP.Infrastructure.DAL.Converters;
using Product.AWP.Infrastructure.DAL.Entities;
using Product.AWP.Infrastructure.DAL.Interfaces;
using Product.AWP.Infrastructure.DAL.Interfaces.Repositories;

namespace Product.AWP.Infrastructure.DAL.Repositories;

internal sealed class ConfigurationVersionRepository : BaseRepository<ConfigurationVersion, Platform.AWP.DataContracts.Infrastructure.ConfigurationVersion>, IConfigurationVersionRepository
{
    public ConfigurationVersionRepository(ILogger<ConfigurationVersionRepository> logger, IInfrastructureDataModel objectContext) : base(logger, objectContext, new ConfigurationVersionToDtoSymmetricConverter())
    {
    }

    public async Task UpdateVersion(string key, bool useStartsWith = false)
    {
        try
        {
            if (useStartsWith)
            {
                var keysToUpdate = await ObjectContext.ConfigurationVersions
                    .Where(cv => cv.Name.StartsWith(key))
                    .ToListAsync();
                foreach (var configurationVersion in keysToUpdate)
                {
                    UpdateKeyVersion(null, configurationVersion);
                }
            }
            else
            {
                var currentVersion = await ObjectContext.ConfigurationVersions
                    .FirstOrDefaultAsync(cv => cv.Name.Equals(key));
                UpdateKeyVersion(key, currentVersion);
            }

            await ObjectContext.SaveChangesAsync();
        }
        catch (Exception exc)
        {
            Logger.ErrorDev(exc, "UpdateVersion");
            throw;
        }
    }

    private void UpdateKeyVersion(string key, ConfigurationVersion currentVersion)
    {
        ConfigurationVersion newVersion;
        if (currentVersion == null)
        {
            newVersion = new ConfigurationVersion
            {
                Id = Guid.NewGuid(),
                Name = key
            };

            ObjectContext.ConfigurationVersions.Add(newVersion);
        }
        else
        {
            newVersion = currentVersion;
        }

        newVersion.VersionMarker = Guid.NewGuid();
    }
}
