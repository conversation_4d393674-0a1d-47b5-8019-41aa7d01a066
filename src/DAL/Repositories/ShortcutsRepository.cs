using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Platform.AWP.DataContracts.Infrastructure;
using Platform.AWP.DataContracts.Infrastructure.ShortcutActions;
using Platform.Common;
using Platform.Logging.MicrosoftExtensions;
using Product.AWP.Infrastructure.DAL.Converters;
using Product.AWP.Infrastructure.DAL.Entities;
using Product.AWP.Infrastructure.DAL.Interfaces;
using Product.AWP.Infrastructure.DAL.Interfaces.Repositories;
using Shortcut = Product.AWP.Infrastructure.DAL.Entities.Shortcut;
using UserShortcut = Product.AWP.Infrastructure.DAL.Entities.UserShortcut;

namespace Product.AWP.Infrastructure.DAL.Repositories;

internal sealed class ShortcutsRepository : BaseRepository<Shortcut, Platform.AWP.DataContracts.Infrastructure.Shortcut>, IShortcutsRepository
{
    public ShortcutsRepository(ILogger<ShortcutsRepository> logger, IInfrastructureDataModel objectContext) : base(logger, objectContext, new ShortcutToDtoSymmetricConverter())
    {
    }

    public async Task<Platform.AWP.DataContracts.Infrastructure.Shortcut[]> GetShortcutsForCurrentUser(Guid? serviceAreaId, Guid userRolesId)
    {
        using var mlh = this.CreateMethodLogHelper(Logger).WithArgs("userName={serviceAreaId}, userRolesId={userRolesId}", serviceAreaId, userRolesId);

        try
        {
            var entities =
                (await ObjectContext.Shortcuts
                    .AsNoTracking()
                    .Include(x => x.Application)
                    .Include(x => x.UserRoles)
                    .ToArrayAsync())
                .Where(x => IsEntityAvailableToUserRole(x, userRolesId))
                .ToArray();

            var result = entities.Select(Converter.ConvertEntityToDto).ToList();

            var serviceAreaConfigs = await GetServiceAreaConfigurations(serviceAreaId, result.Select(sc => sc.Id).ToList(), AddFilterByShortcutList);
            ExtendedPropertiesMapper.FillProperties(serviceAreaConfigs, result);

            return result.Where(x => !x.Disabled).ToArray();
        }
        catch (Exception exc)
        {
            mlh.LogMethodError(exc, "GetShortcutsForCurrentUser");
            throw;
        }
    }

    public async Task<Platform.AWP.DataContracts.Infrastructure.UserShortcut[]> GetCurrentUserCustomShortcuts(string userName)
    {
        using var mlh = this.CreateMethodLogHelper(Logger).WithArgs("userName={userName}", userName);

        try
        {
            var userShortcuts = await ObjectContext.UserShortcuts.AsNoTracking().Where(sc => sc.UserName == userName).ToListAsync();
            return userShortcuts.Select(ConvertFromUserShortcut).ToArray();
        }
        catch (Exception exc)
        {
            mlh.LogMethodError(exc, "GetCurrentUserCustomShortcuts");
            throw;
        }
    }

    public async Task AddCurrentUserCustomShortcut(Platform.AWP.DataContracts.Infrastructure.UserShortcut shortcut, string userName)
    {
        var userShortcut = new UserShortcut
        {
            Id = Guid.NewGuid(),
            Name = shortcut.Name,
            UserName = userName,
            Action = shortcut.Action.XmlSerialize(),
            Description = shortcut.Description,
            Type = (int) ShortcutType.CustomUserShortcut,
            SortOrder = shortcut.SortOrder
        };
        ObjectContext.UserShortcuts.Add(userShortcut);
        await ObjectContext.SaveChangesAsync();
    }

    public async Task UpdateCurrentUserCustomShortcut(Platform.AWP.DataContracts.Infrastructure.UserShortcut shortcut)
    {
        var userShortcut = await ObjectContext.UserShortcuts.FirstOrDefaultAsync(sc => sc.Id == shortcut.Id);
        if (userShortcut == null)
        {
            throw new ArgumentException($"Custom user shortcut with id {shortcut.Id} not found in database");
        }

        userShortcut.Name = shortcut.Name;
        userShortcut.Action = shortcut.Action.XmlSerialize();
        userShortcut.Description = shortcut.Description;
        userShortcut.Type = (int) ShortcutType.CustomUserShortcut;
        userShortcut.SortOrder = shortcut.SortOrder;
        await ObjectContext.SaveChangesAsync();
    }

    public async Task RemoveCurrentUserCustomShortcut(Guid userShortcutId)
    {
        var shortcut = await ObjectContext.UserShortcuts.FirstOrDefaultAsync(sc => sc.Id == userShortcutId);
        if (shortcut == null)
        {
            throw new ArgumentException($"Custom user shortcut with id {userShortcutId} not found in database");
        }

        ObjectContext.UserShortcuts.Remove(shortcut);

        await ObjectContext.SaveChangesAsync();
    }

    private Platform.AWP.DataContracts.Infrastructure.UserShortcut ConvertFromUserShortcut(UserShortcut userShortcut)
    {
        using var mlh = this.CreateMethodLogHelper(Logger).WithArgs("userShortcut={userShortcut}", userShortcut);

        var result = new Platform.AWP.DataContracts.Infrastructure.UserShortcut();
        try
        {
            result.Action = SerializationHelper.XmlDeserialize<CustomUserShortcutAction>(userShortcut.Action);
        }
        catch (Exception e)
        {
            mlh.LogMethodError(e, "Failed to deserialize shortcut action");
        }

        result.Description = userShortcut.Description;
        result.Id = userShortcut.Id;
        result.Name = userShortcut.Name;
        result.SortOrder = userShortcut.SortOrder;
        result.Type = ShortcutType.CustomUserShortcut;
        result.UserName = userShortcut.UserName;
        return result;
    }

    private static IQueryable<ServiceAreaConfiguration> AddFilterByShortcutList(IQueryable<ServiceAreaConfiguration> currentQuery, Guid[] shortcutIds)
    {
        return currentQuery?.Where(sac => sac.ShortcutId.HasValue && shortcutIds.Contains(sac.ShortcutId.Value));
    }
}
