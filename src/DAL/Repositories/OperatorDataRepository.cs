using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Platform.Common;
using Product.AWP.Infrastructure.DAL.Converters;
using Product.AWP.Infrastructure.DAL.Entities;
using Product.AWP.Infrastructure.DAL.Interfaces;
using Product.AWP.Infrastructure.DAL.Interfaces.Repositories;

namespace Product.AWP.Infrastructure.DAL.Repositories;

internal sealed class OperatorDataRepository : BaseRepository<Operator, Platform.AWP.DataContracts.Infrastructure.Operator>, IOperatorDataRepository
{
    private readonly OperatorCustomAttributeConverter _customAttributeConverter;

    public OperatorDataRepository(ILogger<OperatorDataRepository> logger, IInfrastructureDataModel objectContext) : base(logger, objectContext, new OperatorConverter())
    {
        _customAttributeConverter = new OperatorCustomAttributeConverter();
    }

    public override async Task Add(Platform.AWP.DataContracts.Infrastructure.Operator dataContract)
    {
        var activeDirectoryId = dataContract.ActiveDirectoryId;
        var userName = dataContract.UserName.ToLower();

        var existingOperator = await ObjectContext.Operators.FirstOrDefaultAsync(x =>
            x.ActiveDirectoryId == activeDirectoryId && x.UserName.ToLower() == userName);
        if (existingOperator != null)
        {
            throw new InvalidOperationException("Operator with the same ActiveDirectoryId already exist!");
        }

        await base.Add(dataContract);
    }

    public async Task<Platform.AWP.DataContracts.Infrastructure.Operator[]> FindByUserNameContains(string userName)
    {
        if (string.IsNullOrEmpty(userName))
        {
            return null;
        }

        var usernameParameter = userName.ToLower();
        return (await IncludeLinkedEntities(ObjectSet)
                .AsNoTracking()
                .Where(x => !string.IsNullOrEmpty(x.UserName) && x.UserName.ToLower().Contains(usernameParameter))
                .ToArrayAsync())
            .Select(Converter.ConvertEntityToDto).ToArray();
        ;
    }

    public async Task<Platform.AWP.DataContracts.Infrastructure.Operator> FindByActiveDirectoryId(Guid activeDirectoryId)
    {
        return (await IncludeLinkedEntities(ObjectSet)
                .AsNoTracking()
                .FirstOrDefaultAsync(p => p.ActiveDirectoryId == activeDirectoryId))
            .IfNotNull(Converter.ConvertEntityToDto);
    }

    public async Task<Platform.AWP.DataContracts.Infrastructure.Operator[]> FindByActiveDirectoryIds(Guid[] activeDirectoryIds)
    {
        if (activeDirectoryIds == null) throw new ArgumentNullException(nameof(activeDirectoryIds));
        if (activeDirectoryIds.Length == 0) return Array.Empty<Platform.AWP.DataContracts.Infrastructure.Operator>();

        var query = IncludeLinkedEntities(ObjectSet).AsNoTracking();
        if (activeDirectoryIds.Length == 1)
        {
            var adId = activeDirectoryIds[0];
            query = query.Where(x => x.ActiveDirectoryId == adId);
        }
        else
        {
            var adIds = activeDirectoryIds.ToArray();
            query = query.Where(x => adIds.Contains(x.ActiveDirectoryId));
        }

        return (await query.ToListAsync()).Select(Converter.ConvertEntityToDto).ToArray();
    }

    public async Task<Platform.AWP.DataContracts.Infrastructure.Operator[]> GetOperatorsInGroups(IList<Guid> operatorGroupsIds)
    {
        var operatorGroupIdsArray = operatorGroupsIds.ToArray();
        return (await IncludeLinkedEntities(ObjectSet)
                .AsNoTracking()
                .Where(x => x.OperatorGroupLinks.Any(op => operatorGroupIdsArray.Contains(op.OperatorGroupId)))
                .ToArrayAsync())
            .Select(Converter.ConvertEntityToDto).ToArray();
    }

    public async Task AddOrUpdateCustomAttributes(IList<Platform.AWP.DataContracts.Infrastructure.OperatorCustomAttribute> attributes)
    {
        if (attributes == null || attributes.Count == 0)
            return;

        var operatorIds = attributes.Select(a => a.OperatorId).Distinct().ToArray();
        var codes = attributes.Select(a => a.Code).Distinct().ToArray();

        var existingAttributes = await ObjectContext.OperatorsCustomAttributes
            .Where(x => operatorIds.Contains(x.OperatorId) && codes.Contains(x.Code))
            .ToListAsync();

        var existingAttributesDict = existingAttributes
            .ToDictionary(x => $"{x.OperatorId}_{x.Code}");

        foreach (var attr in attributes)
        {
            var key = $"{attr.OperatorId}_{attr.Code}";

            if (existingAttributesDict.TryGetValue(key, out var existAttribute))
            {
                _customAttributeConverter.MergeChanges(attr, existAttribute);
            }
            else
            {
                var newAttribute = new OperatorCustomAttribute();
                _customAttributeConverter.MergeChanges(attr, newAttribute);
                await ObjectContext.OperatorsCustomAttributes.AddAsync(newAttribute);
            }
        }

        await ObjectContext.SaveChangesAsync();
    }

    public async Task RemoveCustomAttribute(Guid operatorId, IList<string> codes)
    {
        var query = ObjectContext.OperatorsCustomAttributes.Where(x => x.OperatorId == operatorId);
        if (codes != null)
        {
            var localCodes = codes.ToArray();
            query = query.Where(x => localCodes.Contains(x.Code));
        }

        var attrsFromDb = await query.ToArrayAsync();
        foreach (var entity in attrsFromDb)
        {
            ObjectContext.OperatorsCustomAttributes.Remove(entity);
        }

        await ObjectContext.SaveChangesAsync();
    }

    public async Task<Platform.AWP.DataContracts.Infrastructure.OperatorCustomAttribute[]> GetCustomAttributes(IList<Guid> operatorIds, IList<string> codes)
    {
        var operatorIdsArray = operatorIds.ToArray();

        var query = ObjectContext.OperatorsCustomAttributes.Where(x => operatorIdsArray.Contains(x.OperatorId));
        if (codes != null)
        {
            var localCodes = codes.ToArray();
            query = query.Where(x => localCodes.Contains(x.Code));
        }

        return (await query.AsNoTracking().ToArrayAsync()).Select(x => _customAttributeConverter.ConvertEntityToDto(x)).ToArray();
    }

    public async Task<Platform.AWP.DataContracts.Infrastructure.Operator[]> FindByStringCa(string caCode, string caValue)
    {
        var query = ObjectContext.OperatorsCustomAttributes
            .AsNoTracking()
            .Where(x => x.Code == caCode && x.StringValue == caValue)
            .Select(x => x.Operator);

        return (await query.ToArrayAsync()).Select(x => Converter.ConvertEntityToDto(x)).ToArray();
    }

    public async Task<Platform.AWP.DataContracts.Infrastructure.Operator[]> FindByGuidCa(string caCode, Guid? caValue)
    {
        var query = ObjectContext.OperatorsCustomAttributes
            .AsNoTracking()
            .Where(x => x.Code == caCode && x.GuidValue == caValue)
            .Select(x => x.Operator);

        return (await query.ToArrayAsync()).Select(x => Converter.ConvertEntityToDto(x)).ToArray();
    }

    public async Task<Platform.AWP.DataContracts.Infrastructure.Operator[]> FindByLongCa(string caCode, long? caValue)
    {
        var query = ObjectContext.OperatorsCustomAttributes
            .AsNoTracking()
            .Where(x => x.Code == caCode && x.LongValue == caValue)
            .Select(x => x.Operator);

        return (await query.ToArrayAsync()).Select(x => Converter.ConvertEntityToDto(x)).ToArray();
    }

    public async Task<Platform.AWP.DataContracts.Infrastructure.Operator[]> FindByDecimalCa(string caCode, decimal? caValue)
    {
        var query = ObjectContext.OperatorsCustomAttributes
            .AsNoTracking()
            .Where(x => x.Code == caCode && x.DecimalValue == caValue)
            .Select(x => x.Operator);

        return (await query.ToArrayAsync()).Select(x => Converter.ConvertEntityToDto(x)).ToArray();
    }

    public async Task<Platform.AWP.DataContracts.Infrastructure.Operator[]> FindByBoolCa(string caCode, bool? caValue)
    {
        var query = ObjectContext.OperatorsCustomAttributes
            .AsNoTracking()
            .Where(x => x.Code == caCode && x.BoolValue == caValue)
            .Select(x => x.Operator);

        return (await query.ToArrayAsync()).Select(x => Converter.ConvertEntityToDto(x)).ToArray();
    }

    public async Task<Platform.AWP.DataContracts.Infrastructure.Operator[]> FindByDateTimeCa(string caCode, DateTime? caValue)
    {
        var query = ObjectContext.OperatorsCustomAttributes
            .AsNoTracking()
            .Where(x => x.Code == caCode && x.DateTimeValue == caValue)
            .Select(x => x.Operator);

        return (await query.ToArrayAsync()).Select(x => Converter.ConvertEntityToDto(x)).ToArray();
    }

    public async Task RemoveCustomAttributes(Guid[] operatorsIds, string[] caCodes)
    {
        if (operatorsIds?.Length == 0)
        {
            return;
        }

        if (caCodes?.Length == 0)
        {
            return;
        }


        IQueryable<OperatorCustomAttribute> query;
        if (operatorsIds == null && caCodes == null)
        {
            query = ObjectContext.OperatorsCustomAttributes;
        }
        else if (operatorsIds == null)
        {
            var codesArr = caCodes;
            query = ObjectContext.OperatorsCustomAttributes.Where(x => codesArr.Contains(x.Code));
        }
        else if (caCodes == null)
        {
            var operatorsIdsArr = operatorsIds;
            query = ObjectContext.OperatorsCustomAttributes.Where(x => operatorsIdsArr.Contains(x.OperatorId));
        }
        else
        {
            var codesArr = caCodes;
            var operatorsIdsArr = operatorsIds;
            query = ObjectContext.OperatorsCustomAttributes.Where(x => operatorsIdsArr.Contains(x.OperatorId) && codesArr.Contains(x.Code));
        }

        await query.ExecuteDeleteAsync();

        await ObjectContext.SaveChangesAsync();
    }

    protected override IQueryable<Operator> IncludeLinkedEntities(IQueryable<Operator> query)
    {
        return base.IncludeLinkedEntities(query)
            .Include(x => x.OperatorGroupLinks)
            .Include(x => x.CustomAttributes);
    }
}
