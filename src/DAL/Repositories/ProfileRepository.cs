using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Platform.AWP.DataContracts.Infrastructure;
using Platform.Logging.MicrosoftExtensions;
using Product.AWP.Infrastructure.DAL.Converters;
using Product.AWP.Infrastructure.DAL.Interfaces;
using Product.AWP.Infrastructure.DAL.Interfaces.Repositories;

namespace Product.AWP.Infrastructure.DAL.Repositories;

internal sealed class ProfileRepository : BaseRepository<Entities.UserGroup, ProfileData>, IProfileRepository
{
    private readonly ServiceAreaConverter _serviceAreaConverter = new();
    private readonly UserRoleToDtoSymmetricConverter _userRoleConverter = new();
    private readonly WorkplaceToDtoConverter _workplaceConverter = new();

    public ProfileRepository(ILogger<ProfileRepository> logger, IInfrastructureDataModel objectContext) : base(logger, objectContext, new ProfileConverter())
    {
    }

    public async Task<ProfileData[]> GetProfiles(Guid[] userGroupId)
    {
        var result = await ObjectContext.UserGroups
            .AsNoTracking()
            .Include(x => x.UserRoles).ThenInclude(x => x.CustomAttributes)
            .Include(x => x.Workplaces).ThenInclude(x => x.CustomAttributes)
            .Include(x => x.ServiceAreas).ThenInclude(x => x.CustomAttributes)
            .Where(x => userGroupId.Contains(x.Id))
            .ToListAsync();

        return result.Select(p => Converter.ConvertEntityToDto(p)).ToArray();
    }

    public async Task<UserRoleWithNoUserGroups[]> GetUserRolesWithNoUserGroups()
    {
        using var mlh = this.CreateMethodLogHelper(Logger).WithNoArgs();
        try
        {
            var query = ObjectContext.UserRoles.AsNoTracking().Include(x => x.Children).Include(x => x.CustomAttributes);
            return (await query.ToArrayAsync())
                .Select(x => _userRoleConverter.ConvertEntityToDto(x, true, true, false) as UserRoleWithNoUserGroups)
                .ToArray();
        }
        catch (Exception exc)
        {
            mlh.LogMethodError(exc, "GetWorkplaces");
            throw;
        }
    }

    public async Task<ServiceAreaWithNoUserGroups[]> GetServiceAreasWithNoUserGroups()
    {
        using var mlh = this.CreateMethodLogHelper(Logger).WithNoArgs();
        try
        {
            var query = ObjectContext.ServiceAreas.AsNoTracking().Include(x => x.Children).Include(x => x.CustomAttributes);
            return (await query.ToArrayAsync())
                .Select(x => _serviceAreaConverter.ConvertEntityToDto(x, true, true, false) as ServiceAreaWithNoUserGroups)
                .ToArray();
        }
        catch (Exception exc)
        {
            mlh.LogMethodError(exc, "GetWorkplaces");
            throw;
        }
    }

    public async Task<WorkplaceWithNoUserGroups[]> GetWorkplacesWithNoUserGroups()
    {
        using var mlh = this.CreateMethodLogHelper(Logger).WithNoArgs();
        try
        {
            var query = ObjectContext.Workplaces.AsNoTracking().Include(x => x.CustomAttributes).Include(x => x.Children);
            return (await query.ToArrayAsync())
                .Select(x => _workplaceConverter.ConvertEntityToDto(x, true, false) as WorkplaceWithNoUserGroups)
                .ToArray();
        }
        catch (Exception exc)
        {
            mlh.LogMethodError(exc, "GetWorkplaces");
            throw;
        }
    }
}
