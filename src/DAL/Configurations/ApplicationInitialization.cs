using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Product.AWP.Infrastructure.DAL.Configurations.BaseConfigurations;

namespace Product.AWP.Infrastructure.DAL.Configurations;

internal class ApplicationInitialization : HasIdEntityBase<Entities.ApplicationInitialization>
{
    public ApplicationInitialization(string schema = null)
        : base("ApplicationInitializations", schema)
    {
    }

    protected override void ConfigureProperties(EntityTypeBuilder<Entities.ApplicationInitialization> builder)
    {
        builder.Property(x => x.DisplayGroup).HasColumnName("DisplayGroup").IsRequired();
        builder.Property(x => x.AdapterInitialization).HasColumnName("AdapterInitialization").IsRequired(false);

        builder.OwnsOne(
            x => x.Adapter,
            ad =>
            {
                ad.Property(x => x.Assembly).HasColumnName("Adapter_Assembly").IsRequired(false);
                ad.Property(x => x.TypeName).HasColumnName("Adapter_TypeName").IsRequired(false);
            });

        builder.Property(x => x.StartAsync).HasColumnName("StartAsync").IsRequired();

        builder.OwnsOne(
            x => x.ExpanderSettings,
            es =>
            {
                es.Property(x => x.WrapInExpander).HasColumnName("Expander_WrapInExpander").IsRequired();
                es.Property(x => x.InitiallyExpanded).HasColumnName("Expander_InitiallyExpanded").IsRequired();
                es.Property(x => x.Header).HasColumnName("Expander_Header").IsRequired(false);
                es.Property(x => x.ExpandDirection).HasColumnName("Expander_ExpandDirection").IsRequired();
                es.Property(x => x.ExpandDisplayMode).HasColumnName("Expander_ExpandDisplayMode").IsRequired();
            });

        builder.OwnsOne(
            x => x.SizeSettings,
            ss =>
            {
                ss.Property(x => x.Width).HasColumnName("SizeSettings_Width").IsRequired(false);
                ss.Property(x => x.Height).HasColumnName("SizeSettings_Height").IsRequired(false);
                ss.Property(x => x.MinWidth).HasColumnName("SizeSettings_MinWidth").IsRequired(false);
                ss.Property(x => x.MinHeight).HasColumnName("SizeSettings_MinHeight").IsRequired(false);
                ss.Property(x => x.MaxWidth).HasColumnName("SizeSettings_MaxWidth").IsRequired(false);
                ss.Property(x => x.MaxHeight).HasColumnName("SizeSettings_MaxHeight").IsRequired(false);
            });

        builder.Property(x => x.HideInTaskbar).HasColumnName("HideInTaskbar").IsRequired();
        builder.Property(x => x.HideOnStartup).HasColumnName("HideOnStartup").IsRequired();

        //	foreign keys
        //builder.HasOne(x => x.Application).WithOne(x => x.ApplicationInitialization).IsRequired();
        builder.HasOne(x => x.Application).WithOne(x => x.ApplicationInitialization).HasForeignKey(typeof(Entities.ApplicationInitialization), "Id").IsRequired();
    }
}

internal class WebClientApplicationInitialization : HasIdEntityBase<Entities.WebClientApplicationInitialization>
{
    public WebClientApplicationInitialization(string schema = null)
        : base("WebClientAppInits", schema)
    {
    }

    protected override void ConfigureProperties(EntityTypeBuilder<Entities.WebClientApplicationInitialization> builder)
    {
        builder.Property(x => x.DisplayGroup).HasColumnName("DisplayGroup").IsRequired();
        builder.Property(x => x.ComponentName).HasColumnName("ComponentName").IsRequired();
        builder.Property(x => x.ComponentInit).HasColumnName("ComponentInit").IsRequired(false);

        //	foreign keys
        //builder.HasOne(x => x.Application).WithOne(x => x.ApplicationInitialization).IsRequired();
        builder.HasOne(x => x.Application).WithOne(x => x.WebClientApplicationInitialization).HasForeignKey(typeof(Entities.WebClientApplicationInitialization), "Id").IsRequired();
    }
}
