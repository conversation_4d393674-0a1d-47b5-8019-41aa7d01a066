using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Product.AWP.Infrastructure.DAL.Configurations.BaseConfigurations;

namespace Product.AWP.Infrastructure.DAL.Configurations;

internal class ServiceArea : HasIdEntityBase<Entities.ServiceArea>
{
    public ServiceArea(string schema = null)
        : base("ServiceAreas", schema)
    {
    }

    protected override void ConfigureProperties(EntityTypeBuilder<Entities.ServiceArea> builder)
    {
        builder.Property(x => x.Name).HasColumnName("Name").IsRequired();
        builder.Property(x => x.UtcOffset).HasColumnName("UtcOffset").IsRequired();
        builder.Property(x => x.Code).HasColumnName("Code").HasMaxLength(50).IsRequired();

        builder.Property(x => x.ParentId).HasColumnName("ParentId").IsRequired(false);

        builder.HasOne(x => x.ServiceAreaType).WithMany(x => x.ServiceAreas).HasForeignKey(x => x.ServiceAreaTypeId).IsRequired(false);
        builder.HasOne(x => x.Parent).WithMany(x => x.Children).HasForeignKey(x => x.ParentId).IsRequired(false);
    }
}
