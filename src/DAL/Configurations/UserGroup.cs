using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Product.AWP.Infrastructure.DAL.Configurations.BaseConfigurations;

namespace Product.AWP.Infrastructure.DAL.Configurations;

internal class UserGroup : HasIdEntityBase<Entities.UserGroup>
{
    public UserGroup(string schema = null)
        : base("UserGroups", schema)
    {
    }

    protected override void ConfigureProperties(EntityTypeBuilder<Entities.UserGroup> builder)
    {
        builder.Property(x => x.Name).HasColumnName("Name").HasMaxLength(50).IsRequired();
        builder.Property(x => x.Description).HasColumnName("Description").HasMaxLength(1000).IsRequired(false);

        //	foreign keys
        builder.HasMany(x => x.ServiceAreas).WithMany(x => x.UserGroups)
            .UsingEntity<Dictionary<string, object>>(
                "UserGroupServiceAreas",
                x => x.HasOne<Entities.ServiceArea>().WithMany().HasForeignKey("ServiceAreaId"),
                x => x.HasOne<Entities.UserGroup>().WithMany().HasForeignKey("UserGroupId"),
                jet => jet.ToTable("UserGroupServiceAreas", Schema)
            );
        builder.HasMany(x => x.UserRoles).WithMany(x => x.UserGroups)
            .UsingEntity<Dictionary<string, object>>(
                "UserGroupUserRoles",
                x => x.HasOne<Entities.UserRole>().WithMany().HasForeignKey("UserRoleId"),
                x => x.HasOne<Entities.UserGroup>().WithMany().HasForeignKey("UserGroupId"),
                jet => jet.ToTable("UserGroupUserRoles", Schema)
            );
        builder.HasMany(x => x.Workplaces).WithMany(x => x.UserGroups)
            .UsingEntity<Dictionary<string, object>>(
                "USERGROUP_WORKPLACES",
                x => x.HasOne<Entities.Workplace>().WithMany().HasForeignKey("WORKPLACE_ID"),
                x => x.HasOne<Entities.UserGroup>().WithMany().HasForeignKey("USERGROUP_ID"),
                jet => jet.ToTable("USERGROUP_WORKPLACES", Schema)
            );
        builder.HasMany(x => x.Identities).WithMany(x => x.UserGroups)
            .UsingEntity<Dictionary<string, object>>(
                "USERGROUP_IDENTITIES",
                x => x.HasOne<Entities.Identity>().WithMany().HasForeignKey("IDENTITY_ID"),
                x => x.HasOne<Entities.UserGroup>().WithMany().HasForeignKey("USERGROUP_ID"),
                jet => jet.ToTable("USERGROUP_IDENTITIES", Schema)
            );
    }
}
