using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Product.AWP.Infrastructure.DAL.Configurations.BaseConfigurations;

namespace Product.AWP.Infrastructure.DAL.Configurations;

internal class Configuration : HasIdEntityBase<Entities.Configuration>
{
    public Configuration(string schema = null)
        : base("Configurations", schema)
    {
    }

    protected override void ConfigureProperties(EntityTypeBuilder<Entities.Configuration> builder)
    {
        builder.Property(x => x.Name).HasColumnName("Name").HasMaxLength(255).IsRequired(false);
        builder.Property(x => x.Value).HasColumnName("Value").IsRequired(false);
        builder.Property(x => x.Description).HasColumnName("Description").HasMaxLength(1000).IsRequired(false);
    }
}
