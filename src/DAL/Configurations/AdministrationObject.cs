using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Product.AWP.Infrastructure.DAL.Configurations.BaseConfigurations;

namespace Product.AWP.Infrastructure.DAL.Configurations;

internal class AdministrationObject : HasIdEntityBase<Entities.AdministrationObject>
{
    public AdministrationObject(string schema = null)
        : base("AdministrationObjects", schema)
    {
    }

    protected override void ConfigureProperties(EntityTypeBuilder<Entities.AdministrationObject> builder)
    {
        builder.Property(x => x.Name).HasColumnName("Name").IsRequired();
        builder.Property(x => x.ModuleName).HasColumnName("ModuleName").IsRequired();
    }
}
