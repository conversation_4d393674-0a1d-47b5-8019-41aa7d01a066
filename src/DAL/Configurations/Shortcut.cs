using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Product.AWP.Infrastructure.DAL.Configurations.BaseConfigurations;

namespace Product.AWP.Infrastructure.DAL.Configurations;

internal class Shortcut : HasReadOnlyIdEntityBase<Entities.Shortcut>
{
    public Shortcut(string schema = null)
        : base("BaseShortcuts_Shortcut", schema)
    {
    }

    protected override void ConfigureKey(EntityTypeBuilder<Entities.Shortcut> builder)
    {
        //base.ConfigureKey(builder);
    }

    protected override void ConfigureProperties(EntityTypeBuilder<Entities.Shortcut> builder)
    {
        builder.Property(x => x.ApplicationId).HasColumnName("ApplicationId").IsRequired(false);
        builder.Property(x => x.Disabled).HasColumnName("DISABLED").IsRequired();

        builder.HasOne(x => x.Application).WithMany(x => x.Shortcuts).HasForeignKey(x => x.ApplicationId).IsRequired(false);
        builder.HasMany(e => e.UserRoles).WithMany(e => e.Shortcuts)
            .UsingEntity<Dictionary<string, object>>(
                "ShortcutUserRole",
                x => x.HasOne<Entities.UserRole>().WithMany().HasForeignKey("UserRoleId"),
                x => x.HasOne<Entities.Shortcut>().WithMany().HasForeignKey("ShortcutId"),
                jet => jet.ToTable("ShortcutUserRole", Schema)
            );
    }
}
