using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Product.AWP.Infrastructure.DAL.Configurations.BaseConfigurations;

namespace Product.AWP.Infrastructure.DAL.Configurations;

internal class Operator : HasIdEntityBase<Entities.Operator>
{
    public Operator(string schema = null)
        : base("Operators", schema)
    {
    }

    protected override void ConfigureProperties(EntityTypeBuilder<Entities.Operator> builder)
    {
        builder.Property(x => x.FirstName).HasColumnName("FirstName").IsRequired(false);
        builder.Property(x => x.MiddleName).HasColumnName("MiddleName").IsRequired(false);
        builder.Property(x => x.LastName).HasColumnName("LastName").IsRequired(false);
        builder.Property(x => x.Nickname).HasColumnName("NICKNAME").HasMaxLength(300).IsRequired(false);
        builder.Property(x => x.UserName).HasColumnName("UserName").HasMaxLength(300).IsRequired();
        builder.Property(x => x.ActiveDirectoryId).HasColumnName("ACTIVE_DIRECTORY_ID").IsRequired();
    }
}
