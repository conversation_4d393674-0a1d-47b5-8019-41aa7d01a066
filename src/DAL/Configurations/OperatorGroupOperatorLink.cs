using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Product.AWP.Infrastructure.DAL.Configurations.BaseConfigurations;

namespace Product.AWP.Infrastructure.DAL.Configurations;

internal class OperatorGroupOperatorLink : EntityTypeConfigurationBase<Entities.OperatorGroupOperatorLink>
{
    public OperatorGroupOperatorLink(string schema = null)
        : base("OperatorGroupOperator", schema)
    {
    }

    protected override void ConfigureKey(EntityTypeBuilder<Entities.OperatorGroupOperatorLink> builder)
    {
        builder.Property(x => x.OperatorGroupId).HasColumnName(nameof(Entities.OperatorGroupOperatorLink.OperatorGroupId)).IsRequired();
        builder.Property(x => x.OperatorId).HasColumnName(nameof(Entities.OperatorGroupOperatorLink.OperatorId)).IsRequired();
        builder.HasKey(x => new {x.OperatorGroupId, x.OperatorId});
    }

    protected override void ConfigureProperties(EntityTypeBuilder<Entities.OperatorGroupOperatorLink> builder)
    {
        builder.HasIndex(x => x.OperatorGroupId);
        builder.HasIndex(x => x.OperatorId);

        builder.HasOne(x => x.OperatorGroup).WithMany(x => x.OperatorLinks).HasForeignKey(x => x.OperatorGroupId).IsRequired();
        builder.HasOne(x => x.Operator).WithMany(x => x.OperatorGroupLinks).HasForeignKey(x => x.OperatorId).IsRequired();
    }

    public static OperatorGroupOperatorLink CreateAndConfigure(EntityTypeBuilder<Entities.OperatorGroupOperatorLink> builder, string schema = null)
    {
        var result = new OperatorGroupOperatorLink(schema);
        result.Configure(builder);
        return result;
    }
}
