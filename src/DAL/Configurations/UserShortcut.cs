using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Product.AWP.Infrastructure.DAL.Configurations.BaseConfigurations;

namespace Product.AWP.Infrastructure.DAL.Configurations;

internal class UserShortcut : HasReadOnlyIdEntityBase<Entities.UserShortcut>
{
    public UserShortcut(string schema = null)
        : base("BaseShortcuts_UserShortcut", schema)
    {
    }

    protected override void ConfigureKey(EntityTypeBuilder<Entities.UserShortcut> builder)
    {
        //base.ConfigureKey(builder);
    }

    protected override void ConfigureProperties(EntityTypeBuilder<Entities.UserShortcut> builder)
    {
        builder.Property(x => x.UserName).HasColumnName("UserName").HasMaxLength(100).IsRequired();
    }
}
