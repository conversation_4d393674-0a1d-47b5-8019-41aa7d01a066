using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Product.AWP.Infrastructure.DAL.Configurations.BaseConfigurations;

namespace Product.AWP.Infrastructure.DAL.Configurations;

internal class OperatorGroupCustomAttribute : EntityTypeConfigurationBase<Entities.OperatorGroupCustomAttribute>
{
    public OperatorGroupCustomAttribute(string schema = null)
        : base("OPERATOR_GROUP_CA", schema)
    {
    }

    protected override void ConfigureKey(EntityTypeBuilder<Entities.OperatorGroupCustomAttribute> builder)
    {
        builder.Property(x => x.OperatorGroupId).HasColumnName("OPERATOR_GROUP_ID").IsRequired();
        builder.Property(x => x.Code).HasColumnName("CODE").HasMaxLength(100).IsRequired();
        builder.HasKey(x => new {x.OperatorGroupId, x.Code});
    }

    protected override void ConfigureProperties(EntityTypeBuilder<Entities.OperatorGroupCustomAttribute> builder)
    {
        builder.Property(x => x.StringValue).HasColumnName("STRING_VALUE").IsRequired(false);
        builder.Property(x => x.GuidValue).HasColumnName("GUID_VALUE").IsRequired(false);
        builder.Property(x => x.LongValue).HasColumnName("LONG_VALUE").IsRequired(false);
        builder.Property(x => x.DecimalValue).HasColumnName("DECIMAL_VALUE").IsRequired(false);
        builder.Property(x => x.BoolValue).HasColumnName("BOOL_VALUE").IsRequired(false);
        builder.Property(x => x.DateTimeValue).HasColumnName("DATETIME_VALUE").IsRequired(false);
        builder.Property(x => x.BinaryValue).HasColumnName("BINARY_VALUE").IsRequired(false);

        builder.HasOne(x => x.OperatorGroup).WithMany(x => x.CustomAttributes).HasForeignKey(x => x.OperatorGroupId).IsRequired();
    }
}
