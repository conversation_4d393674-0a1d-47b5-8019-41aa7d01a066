using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Product.AWP.Infrastructure.DAL.Configurations.BaseConfigurations;

namespace Product.AWP.Infrastructure.DAL.Configurations;

internal class Application : HasIdEntityBase<Entities.Application>
{
    public Application(string schema = null)
        : base("Applications", schema)
    {
    }

    protected override void ConfigureProperties(EntityTypeBuilder<Entities.Application> builder)
    {
        builder.Property(x => x.Name).HasColumnName("Name").HasMaxLength(100).IsRequired();
        builder.Property(x => x.Type).HasColumnName("Type").IsRequired();
        builder.Property(x => x.IsService).HasColumnName("IsService").IsRequired();
        builder.Property(x => x.SortOrder).HasColumnName("SortOrder").IsRequired();
        builder.Property(x => x.EnableAutoSignOn).HasColumnName("EnableAutoSignOn").IsRequired(false);
        builder.Property(x => x.Disabled).HasColumnName("Disabled").IsRequired(false);
        builder.Property(x => x.CredentialsConfigurationName).HasColumnName("CredentialsConfigurationName").IsRequired(false);
        builder.Property(x => x.ManualStart).HasColumnName("ManualStart").IsRequired();
        builder.Property(x => x.LaunchOrder).HasColumnName("LaunchOrder").IsRequired();
        builder.Property(x => x.ToolbarImage).HasColumnName("ToolbarImage").IsRequired(false);


        builder.HasMany(e => e.UserRoles).WithMany(e => e.Applications)
            .UsingEntity<Dictionary<string, object>>(
                "ApplicationUserRole",
                x => x.HasOne<Entities.UserRole>().WithMany().HasForeignKey("UserRoleId"),
                x => x.HasOne<Entities.Application>().WithMany().HasForeignKey("ApplicationId"),
                jet => jet.ToTable("ApplicationUserRole", Schema)
            );
    }
}
