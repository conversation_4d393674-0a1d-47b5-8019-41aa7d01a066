using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Product.AWP.Infrastructure.DAL.Configurations.BaseConfigurations;

namespace Product.AWP.Infrastructure.DAL.Configurations;

internal class Identity : HasIdEntityBase<Entities.Identity>
{
    public Identity(string schema = null)
        : base("IDENTITIES", schema)
    {
    }

    protected override void ConfigureProperties(EntityTypeBuilder<Entities.Identity> builder)
    {
        builder.Property(x => x.Login).HasColumnName("LOGIN").IsRequired().HasMaxLength(100);
    }
}
