using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Product.AWP.Infrastructure.DAL.Configurations.BaseConfigurations;

namespace Product.AWP.Infrastructure.DAL.Configurations;

internal class WebAppProxySetting : HasIdEntityBase<Entities.WebAppProxySetting>
{
    public WebAppProxySetting(string schema = null) : base("WebAppProxySettings", schema)
    {
    }

    protected override void ConfigureProperties(EntityTypeBuilder<Entities.WebAppProxySetting> builder)
    {
        builder.Property(x => x.WebApplicationId).HasColumnName(nameof(Entities.WebAppProxySetting.WebApplicationId)).IsRequired(false);
        builder.Property(x => x.OriginalUrl).HasColumnName(nameof(Entities.WebAppProxySetting.OriginalUrl)).HasMaxLength(2048).IsRequired(false);
        builder.Property(x => x.ProxyPort).HasColumnName(nameof(Entities.WebAppProxySetting.ProxyPort)).IsRequired();
        builder.Property(x => x.ScriptToInject).HasColumnName(nameof(Entities.WebAppProxySetting.ScriptToInject)).IsRequired(false);
        builder.Property(x => x.ProxyAdapterCode).HasColumnName(nameof(Entities.WebAppProxySetting.ProxyAdapterCode)).HasMaxLength(100).IsRequired(false);

        builder
            .HasOne(x => x.WebApplicationInitialization)
            .WithMany(x => x.WebAppProxySettings)
            .HasForeignKey(x => x.WebApplicationId)
            .IsRequired(false);
    }
}
