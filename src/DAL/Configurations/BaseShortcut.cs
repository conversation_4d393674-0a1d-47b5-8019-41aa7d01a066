using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Product.AWP.Infrastructure.DAL.Configurations.BaseConfigurations;

namespace Product.AWP.Infrastructure.DAL.Configurations;

internal class BaseShortcut : HasReadOnlyIdEntityBase<Entities.BaseShortcut>
{
    public BaseShortcut(string schema = null)
        : base("BaseShortcuts", schema)
    {
    }

    protected override void ConfigureProperties(EntityTypeBuilder<Entities.BaseShortcut> builder)
    {
        builder.Property(x => x.Name).HasColumnName("Name").HasMaxLength(100).IsRequired();
        builder.Property(x => x.Type).HasColumnName("Type").IsRequired();
        builder.Property(x => x.Action).HasColumnName("Action").IsRequired();
        builder.Property(x => x.Description).HasColumnName("Description").IsRequired(false);
        builder.Property(x => x.SortOrder).HasColumnName("SortOrder").IsRequired();
    }
}
