using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Product.AWP.Infrastructure.DAL.Configurations.BaseConfigurations;

namespace Product.AWP.Infrastructure.DAL.Configurations;

internal class ServiceAreaType : HasReadOnlyIdEntityBase<Entities.ServiceAreaType>
{
    public ServiceAreaType(string schema = null)
        : base("ServiceAreaTypes", schema)
    {
    }

    protected override void ConfigureProperties(EntityTypeBuilder<Entities.ServiceAreaType> builder)
    {
        builder.Property(x => x.Name).HasColumnName("Name").IsRequired();
    }
}
