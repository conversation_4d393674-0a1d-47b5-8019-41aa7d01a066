using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Product.AWP.Infrastructure.DAL.Configurations.BaseConfigurations;

namespace Product.AWP.Infrastructure.DAL.Configurations;

internal class OperatorGroup : HasIdEntityBase<Entities.OperatorGroup>
{
    public OperatorGroup(string schema = null)
        : base("OperatorGroups", schema)
    {
    }

    protected override void ConfigureProperties(EntityTypeBuilder<Entities.OperatorGroup> builder)
    {
        builder.Property(x => x.Name).HasColumnName("Name").IsRequired(false);
        builder.Property(x => x.ParentId).HasColumnName("ParentId").IsRequired(false);

        //builder.HasMany(e => e.Operators).WithMany(o => o.OperatorGroups)
        //	.UsingEntity<Dictionary<string, object>>(
        //		"OperatorGroupOperator",
        //		x => x.HasOne<Common.Model.Operator>().WithMany().HasForeignKey("OperatorId"),
        //		x => x.HasOne<Common.Model.OperatorGroup>().WithMany().HasForeignKey("OperatorGroupId"),
        //		jet => jet.ToTable("OperatorGroupOperator", Schema)
        //	);
        builder.HasMany(e => e.Operators).WithMany(o => o.OperatorGroups).UsingEntity<Entities.OperatorGroupOperatorLink>(b => OperatorGroupOperatorLink.CreateAndConfigure(b, Schema));
        builder.HasOne(x => x.Parent).WithMany(x => x.Children).HasForeignKey(x => x.ParentId).IsRequired(false);
    }
}
