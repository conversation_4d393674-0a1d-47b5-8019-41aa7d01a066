using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Product.AWP.Infrastructure.DAL.Configurations.BaseConfigurations;

namespace Product.AWP.Infrastructure.DAL.Configurations;

internal class CustomAttribute : HasIdEntityBase<Entities.CustomAttribute>
{
    public CustomAttribute(string schema = null)
        : base("CustomAttributes", schema)
    {
    }

    protected override void ConfigureProperties(EntityTypeBuilder<Entities.CustomAttribute> builder)
    {
        builder.Property(x => x.Value).HasColumnName("Value").IsRequired();
        builder.Property(x => x.Name).HasColumnName("Name").IsRequired();

        builder.Property(x => x.ServiceAreaId).HasColumnName("ServiceAreaId").IsRequired(false);
        builder.Property(x => x.UserRoleId).HasColumnName("UserRoleId").IsRequired(false);
        builder.Property(x => x.WorkplaceId).HasColumnName("WORKPLACE_ID").IsRequired(false);
        builder.Property(x => x.Image).HasColumnName("Image").IsRequired(false);

        //	foreign keys
        builder.HasOne(x => x.ServiceArea).WithMany(x => x.CustomAttributes).HasForeignKey(x => x.ServiceAreaId).IsRequired(false).OnDelete(DeleteBehavior.Cascade);
        builder.HasOne(x => x.UserRole).WithMany(x => x.CustomAttributes).HasForeignKey(x => x.UserRoleId).IsRequired(false).OnDelete(DeleteBehavior.Cascade);
        builder.HasOne(x => x.OperatorStatus).WithMany(x => x.CustomAttributes).HasForeignKey(x => x.OperatorStatusId).IsRequired(false).OnDelete(DeleteBehavior.Cascade);
        builder.HasOne(x => x.Workplace).WithMany(x => x.CustomAttributes).HasForeignKey(x => x.WorkplaceId).IsRequired(false).OnDelete(DeleteBehavior.Cascade);
    }
}
