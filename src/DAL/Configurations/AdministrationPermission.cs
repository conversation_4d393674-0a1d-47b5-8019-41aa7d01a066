using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Product.AWP.Infrastructure.DAL.Configurations.BaseConfigurations;

namespace Product.AWP.Infrastructure.DAL.Configurations;

internal class AdministrationPermission : HasIdEntityBase<Entities.AdministrationPermission>
{
    public AdministrationPermission(string schema = null)
        : base("AdministrationPermissions", schema)
    {
    }

    protected override void ConfigureProperties(EntityTypeBuilder<Entities.AdministrationPermission> builder)
    {
        builder.Property(x => x.PermissionId).HasColumnName("PermissionId").IsRequired();
        builder.Property(x => x.AdministrationObjectId).HasColumnName("AdministrationObjectId").IsRequired();
        builder.Property(x => x.UserGroupId).HasColumnName("UserGroupId").IsRequired();

        builder.HasOne(x => x.AdministrationObject).WithMany(x => x.AdministrationPermissions).HasForeignKey(x => x.AdministrationObjectId).IsRequired().OnDelete(DeleteBehavior.NoAction);
        builder.HasOne(x => x.UserGroup).WithMany(x => x.AdministrationPermissions).HasForeignKey(x => x.UserGroupId).IsRequired();
    }
}
