using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Product.AWP.Infrastructure.DAL.Configurations.BaseConfigurations;

namespace Product.AWP.Infrastructure.DAL.Configurations;

internal class UserRole : HasIdEntityBase<Entities.UserRole>
{
    public UserRole(string schema = null)
        : base("UserRoles", schema)
    {
    }

    protected override void ConfigureProperties(EntityTypeBuilder<Entities.UserRole> builder)
    {
        builder.Property(x => x.Name).HasColumnName("Name").HasMaxLength(150).IsRequired();
        builder.Property(x => x.Description).HasColumnName("Description").HasMaxLength(1000).IsRequired(false);
        builder.Property(x => x.ParentId).HasColumnName("ParentId").IsRequired(false);

        builder.HasOne(x => x.Parent).WithMany(x => x.Children).HasForeignKey(x => x.ParentId).IsRequired(false);
    }
}
