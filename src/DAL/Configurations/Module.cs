using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Product.AWP.Infrastructure.DAL.Configurations.BaseConfigurations;

namespace Product.AWP.Infrastructure.DAL.Configurations;

internal class Module : HasIdEntityBase<Entities.Module>
{
    public Module(string schema = null)
        : base("Modules", schema)
    {
    }

    protected override void ConfigureProperties(EntityTypeBuilder<Entities.Module> builder)
    {
        builder.Property(x => x.Name).HasColumnName("Name").HasMaxLength(50).IsRequired();
        builder.Property(x => x.Initialization).HasColumnName("Initialization").IsRequired(false);
        builder.Property(x => x.Description).HasColumnName("Description").HasMaxLength(1000).IsRequired(false);
        builder.Property(x => x.IsOptional).HasColumnName("IsOptional").IsRequired();
        builder.Property(x => x.IsDisabled).HasColumnName("IsDisabled").IsRequired();

        builder.HasMany(x => x.UserRoles).WithMany(x => x.Modules)
            .UsingEntity<Dictionary<string, object>>(
                "ModuleUserRole",
                x => x.HasOne<Entities.UserRole>().WithMany().HasForeignKey("UserRoleId"),
                x => x.HasOne<Entities.Module>().WithMany().HasForeignKey("ModuleId"),
                jet => jet.ToTable("ModuleUserRole", Schema)
            );
    }
}
