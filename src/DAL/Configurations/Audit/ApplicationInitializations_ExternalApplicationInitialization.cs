using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Product.AWP.Infrastructure.DAL.Configurations.Audit.Base;

namespace Product.AWP.Infrastructure.DAL.Configurations.Audit;

internal class ExternalApplicationInitialization : AuditableEntityWithIdBase<Entities.Audit.ExternalApplicationInitialization>
{
    public ExternalApplicationInitialization(string schema = null) : base("APP_INIT_EXTERNAL_CHANGES", schema)
    {
    }

    protected override void ConfigureKey(EntityTypeBuilder<Entities.Audit.ExternalApplicationInitialization> builder)
    {
        //	иначе ошибка System.InvalidOperationException: A key cannot be configured on 'WebApplicationInitialization' because it is a derived type. The key must be configured on the root type 'ApplicationInitialization'. If you did not intend for 'ApplicationInitialization' to be included in the model, ensure that it is not referenced by a DbSet property on your context, referenced in a configuration call to ModelBuilder, or referenced from a navigation on a type that is included in the model.
        //base.ConfigureKey(builder);
    }

    protected override void ConfigureOwnProperties(EntityTypeBuilder<Entities.Audit.ExternalApplicationInitialization> builder)
    {
        base.ConfigureOwnProperties(builder);

        builder.Property(x => x.AcquisitionTimeout).HasColumnName("ACQUISITION_TIMEOUT").IsRequired(false);

        builder.Property(x => x.Path).HasColumnName("PATH").IsRequired();
        builder.Property(x => x.Arguments).HasColumnName("ARGUMENTS").IsRequired(false);
        builder.Property(x => x.WorkingDirectory).HasColumnName("WORKING_DIRECTORY").IsRequired(false);

        builder.Property(x => x.Caption).HasColumnName("CAPTION").IsRequired(false);
        builder.Property(x => x.ClassName).HasColumnName("CLASS_NAME").IsRequired(false);
        builder.Property(x => x.LimitToProcess).HasColumnName("LIMIT_TO_PROCESS").IsRequired();
        builder.Property(x => x.UseProcessMainWindow).HasColumnName("USE_PROCESS_MAIN_WINDOW").IsRequired();
        builder.Property(x => x.SearchWindowAfterLogin).HasColumnName("SEARCH_WINDOW_AFTER_LOGIN").IsRequired();
    }
}
