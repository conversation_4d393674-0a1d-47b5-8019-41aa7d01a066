using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Product.AWP.Infrastructure.DAL.Configurations.Audit.Base;

namespace Product.AWP.Infrastructure.DAL.Configurations.Audit;

internal class UserGroupUserRole : AuditableEntityBase<Entities.Audit.UserGroupUserRole>
{
    public UserGroupUserRole(string schema = null) : base("USER_GROUP_USER_ROLE_CHANGES", schema)
    {
    }

    protected override void ConfigureOwnProperties(EntityTypeBuilder<Entities.Audit.UserGroupUserRole> builder)
    {
        builder.Property(x => x.UserRoleId).HasColumnName("USER_ROLE_ID").IsRequired();
        builder.Property(x => x.UserGroupId).HasColumnName("USER_GROUP_ID").IsRequired();
    }
}
