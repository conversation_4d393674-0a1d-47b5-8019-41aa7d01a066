using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Product.AWP.Infrastructure.DAL.Configurations.Audit.Base;

namespace Product.AWP.Infrastructure.DAL.Configurations.Audit;

internal class ModuleUserRole : AuditableEntityBase<Entities.Audit.ModuleUserRole>
{
    public ModuleUserRole(string schema = null) : base("MODULE_USER_ROLE_CHANGES", schema)
    {
    }

    protected override void ConfigureOwnProperties(EntityTypeBuilder<Entities.Audit.ModuleUserRole> builder)
    {
        builder.Property(x => x.ModuleId).HasColumnName("MODULE_ID").IsRequired();
        builder.Property(x => x.UserRoleId).HasColumnName("USER_ROLE_ID").IsRequired();
    }
}
