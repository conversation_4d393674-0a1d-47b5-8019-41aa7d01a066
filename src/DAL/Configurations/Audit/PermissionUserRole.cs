using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Product.AWP.Infrastructure.DAL.Configurations.Audit.Base;

namespace Product.AWP.Infrastructure.DAL.Configurations.Audit;

internal class PermissionUserRole : AuditableEntityBase<Entities.Audit.PermissionUserRole>
{
    public PermissionUserRole(string schema = null) : base("PERMISSION_USER_ROLE_CHANGES", schema)
    {
    }

    protected override void ConfigureOwnProperties(EntityTypeBuilder<Entities.Audit.PermissionUserRole> builder)
    {
        builder.Property(x => x.PermissionId).HasColumnName("PERMISSION_ID").IsRequired();
        builder.Property(x => x.UserRoleId).HasColumnName("USER_ROLE_ID").IsRequired();
    }
}
