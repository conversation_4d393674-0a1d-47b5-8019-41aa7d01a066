using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Product.AWP.Infrastructure.DAL.Configurations.Audit.Base;

namespace Product.AWP.Infrastructure.DAL.Configurations.Audit;

internal class UserGroupIdentity : AuditableEntityBase<Entities.Audit.UserGroupIdentity>
{
    public UserGroupIdentity(string schema = null) : base("USER_GROUP_IDENTITY_CHANGES", schema)
    {
    }

    protected override void ConfigureOwnProperties(EntityTypeBuilder<Entities.Audit.UserGroupIdentity> builder)
    {
        builder.Property(x => x.UserGroupId).HasColumnName("USER_GROUP_ID").IsRequired();
        builder.Property(x => x.IdentityId).HasColumnName("IDENTITY_ID").IsRequired();
    }
}
