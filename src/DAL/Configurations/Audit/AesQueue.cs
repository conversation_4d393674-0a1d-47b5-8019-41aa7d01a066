using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Product.AWP.Infrastructure.DAL.Configurations.Audit.Base;

namespace Product.AWP.Infrastructure.DAL.Configurations.Audit;

internal class AesQueue : AuditableEntityWithIdBase<Entities.Audit.AesQueue>
{
    public AesQueue(string schema = null) : base("AES_QUEUE_CHANGES", schema)
    {
    }

    protected override void ConfigureOwnProperties(EntityTypeBuilder<Entities.Audit.AesQueue> builder)
    {
        base.ConfigureOwnProperties(builder);

        builder.Property(x => x.Name).HasColumnName("NAME").HasMaxLength(100).IsRequired();
        builder.Property(x => x.Extension).HasColumnName("EXTENSION").HasMaxLength(50).IsRequired();
        builder.Property(x => x.Description).HasColumnName("DESCRIPTION").HasMaxLength(1000).IsRequired(false);
        builder.Property(x => x.Aggregatable).HasColumnName("AGGREGATABLE").IsRequired(false);
    }
}
