using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Product.AWP.Infrastructure.DAL.Configurations.Audit.Base;

namespace Product.AWP.Infrastructure.DAL.Configurations.Audit;

internal class ApplicationUserRole : AuditableEntityBase<Entities.Audit.ApplicationUserRole>
{
    public ApplicationUserRole(string schema = null) : base("APPLICATION_USER_ROLE_CHANGES", schema)
    {
    }

    protected override void ConfigureOwnProperties(EntityTypeBuilder<Entities.Audit.ApplicationUserRole> builder)
    {
        builder.Property(x => x.ApplicationId).HasColumnName("APPLICATION_ID").IsRequired();
        builder.Property(x => x.UserRoleId).HasColumnName("USER_ROLE_ID").IsRequired();
    }
}
