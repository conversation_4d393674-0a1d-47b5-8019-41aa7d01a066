using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Product.AWP.Infrastructure.DAL.Configurations.Audit.Base;

namespace Product.AWP.Infrastructure.DAL.Configurations.Audit;

internal class Operator : AuditableEntityWithIdBase<Entities.Audit.Operator>
{
    public Operator(string schema = null) : base("OPERATOR_CHANGES", schema)
    {
    }

    protected override void ConfigureOwnProperties(EntityTypeBuilder<Entities.Audit.Operator> builder)
    {
        base.ConfigureOwnProperties(builder);

        builder.Property(x => x.FirstName).HasColumnName("FIRST_NAME").IsRequired(false);
        builder.Property(x => x.MiddleName).HasColumnName("MIDDLE_NAME").IsRequired(false);
        builder.Property(x => x.LastName).HasColumnName("LAST_NAME").IsRequired(false);
        builder.Property(x => x.Nickname).HasColumnName("NICKNAME").HasMaxLength(300).IsRequired(false);
        builder.Property(x => x.UserName).HasColumnName("USER_NAME").HasMaxLength(300).IsRequired();
        builder.Property(x => x.ActiveDirectoryId).HasColumnName("ACTIVE_DIRECTORY_ID").IsRequired();
    }
}
