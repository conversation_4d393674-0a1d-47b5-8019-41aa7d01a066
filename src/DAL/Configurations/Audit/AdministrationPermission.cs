using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Product.AWP.Infrastructure.DAL.Configurations.Audit.Base;

namespace Product.AWP.Infrastructure.DAL.Configurations.Audit;

internal class AdministrationPermission : AuditableEntityWithIdBase<Entities.Audit.AdministrationPermission>
{
    public AdministrationPermission(string schema = null) : base("ADMIN_PERMISSION_CHANGES", schema)
    {
    }

    protected override void ConfigureOwnProperties(EntityTypeBuilder<Entities.Audit.AdministrationPermission> builder)
    {
        base.ConfigureOwnProperties(builder);

        builder.Property(x => x.PermissionId).HasColumnName("PERMISSION_ID").IsRequired();
        builder.Property(x => x.AdministrationObjectId).HasColumnName("ADMINISTRATION_OBJECT_ID").IsRequired();
        builder.Property(x => x.UserGroupId).HasColumnName("USER_GROUP_ID").IsRequired();
    }
}
