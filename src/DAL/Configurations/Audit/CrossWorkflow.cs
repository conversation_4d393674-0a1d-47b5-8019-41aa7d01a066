using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Product.AWP.Infrastructure.DAL.Configurations.Audit.Base;

namespace Product.AWP.Infrastructure.DAL.Configurations.Audit;

internal class CrossWorkflow : AuditableEntityWithIdBase<Entities.Audit.CrossWorkflow>
{
    public CrossWorkflow(string schema = null) : base("CROSS_WORKFLOW_CHANGES", schema)
    {
    }

    protected override void ConfigureOwnProperties(EntityTypeBuilder<Entities.Audit.CrossWorkflow> builder)
    {
        base.ConfigureOwnProperties(builder);

        builder.Property(x => x.Order).HasColumnName("ORDER").IsRequired();
        builder.Property(x => x.HostWorkflowId).HasColumnName("HOST_WORKFLOW_IS").IsRequired();
        builder.Property(x => x.TargetWorkflowId).HasColumnName("TARGET_WORKFLOW_ID").IsRequired();
    }
}
