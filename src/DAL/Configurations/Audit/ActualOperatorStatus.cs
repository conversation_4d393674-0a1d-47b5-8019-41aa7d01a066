using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Product.AWP.Infrastructure.DAL.Configurations.Audit.Base;

namespace Product.AWP.Infrastructure.DAL.Configurations.Audit;

internal class ActualOperatorStatus : AuditableEntityWithIdBase<Entities.Audit.ActualOperatorStatus>
{
    public ActualOperatorStatus(string schema = null) : base("ACTUAL_OPERATOR_STAT_CHANGES", schema)
    {
    }

    protected override void ConfigureOwnProperties(EntityTypeBuilder<Entities.Audit.ActualOperatorStatus> builder)
    {
        base.ConfigureOwnProperties(builder);

        builder.Property(x => x.OperatorId).HasColumnName("OPERATOR_ID").IsRequired();
        builder.Property(x => x.DateFrom).HasColumnName("DATE_FROM").IsRequired();
        builder.Property(x => x.ValidUntil).HasColumnName("VALID_UNTIL").IsRequired(false);
        builder.Property(x => x.ActualStatusSetCode).HasColumnName("ACTUAL_STATUS_SET_CODE").IsRequired(false);
        builder.Property(x => x.ActualStatusId).HasColumnName("ACTUAL_STATUS_ID").IsRequired();
        builder.Property(x => x.PreviousStatusId).HasColumnName("PREVIOUS_STATUS_ID").IsRequired(false);
        builder.Property(x => x.ActualPendingStatusId).HasColumnName("ACTUAL_PENDING_STATUS_ID").IsRequired(false);
        builder.Property(x => x.PreviousPendingStatusId).HasColumnName("PREVIOUS_PENDING_STATUS_ID").IsRequired(false);
        builder.Property(x => x.WorkSessionId).HasColumnName("WORK_SESSION_ID").IsRequired(false);
    }
}
