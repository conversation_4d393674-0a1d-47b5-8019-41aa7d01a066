using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Product.AWP.Infrastructure.DAL.Configurations.Audit.Base;

namespace Product.AWP.Infrastructure.DAL.Configurations.Audit;

internal class ApplicationInitialization : AuditableEntityWithIdBase<Entities.Audit.ApplicationInitialization>
{
    public ApplicationInitialization(string schema = null) : base("APPLICATION_INIT_CHANGES", schema)
    {
    }

    protected override void ConfigureOwnProperties(EntityTypeBuilder<Entities.Audit.ApplicationInitialization> builder)
    {
        base.ConfigureOwnProperties(builder);

        builder.Property(x => x.DisplayGroup).HasColumnName("DISPLAY_GROUP").IsRequired();
        builder.Property(x => x.AdapterInitialization).HasColumnName("ADAPTER_INITIALIZATION").IsRequired(false);

        builder.OwnsOne(
            x => x.Adapter,
            ad =>
            {
                ad.Property(x => x.Assembly).HasColumnName("ADAPTER_ASSEMBLY").IsRequired(false);
                ad.Property(x => x.TypeName).HasColumnName("ADAPTER_TYPENAME").IsRequired(false);
            });

        builder.Property(x => x.StartAsync).HasColumnName("START_ASYNC").IsRequired();

        builder.Property(x => x.WrapInExpander).HasColumnName("WRAP_IN_EXPANDER").IsRequired();
        builder.Property(x => x.InitiallyExpanded).HasColumnName("INITIALLY_EXPANDED").IsRequired();
        builder.Property(x => x.Header).HasColumnName("HEADER").IsRequired(false);
        builder.Property(x => x.ExpandDirection).HasColumnName("EXPAND_DIRECTION").IsRequired();
        builder.Property(x => x.ExpandDisplayMode).HasColumnName("EXPAND_DISPLAY_MODE").IsRequired();

        builder.Property(x => x.Width).HasColumnName("WIDTH").IsRequired(false);
        builder.Property(x => x.Height).HasColumnName("HEIGHT").IsRequired(false);
        builder.Property(x => x.MinWidth).HasColumnName("MIN_WIDTH").IsRequired(false);
        builder.Property(x => x.MinHeight).HasColumnName("MIN_HEIGHT").IsRequired(false);
        builder.Property(x => x.MaxWidth).HasColumnName("MAX_WIDTH").IsRequired(false);
        builder.Property(x => x.MaxHeight).HasColumnName("MAX_HEIGHT").IsRequired(false);

        builder.Property(x => x.HideInTaskbar).HasColumnName("HIDE_IN_TASKBAR").IsRequired();
        builder.Property(x => x.HideOnStartup).HasColumnName("HIDE_ON_STARTUP").IsRequired();
    }
}
