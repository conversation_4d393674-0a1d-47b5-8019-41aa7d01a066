using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Product.AWP.Infrastructure.DAL.Configurations.Audit.Base;

namespace Product.AWP.Infrastructure.DAL.Configurations.Audit;

public class ServiceArea : AuditableEntityWithIdBase<Entities.Audit.ServiceArea>
{
    public ServiceArea(string schema = null) : base("SERVICE_AREA_CHANGES", schema)
    {
    }

    protected override void ConfigureOwnProperties(EntityTypeBuilder<Entities.Audit.ServiceArea> builder)
    {
        base.ConfigureOwnProperties(builder);

        builder.Property(x => x.Name).HasColumnName("NAME").IsRequired();
        builder.Property(x => x.UtcOffset).HasColumnName("UTC_OFFSET").IsRequired();
        builder.Property(x => x.Code).HasColumnName("CODE").HasMaxLength(50).IsRequired();
        builder.Property(x => x.ServiceAreaTypeId).HasColumnName("SERVICE_AREA_TYPE_ID").IsRequired(false);
        builder.Property(x => x.ParentId).HasColumnName("PARENT_ID").IsRequired(false);
    }
}
