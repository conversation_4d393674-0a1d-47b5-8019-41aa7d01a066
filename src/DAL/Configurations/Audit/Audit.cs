using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Product.AWP.Infrastructure.DAL.Configurations.Audit;

public class Audit : IEntityTypeConfiguration<Entities.Audit.Audit>
{
    private readonly string _schema;

    public Audit(string schema = null)
    {
        _schema = schema;
    }

    public void Configure(EntityTypeBuilder<Entities.Audit.Audit> builder)
    {
        builder.ToTable("AUDITS", _schema);

        builder.Property(x => x.Id).HasColumnName("ID").IsRequired();
        builder.HasKey(x => x.Id);

        builder.Property(x => x.Login).HasColumnName("LOGIN").IsRequired().HasMaxLength(500);
        builder.Property(x => x.Dns).HasColumnName("DNS").IsRequired().HasMaxLength(500);
        builder.Property(x => x.ChangeDate).HasColumnName("CHANGE_DATE").IsRequired();
        builder.Property(x => x.OperationName).HasColumnName("OPERATION_NAME").IsRequired().HasMaxLength(500);
    }
}
