using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Product.AWP.Infrastructure.DAL.Configurations.Audit.Base;

namespace Product.AWP.Infrastructure.DAL.Configurations.Audit;

internal class WorkflowGroup : AuditableEntityWithIdBase<Entities.Audit.WorkflowGroup>
{
    public WorkflowGroup(string schema = null) : base("WORKFLOW_GROUP_CHANGES", schema)
    {
    }

    protected override void ConfigureOwnProperties(EntityTypeBuilder<Entities.Audit.WorkflowGroup> builder)
    {
        base.ConfigureOwnProperties(builder);

        builder.Property(x => x.Name).HasColumnName("NAME").HasMaxLength(50).IsRequired();
        builder.Property(x => x.Description).HasColumnName("DESCRIPTION").HasMaxLength(1000).IsRequired(false);
        builder.Property(x => x.Image).HasColumnName("IMAGE").IsRequired(false);
        builder.Property(x => x.Order).HasColumnName("ORDER").IsRequired(false);
    }
}
