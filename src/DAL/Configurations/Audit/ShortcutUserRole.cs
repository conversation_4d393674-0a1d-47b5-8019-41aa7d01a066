using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Product.AWP.Infrastructure.DAL.Configurations.Audit.Base;

namespace Product.AWP.Infrastructure.DAL.Configurations.Audit;

internal class ShortcutUserRole : AuditableEntityBase<Entities.Audit.ShortcutUserRole>
{
    public ShortcutUserRole(string schema = null) : base("SHORTCUT_USER_ROLE_CHANGES", schema)
    {
    }

    protected override void ConfigureOwnProperties(EntityTypeBuilder<Entities.Audit.ShortcutUserRole> builder)
    {
        builder.Property(x => x.ShortcutId).HasColumnName("SHORTCUT_ID").IsRequired();
        builder.Property(x => x.UserRoleId).HasColumnName("USER_ROLE_ID").IsRequired();
    }
}
