using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Product.AWP.Infrastructure.DAL.Configurations.Audit.Base;

namespace Product.AWP.Infrastructure.DAL.Configurations.Audit;

internal class ControlApplicationInitialization : AuditableEntityWithIdBase<Entities.Audit.ControlApplicationInitialization>
{
    public ControlApplicationInitialization(string schema = null) : base("APP_INIT_CONTROL_CHANGES", schema)
    {
    }

    protected override void ConfigureKey(EntityTypeBuilder<Entities.Audit.ControlApplicationInitialization> builder)
    {
        //	иначе ошибка System.InvalidOperationException: A key cannot be configured on 'WebApplicationInitialization' because it is a derived type. The key must be configured on the root type 'ApplicationInitialization'. If you did not intend for 'ApplicationInitialization' to be included in the model, ensure that it is not referenced by a DbSet property on your context, referenced in a configuration call to ModelBuilder, or referenced from a navigation on a type that is included in the model.
        //base.ConfigureKey(builder);
    }

    protected override void ConfigureOwnProperties(EntityTypeBuilder<Entities.Audit.ControlApplicationInitialization> builder)
    {
        base.ConfigureOwnProperties(builder);

        builder.Property(x => x.ControlInitialization).HasColumnName("CONTROL_INITIALIZATION").IsRequired(false);
        builder.Property(x => x.ControlPerformsLogin).HasColumnName("CONTROL_PERFORMS_LOGIN").IsRequired();

        builder.OwnsOne(
            x => x.Control,
            c =>
            {
                c.Property(x => x.Assembly).HasColumnName("CONTROL_ASSEMBLY").IsRequired(false);
                c.Property(x => x.TypeName).HasColumnName("CONTROL_TYPENAME").IsRequired(false);
            });
    }
}
