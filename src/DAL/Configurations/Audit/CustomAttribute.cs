using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Product.AWP.Infrastructure.DAL.Configurations.Audit.Base;

namespace Product.AWP.Infrastructure.DAL.Configurations.Audit;

internal class CustomAttribute : AuditableEntityWithIdBase<Entities.Audit.CustomAttribute>
{
    public CustomAttribute(string schema = null) : base("CUSTOM_ATTRIBUTE_CHANGES", schema)
    {
    }

    protected override void ConfigureOwnProperties(EntityTypeBuilder<Entities.Audit.CustomAttribute> builder)
    {
        base.ConfigureOwnProperties(builder);

        builder.Property(x => x.Value).HasColumnName("VALUE").IsRequired();
        builder.Property(x => x.Name).HasColumnName("NAME").IsRequired();

        builder.Property(x => x.ServiceAreaId).HasColumnName("SERVICE_AREA_ID").IsRequired(false);
        builder.Property(x => x.OperatorStatusId).HasColumnName("OPERATOR_STATUS_ID").IsRequired(false);
        builder.Property(x => x.UserRoleId).HasColumnName("USER_ROLE_ID").IsRequired(false);
        builder.Property(x => x.WorkplaceId).HasColumnName("WORKPLACE_ID").IsRequired(false);
        builder.Property(x => x.Image).HasColumnName("IMAGE").IsRequired(false);
    }
}
