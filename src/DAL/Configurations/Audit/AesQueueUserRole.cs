using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Product.AWP.Infrastructure.DAL.Configurations.Audit.Base;

namespace Product.AWP.Infrastructure.DAL.Configurations.Audit;

internal class AesQueueUserRole : AuditableEntityBase<Entities.Audit.AesQueueUserRole>
{
    public AesQueueUserRole(string schema = null) : base("AES_QUEUE_USER_ROLE_CHANGES", schema)
    {
    }

    protected override void ConfigureOwnProperties(EntityTypeBuilder<Entities.Audit.AesQueueUserRole> builder)
    {
        builder.Property(x => x.AesQueueId).HasColumnName("AES_QUEUE_ID").IsRequired();
        builder.Property(x => x.UserRoleId).HasColumnName("USER_ROLE_ID").IsRequired();
    }
}
