using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Product.AWP.Infrastructure.DAL.Configurations.Audit.Base;

namespace Product.AWP.Infrastructure.DAL.Configurations.Audit;

internal class WebApplicationInitialization : AuditableEntityWithIdBase<Entities.Audit.WebApplicationInitialization>
{
    public WebApplicationInitialization(string schema = null) : base("APP_INIT_WEB_CHANGES", schema)
    {
    }

    protected override void ConfigureKey(EntityTypeBuilder<Entities.Audit.WebApplicationInitialization> builder)
    {
        //	иначе ошибка System.InvalidOperationException: A key cannot be configured on 'WebApplicationInitialization' because it is a derived type. The key must be configured on the root type 'ApplicationInitialization'. If you did not intend for 'ApplicationInitialization' to be included in the model, ensure that it is not referenced by a DbSet property on your context, referenced in a configuration call to ModelBuilder, or referenced from a navigation on a type that is included in the model.
        //base.ConfigureKey(builder);
    }

    protected override void ConfigureOwnProperties(EntityTypeBuilder<Entities.Audit.WebApplicationInitialization> builder)
    {
        base.ConfigureOwnProperties(builder);

        builder.Property(x => x.StartUrl).HasColumnName("START_URL").IsRequired();
        builder.Property(x => x.SuppressScriptErrors).HasColumnName("SUPPRESS_SCRIPT_ERRORS").IsRequired();
    }
}
