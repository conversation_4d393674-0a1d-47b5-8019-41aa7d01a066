using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Product.AWP.Infrastructure.DAL.Configurations.Audit.Base;

namespace Product.AWP.Infrastructure.DAL.Configurations.Audit;

internal class OperatorStatusUserRole : AuditableEntityBase<Entities.Audit.OperatorStatusUserRole>
{
    public OperatorStatusUserRole(string schema = null) : base("OPER_STATUS_USER_ROLE_CHANGES", schema)
    {
    }

    protected override void ConfigureOwnProperties(EntityTypeBuilder<Entities.Audit.OperatorStatusUserRole> builder)
    {
        builder.Property(x => x.OperatorStatusId).HasColumnName("OPERATOR_STATUS_ID").IsRequired();
        builder.Property(x => x.UserRoleId).HasColumnName("USER_ROLE_ID").IsRequired();
    }
}
