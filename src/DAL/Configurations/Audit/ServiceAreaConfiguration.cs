using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Product.AWP.Infrastructure.DAL.Configurations.Audit.Base;

namespace Product.AWP.Infrastructure.DAL.Configurations.Audit;

internal class ServiceAreaConfiguration : AuditableEntityWithIdBase<Entities.Audit.ServiceAreaConfiguration>
{
    public ServiceAreaConfiguration(string schema = null) : base("SERVICE_AREA_CONFIG_CHANGES", schema)
    {
    }

    protected override void ConfigureOwnProperties(EntityTypeBuilder<Entities.Audit.ServiceAreaConfiguration> builder)
    {
        base.ConfigureOwnProperties(builder);

        builder.Property(x => x.ServiceAreaId).HasColumnName("SERVICE_AREA_ID").IsRequired();
        builder.Property(x => x.PropertyKey).HasColumnName("PROPERTY_KEY").HasMaxLength(500).IsRequired();

        builder.Property(x => x.Value).HasColumnName("VALUE").IsRequired(false);
        builder.Property(x => x.ShortValue).HasColumnName("SHORT_VALUE").IsRequired(false);
        builder.Property(x => x.IntValue).HasColumnName("INT_VALUE").IsRequired(false);
        builder.Property(x => x.DoubleValue).HasColumnName("DOUBLE_VALUE").IsRequired(false);
        builder.Property(x => x.LongValue).HasColumnName("LONG_VALUE").IsRequired(false);
        builder.Property(x => x.GuidValue).HasColumnName("GUID_VALUE").IsRequired(false);
        builder.Property(x => x.DateTimeValue).HasColumnName("DATETIME_VALUE").IsRequired(false);
        builder.Property(x => x.BoolValue).HasColumnName("BOOL_VALUE").IsRequired(false);

        builder.Property(x => x.ConfigurationId).HasColumnName("CONFIGURATION_ID").IsRequired(false);
        builder.Property(x => x.ApplicationId).HasColumnName("APPLICATION_ID").IsRequired(false);
        builder.Property(x => x.ModuleId).HasColumnName("MODULE_ID").IsRequired(false);
        builder.Property(x => x.ShortcutId).HasColumnName("SHORTCUT_ID").IsRequired(false);
        builder.Property(x => x.AesQueueId).HasColumnName("AES_QUEUE_ID").IsRequired(false);
    }
}
