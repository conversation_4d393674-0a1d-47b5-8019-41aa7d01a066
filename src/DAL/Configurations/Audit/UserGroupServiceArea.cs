using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Product.AWP.Infrastructure.DAL.Configurations.Audit.Base;

namespace Product.AWP.Infrastructure.DAL.Configurations.Audit;

internal class UserGroupServiceArea : AuditableEntityBase<Entities.Audit.UserGroupServiceArea>
{
    public UserGroupServiceArea(string schema = null) : base("USER_GROUP_SER_AREA_CHANGES", schema)
    {
    }

    protected override void ConfigureOwnProperties(EntityTypeBuilder<Entities.Audit.UserGroupServiceArea> builder)
    {
        builder.Property(x => x.ServiceAreaId).HasColumnName("SERVICE_AREA_ID").IsRequired();
        builder.Property(x => x.UserGroupId).HasColumnName("USER_GROUP_ID").IsRequired();
    }
}
