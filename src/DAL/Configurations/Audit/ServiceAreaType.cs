using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Product.AWP.Infrastructure.DAL.Configurations.Audit.Base;

namespace Product.AWP.Infrastructure.DAL.Configurations.Audit;

internal class ServiceAreaType : AuditableEntityWithIdBase<Entities.Audit.ServiceAreaType>
{
    public ServiceAreaType(string schema = null) : base("SERVICE_AREA_TYPE_CHANGES", schema)
    {
    }

    protected override void ConfigureOwnProperties(EntityTypeBuilder<Entities.Audit.ServiceAreaType> builder)
    {
        base.ConfigureOwnProperties(builder);

        builder.Property(x => x.Name).HasColumnName("NAME").IsRequired();
    }
}
