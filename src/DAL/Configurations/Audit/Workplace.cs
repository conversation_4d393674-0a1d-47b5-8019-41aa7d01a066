using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Product.AWP.Infrastructure.DAL.Configurations.Audit.Base;

namespace Product.AWP.Infrastructure.DAL.Configurations.Audit;

internal class Workplace : AuditableEntityWithIdBase<Entities.Audit.Workplace>
{
    public Workplace(string schema = null) : base("WORKPLACE_CHANGES", schema)
    {
    }

    protected override void ConfigureOwnProperties(EntityTypeBuilder<Entities.Audit.Workplace> builder)
    {
        base.ConfigureOwnProperties(builder);

        builder.Property(x => x.Name).HasColumnName("NAME").IsRequired();
        builder.Property(x => x.Code).HasColumnName("CODE").IsRequired();
        builder.Property(x => x.UtcOffset).HasColumnName("UTC_OFFSET").IsRequired();
        builder.Property(x => x.ParentId).HasColumnName("PARENT_ID").IsRequired(false);
    }
}
