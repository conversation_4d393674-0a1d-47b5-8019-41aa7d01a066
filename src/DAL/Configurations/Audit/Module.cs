using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Product.AWP.Infrastructure.DAL.Configurations.Audit.Base;

namespace Product.AWP.Infrastructure.DAL.Configurations.Audit;

internal class Module : AuditableEntityWithIdBase<Entities.Audit.Module>
{
    public Module(string schema = null) : base("MODULE_CHANGES", schema)
    {
    }

    protected override void ConfigureOwnProperties(EntityTypeBuilder<Entities.Audit.Module> builder)
    {
        base.ConfigureOwnProperties(builder);

        builder.Property(x => x.Name).HasColumnName("NAME").HasMaxLength(50).IsRequired();
        builder.Property(x => x.Initialization).HasColumnName("INITIALIZATION").IsRequired(false);
        builder.Property(x => x.Description).HasColumnName("DESCRIPTION").HasMaxLength(1000).IsRequired(false);
        builder.Property(x => x.IsOptional).HasColumnName("IS_OPTIONAL").IsRequired();
        builder.Property(x => x.IsDisabled).HasColumnName("IS_DISABLED").IsRequired();
    }
}
