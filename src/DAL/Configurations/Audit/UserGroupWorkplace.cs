using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Product.AWP.Infrastructure.DAL.Configurations.Audit.Base;

namespace Product.AWP.Infrastructure.DAL.Configurations.Audit;

internal class UserGroupWorkplace : AuditableEntityBase<Entities.Audit.UserGroupWorkplace>
{
    public UserGroupWorkplace(string schema = null) : base("USER_GROUP_WORKPLACE_CHANGES", schema)
    {
    }

    protected override void ConfigureOwnProperties(EntityTypeBuilder<Entities.Audit.UserGroupWorkplace> builder)
    {
        builder.Property(x => x.WorkplaceId).HasColumnName("WORKPLACE_ID").IsRequired();
        builder.Property(x => x.UserGroupId).HasColumnName("USER_GROUP_ID").IsRequired();
    }
}
