using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Product.AWP.Infrastructure.DAL.Configurations.Audit.Base;

namespace Product.AWP.Infrastructure.DAL.Configurations.Audit;

internal class OperatorGroup : AuditableEntityWithIdBase<Entities.Audit.OperatorGroup>
{
    public OperatorGroup(string schema = null) : base("OPERATOR_GROUP_CHANGES", schema)
    {
    }

    protected override void ConfigureOwnProperties(EntityTypeBuilder<Entities.Audit.OperatorGroup> builder)
    {
        base.ConfigureOwnProperties(builder);

        builder.Property(x => x.Name).HasColumnName("NAME").IsRequired(false);
        builder.Property(x => x.ParentId).HasColumnName("PARENT_ID").IsRequired(false);
    }
}
