using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Product.AWP.Infrastructure.DAL.Configurations.Audit.Base;

namespace Product.AWP.Infrastructure.DAL.Configurations.Audit;

internal class AdministrationObject : AuditableEntityWithIdBase<Entities.Audit.AdministrationObject>
{
    public AdministrationObject(string schema = null) : base("ADMINISTRATION_OBJECT_CHANGES", schema)
    {
    }

    protected override void ConfigureOwnProperties(EntityTypeBuilder<Entities.Audit.AdministrationObject> builder)
    {
        base.ConfigureOwnProperties(builder);

        builder.Property(x => x.Name).HasColumnName("NAME").IsRequired();
        builder.Property(x => x.ModuleName).HasColumnName("MODULE_NAME").IsRequired();
    }
}
