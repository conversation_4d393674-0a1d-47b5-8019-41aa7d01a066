using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Product.AWP.Infrastructure.DAL.Configurations.Audit.Base;

namespace Product.AWP.Infrastructure.DAL.Configurations.Audit;

internal class Permission : AuditableEntityWithIdBase<Entities.Audit.Permission>
{
    public Permission(string schema = null) : base("PERMISSION_CHANGES", schema)
    {
    }

    protected override void ConfigureOwnProperties(EntityTypeBuilder<Entities.Audit.Permission> builder)
    {
        base.ConfigureOwnProperties(builder);

        builder.Property(x => x.Name).HasColumnName("NAME").HasMaxLength(50).IsRequired();
        builder.Property(x => x.Description).HasColumnName("DESCRIPTION").HasMaxLength(1000).IsRequired(false);
    }
}
