using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Product.AWP.Infrastructure.DAL.Configurations.Audit.Base;

namespace Product.AWP.Infrastructure.DAL.Configurations.Audit;

internal class UserShortcut : AuditableEntityWithIdBase<Entities.Audit.UserShortcut>
{
    public UserShortcut(string schema = null) : base("USER_SHORTCUT_CHANGES", schema)
    {
    }

    protected override void ConfigureKey(EntityTypeBuilder<Entities.Audit.UserShortcut> builder)
    {
        //	иначе ошибка System.InvalidOperationException: A key cannot be configured on 'Shortcut' because it is a derived type. The key must be configured on the root type 'BaseShortcut'. If you did not intend for 'BaseShortcut' to be included in the model, ensure that it is not referenced by a DbSet property on your context, referenced in a configuration call to ModelBuilder, or referenced from a navigation on a type that is included in the model.
        //base.Configure<PERSON><PERSON>(builder);
    }

    protected override void ConfigureOwnProperties(EntityTypeBuilder<Entities.Audit.UserShortcut> builder)
    {
        base.ConfigureOwnProperties(builder);

        builder.Property(x => x.UserName).HasColumnName("USER_NAME").HasMaxLength(100).IsRequired();
    }
}
