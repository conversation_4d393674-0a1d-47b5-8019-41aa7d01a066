using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Product.AWP.Infrastructure.DAL.Configurations.Audit.Base;

namespace Product.AWP.Infrastructure.DAL.Configurations.Audit;

internal class StatusTransition : AuditableEntityWithIdBase<Entities.Audit.StatusTransition>
{
    public StatusTransition(string schema = null) : base("STATUS_TRANSITION_CHANGES", schema)
    {
    }

    protected override void ConfigureOwnProperties(EntityTypeBuilder<Entities.Audit.StatusTransition> builder)
    {
        base.ConfigureOwnProperties(builder);

        builder.Property(x => x.IsDefault).HasColumnName("IS_DEFAULT").IsRequired();
        builder.Property(x => x.OperatorStatusFromId).HasColumnName("OERATOR_STATUS_FROM_ID").IsRequired();
        builder.Property(x => x.OperatorStatusToId).HasColumnName("OPERATOR_STATUS_TO_ID").IsRequired();
    }
}
