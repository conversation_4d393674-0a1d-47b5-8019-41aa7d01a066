using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Product.AWP.Infrastructure.DAL.Configurations.Audit.Base;

namespace Product.AWP.Infrastructure.DAL.Configurations.Audit;

internal class OperatorStatus : AuditableEntityWithIdBase<Entities.Audit.OperatorStatus>
{
    public OperatorStatus(string schema = null) : base("OPERATOR_STATUS_CHANGES", schema)
    {
    }

    protected override void ConfigureOwnProperties(EntityTypeBuilder<Entities.Audit.OperatorStatus> builder)
    {
        base.ConfigureOwnProperties(builder);

        builder.Property(x => x.Name).HasColumnName("NAME").IsRequired();
        builder.Property(x => x.Code).HasColumnName("CODE").HasMaxLength(50).IsRequired();
        builder.Property(x => x.IsStarting).HasColumnName("IS_STARTING").IsRequired();
        builder.Property(x => x.IsTerminating).HasColumnName("IS_TERMINATING").IsRequired();
        builder.Property(x => x.ValidityDuration).HasColumnName("VALIDITY_DURATION").IsRequired();
    }
}
