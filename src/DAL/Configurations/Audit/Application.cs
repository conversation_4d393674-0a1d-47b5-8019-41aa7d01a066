using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Product.AWP.Infrastructure.DAL.Configurations.Audit.Base;

namespace Product.AWP.Infrastructure.DAL.Configurations.Audit;

internal class Application : AuditableEntityWithIdBase<Entities.Audit.Application>
{
    public Application(string schema = null) : base("APPLICATION_CHANGES", schema)
    {
    }

    protected override void ConfigureOwnProperties(EntityTypeBuilder<Entities.Audit.Application> builder)
    {
        base.ConfigureOwnProperties(builder);

        builder.Property(x => x.Name).HasColumnName("NAME").HasMaxLength(100).IsRequired();
        builder.Property(x => x.Type).HasColumnName("TYPE").IsRequired();
        builder.Property(x => x.IsService).HasColumnName("IS_SERVICE").IsRequired();
        builder.Property(x => x.SortOrder).HasColumnName("SORT_ORDER").IsRequired();
        builder.Property(x => x.EnableAutoSignOn).HasColumnName("ENABLE_AUTO_SING_ON").IsRequired(false);
        builder.Property(x => x.Disabled).HasColumnName("DISABLED").IsRequired(false);
        builder.Property(x => x.CredentialsConfigurationName).HasColumnName("CREDENTIALS_CONFIGURATION_NAME").IsRequired(false);
        builder.Property(x => x.ManualStart).HasColumnName("MANUAL_START").IsRequired();
        builder.Property(x => x.LaunchOrder).HasColumnName("LAUNCH_ORDER").IsRequired();
        builder.Property(x => x.ToolbarImage).HasColumnName("TOOLBAR_IMAGE").IsRequired(false);
    }
}
