using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Product.AWP.Infrastructure.DAL.Configurations.Audit.Base;

namespace Product.AWP.Infrastructure.DAL.Configurations.Audit;

internal class LoginWorkflow : AuditableEntityWithIdBase<Entities.Audit.LoginWorkflow>
{
    public LoginWorkflow(string schema = null) : base("LOGIN_WORKFLOW_CHANGES", schema)
    {
    }

    protected override void ConfigureOwnProperties(EntityTypeBuilder<Entities.Audit.LoginWorkflow> builder)
    {
        base.ConfigureOwnProperties(builder);

        builder.Property(x => x.WorkflowDefinition).HasColumnName("WORKFLOW_DEFINITION").IsRequired();
        builder.Property(x => x.ApplicationId).HasColumnName("APPLICATION_ID").IsRequired();
    }
}
