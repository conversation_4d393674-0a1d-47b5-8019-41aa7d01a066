using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Product.AWP.Infrastructure.DAL.Configurations.Audit.Base;

namespace Product.AWP.Infrastructure.DAL.Configurations.Audit;

internal class WorkflowUserRole : AuditableEntityBase<Entities.Audit.WorkflowUserRole>
{
    public WorkflowUserRole(string schema = null) : base("WORKFLOW_USER_ROLE_CHANGES", schema)
    {
    }

    protected override void ConfigureOwnProperties(EntityTypeBuilder<Entities.Audit.WorkflowUserRole> builder)
    {
        builder.Property(x => x.WorkfolwId).HasColumnName("WORKFLOW_ID").IsRequired();
        builder.Property(x => x.UserRoleId).HasColumnName("USER_ROLE_ID").IsRequired();
    }
}
