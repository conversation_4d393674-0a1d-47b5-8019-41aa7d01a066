using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Product.AWP.Infrastructure.DAL.Configurations.Audit.Base;

namespace Product.AWP.Infrastructure.DAL.Configurations.Audit;

internal class OperatorGroupCustomAttribute : AuditableEntityBase<Entities.Audit.OperatorGroupCustomAttribute>
{
    public OperatorGroupCustomAttribute(string schema = null) : base("OPERATOR_GROUP_CA_CHANGES", schema)
    {
    }

    protected override void ConfigureOwnProperties(EntityTypeBuilder<Entities.Audit.OperatorGroupCustomAttribute> builder)
    {
        builder.Property(x => x.OperatorGroupId).HasColumnName("OPERATOR_GROUP_ID").IsRequired();
        builder.Property(x => x.Code).HasColumnName("CODE").IsRequired();
        builder.Property(x => x.StringValue).HasColumnName("STRING_VALUE").IsRequired(false);
        builder.Property(x => x.GuidValue).HasColumnName("GUID_VALUE").IsRequired(false);
        builder.Property(x => x.LongValue).HasColumnName("LONG_VALUE").IsRequired(false);
        builder.Property(x => x.DecimalValue).HasColumnName("DECIMAL_VALUE").IsRequired(false);
        builder.Property(x => x.BoolValue).HasColumnName("BOOL_VALUE").IsRequired(false);
        builder.Property(x => x.DateTimeValue).HasColumnName("DATETIME_VALUE").IsRequired(false);
        builder.Property(x => x.BinaryValue).HasColumnName("BINARY_VALUE").IsRequired(false);
    }
}
