using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Product.AWP.Infrastructure.DAL.Configurations.Audit.Base;

namespace Product.AWP.Infrastructure.DAL.Configurations.Audit;

internal class BaseShortcut : AuditableEntityWithIdBase<Entities.Audit.BaseShortcut>
{
    public BaseShortcut(string schema = null) : base("BASE_SHORTCUT_CHANGES", schema)
    {
    }

    protected override void ConfigureOwnProperties(EntityTypeBuilder<Entities.Audit.BaseShortcut> builder)
    {
        base.ConfigureOwnProperties(builder);

        builder.Property(x => x.Name).HasColumnName("NAME").HasMaxLength(100).IsRequired();
        builder.Property(x => x.Type).HasColumnName("TYPE").IsRequired();
        builder.Property(x => x.Action).HasColumnName("ACTION").IsRequired();
        builder.Property(x => x.Description).HasColumnName("DESCRIPTION").IsRequired(false);
        builder.Property(x => x.SortOrder).HasColumnName("SORT_ORDER").IsRequired();
    }
}
