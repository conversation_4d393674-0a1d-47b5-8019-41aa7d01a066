using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Product.AWP.Infrastructure.DAL.Configurations.Audit.Base;

namespace Product.AWP.Infrastructure.DAL.Configurations.Audit;

internal class OperatorOperatorGroup : AuditableEntityBase<Entities.Audit.OperatorOperatorGroup>
{
    public OperatorOperatorGroup(string schema = null) : base("OPERATOR_OPER_GROUP_CHANGES", schema)
    {
    }

    protected override void ConfigureOwnProperties(EntityTypeBuilder<Entities.Audit.OperatorOperatorGroup> builder)
    {
        builder.Property(x => x.OperatorId).HasColumnName("OPERATOR_ID").IsRequired();
        builder.Property(x => x.OperatorGroupId).HasColumnName("OPERATOR_GROUP_ID").IsRequired();
    }
}
