using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Product.AWP.Infrastructure.DAL.Entities.Audit;

namespace Product.AWP.Infrastructure.DAL.Configurations.Audit.Base;

public abstract class AuditableEntityBase<TEntity> : IEntityTypeConfiguration<TEntity>
    where TEntity : AuditEntityBase
{
    private readonly string _tableName;

    protected AuditableEntityBase(string tableName, string schema = null)
    {
        _tableName = tableName;
        Schema = schema;
    }

    protected string Schema { get; }

    public void Configure(EntityTypeBuilder<TEntity> builder)
    {
        builder.ToTable(_tableName, Schema);
        Configure<PERSON>ey(builder);

        builder.Property(x => x.InsertAuditId).HasColumnName("INSERT_AUDIT_ID").IsRequired(false);
        builder.Property(x => x.UpdateAuditId).HasColumnName("UPDATE_AUDIT_ID").IsRequired(false);
        builder.Property(x => x.DeleteAuditId).HasColumnName("DELETE_AUDIT_ID").IsRequired(false);

        builder.HasOne(x => x.InsertAudit).WithMany().HasForeignKey(t => t.InsertAuditId).OnDelete(DeleteBehavior.Cascade);
        builder.HasOne(x => x.UpdateAudit).WithMany().HasForeignKey(t => t.UpdateAuditId).OnDelete(DeleteBehavior.Cascade);
        builder.HasOne(x => x.DeleteAudit).WithMany().HasForeignKey(t => t.DeleteAuditId).OnDelete(DeleteBehavior.Cascade);

        ConfigureOwnProperties(builder);
    }

    protected virtual void ConfigureKey(EntityTypeBuilder<TEntity> builder)
    {
        builder.Property(x => x.Uid).HasColumnName("UID").IsRequired();
        builder.HasKey(x => x.Uid);
    }

    protected abstract void ConfigureOwnProperties(EntityTypeBuilder<TEntity> builder);
}
