using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Product.AWP.Infrastructure.DAL.Entities.Audit;

namespace Product.AWP.Infrastructure.DAL.Configurations.Audit.Base;

public abstract class AuditableEntityWithIdBase<TEntity> : AuditableEntityBase<TEntity>
    where TEntity : AuditEntityWithIdBase
{
    protected AuditableEntityWithIdBase(string tableName, string schema = null) : base(tableName, schema)
    {
    }

    protected override void ConfigureOwnProperties(EntityTypeBuilder<TEntity> builder)
    {
        builder.Property(x => x.Id).HasColumnName("ID").IsRequired();
    }
}
