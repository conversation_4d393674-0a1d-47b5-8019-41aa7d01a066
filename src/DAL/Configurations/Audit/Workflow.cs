using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Product.AWP.Infrastructure.DAL.Configurations.Audit.Base;

namespace Product.AWP.Infrastructure.DAL.Configurations.Audit;

internal class Workflow : AuditableEntityWithIdBase<Entities.Audit.Workflow>
{
    public Workflow(string schema = null) : base("WORKFLOW_CHANGES", schema)
    {
    }

    protected override void ConfigureOwnProperties(EntityTypeBuilder<Entities.Audit.Workflow> builder)
    {
        base.ConfigureOwnProperties(builder);

        builder.Property(x => x.Name).HasColumnName("NAME").IsRequired();
        builder.Property(x => x.Order).HasColumnName("ORDER").IsRequired();
        builder.Property(x => x.Group).HasColumnName("GROUP").IsRequired();
        builder.Property(x => x.Description).HasColumnName("DESCRIPTION").IsRequired();

        builder.Property(x => x.SerializedValue).HasColumnName("SERIALIZED_VALUE").IsRequired();
        builder.Property(x => x.EventThreadOption).HasColumnName("EVENT_THREAD_OPTION").IsRequired();
        builder.Property(x => x.InvokeAsync).HasColumnName("INVOKE_ASYNC").IsRequired();

        builder.Property(x => x.IncludeInStatistics).HasColumnName("INCLUDE_IN_STATISTICS").IsRequired();
        builder.Property(x => x.IsBusinessScenario).HasColumnName("IS_BUSINESS_SCENARIO").IsRequired();
        builder.Property(x => x.IsService).HasColumnName("IS_SERVICE").IsRequired();

        builder.Property(x => x.Code).HasColumnName("CODE").HasMaxLength(50).IsRequired(false);
        builder.Property(x => x.Icon).HasColumnName("ICON").IsRequired(false);

        builder.Property(x => x.UseConfiguration).HasColumnName("USE_CONFIGURATION").IsRequired();
        builder.Property(x => x.ConfigurationValue).HasColumnName("CONFIGURATION_VALUE").IsRequired(false);

        builder.OwnsOne(
            x => x.Configuration,
            c =>
            {
                c.Property(x => x.Assembly).HasColumnName("CONFIGURATION_ASSEMBLY").IsRequired(false);
                c.Property(x => x.TypeName).HasColumnName("CONFIGURATION_TYPENAME").IsRequired(false);
            });

        builder.OwnsOne(
            x => x.SummaryControl,
            sc =>
            {
                sc.Property(x => x.Assembly).HasColumnName("SUMMARY_CONTROL_ASSEMBLY").IsRequired(false);
                sc.Property(x => x.TypeName).HasColumnName("SUMMARY_CONTROL_TYPENAME").IsRequired(false);
            });

        builder.OwnsOne(
            x => x.Event,
            e =>
            {
                e.Property(x => x.Assembly).HasColumnName("EVENT_ASSEMBLY").IsRequired(false);
                e.Property(x => x.TypeName).HasColumnName("EVENT_TYPENAME").IsRequired(false);
            });

        builder.Property(x => x.WorkflowGroupId).HasColumnName("WORKFLOW_GROUP_ID").IsRequired(false);
    }
}
