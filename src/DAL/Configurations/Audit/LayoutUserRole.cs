using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Product.AWP.Infrastructure.DAL.Configurations.Audit.Base;

namespace Product.AWP.Infrastructure.DAL.Configurations.Audit;

internal class LayoutUserRole : AuditableEntityBase<Entities.Audit.LayoutUserRole>
{
    public LayoutUserRole(string schema = null) : base("LAYOUT_USER_ROLE_CHANGES", schema)
    {
    }

    protected override void ConfigureOwnProperties(EntityTypeBuilder<Entities.Audit.LayoutUserRole> builder)
    {
        builder.Property(x => x.LayoutId).HasColumnName("LAYOUT_ID").IsRequired();
        builder.Property(x => x.UserRoleId).HasColumnName("USER_ROLE_ID").IsRequired();
    }
}
