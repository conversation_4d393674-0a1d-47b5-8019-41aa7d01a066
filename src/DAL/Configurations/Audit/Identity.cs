using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Product.AWP.Infrastructure.DAL.Configurations.Audit.Base;

namespace Product.AWP.Infrastructure.DAL.Configurations.Audit;

internal class Identity : AuditableEntityWithIdBase<Entities.Audit.Identity>
{
    public Identity(string schema = null)
        : base("IDENTITIES", schema)
    {
    }

    protected override void ConfigureOwnProperties(EntityTypeBuilder<Entities.Audit.Identity> builder)
    {
        base.ConfigureOwnProperties(builder);

        builder.Property(x => x.Login).HasColumnName("LOGIN").HasMaxLength(100).IsRequired();
    }
}
