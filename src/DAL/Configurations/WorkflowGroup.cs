using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Product.AWP.Infrastructure.DAL.Configurations.BaseConfigurations;

namespace Product.AWP.Infrastructure.DAL.Configurations;

internal class WorkflowGroup : HasReadOnlyIdEntityBase<Entities.WorkflowGroup>
{
    public WorkflowGroup(string schema = null)
        : base("WorkflowGroups", schema)
    {
    }

    protected override void ConfigureProperties(EntityTypeBuilder<Entities.WorkflowGroup> builder)
    {
        builder.Property(x => x.Name).HasColumnName("Name").HasMaxLength(50).IsRequired();
        builder.Property(x => x.Description).HasColumnName("Description").HasMaxLength(1000).IsRequired(false);
        builder.Property(x => x.Image).HasColumnName("Image").IsRequired(false);
        builder.Property(x => x.Order).HasColumnName("Order").IsRequired(false);
    }
}
