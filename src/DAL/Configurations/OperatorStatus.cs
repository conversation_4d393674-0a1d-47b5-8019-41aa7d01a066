using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Product.AWP.Infrastructure.DAL.Configurations.BaseConfigurations;

namespace Product.AWP.Infrastructure.DAL.Configurations;

internal class OperatorStatus : HasIdEntityBase<Entities.OperatorStatus>
{
    public OperatorStatus(string schema = null)
        : base("OperatorStatuses", schema)
    {
    }

    protected override void ConfigureProperties(EntityTypeBuilder<Entities.OperatorStatus> builder)
    {
        builder.Property(x => x.Name).HasColumnName("Name").IsRequired();
        builder.Property(x => x.Code).HasColumnName("Code").HasMaxLength(50).IsRequired();
        builder.Property(x => x.IsStarting).HasColumnName("IsStarting").IsRequired();
        builder.Property(x => x.IsTerminating).HasColumnName("IsTerminating").IsRequired();
        builder.Property(x => x.ValidityDuration).HasColumnName("ValidityDuration").IsRequired();

        builder.HasMany(x => x.UserRoles).WithMany(x => x.OperatorStatuses)
            .UsingEntity<Dictionary<string, object>>(
                "OperatorStatusUserRole",
                x => x.HasOne<Entities.UserRole>().WithMany().HasForeignKey("UserRoleId"),
                x => x.HasOne<Entities.OperatorStatus>().WithMany().HasForeignKey("OperatorStatusId"),
                jet => jet.ToTable("OperatorStatusUserRole", Schema)
            );
    }
}
