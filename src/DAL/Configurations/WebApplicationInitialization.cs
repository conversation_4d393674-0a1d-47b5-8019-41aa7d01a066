using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Product.AWP.Infrastructure.DAL.Configurations.BaseConfigurations;

namespace Product.AWP.Infrastructure.DAL.Configurations;

internal class WebApplicationInitialization : HasIdEntityBase<Entities.WebApplicationInitialization>
{
    public WebApplicationInitialization(string schema = null)
        : base("AppInit_Web", schema)
    {
    }

    protected override void ConfigureKey(EntityTypeBuilder<Entities.WebApplicationInitialization> builder)
    {
        //base.ConfigureKey(builder);
    }

    protected override void ConfigureProperties(EntityTypeBuilder<Entities.WebApplicationInitialization> builder)
    {
        builder.Property(x => x.StartUrl).HasColumnName("StartUrl").IsRequired();
        builder.Property(x => x.SuppressScriptErrors).HasColumnName("SuppressScriptErrors").IsRequired();
    }
}
