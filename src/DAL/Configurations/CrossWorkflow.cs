using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Product.AWP.Infrastructure.DAL.Configurations.BaseConfigurations;

namespace Product.AWP.Infrastructure.DAL.Configurations;

internal class CrossWorkflow : HasReadOnlyIdEntityBase<Entities.CrossWorkflow>
{
    public CrossWorkflow(string schema = null)
        : base("CrossWorkflows", schema)
    {
    }

    protected override void ConfigureProperties(EntityTypeBuilder<Entities.CrossWorkflow> builder)
    {
        builder.Property(x => x.Order).HasColumnName("Order").IsRequired();
        builder.Property(x => x.HostWorkflowId).HasColumnName("HostWorkflowId").IsRequired();
        builder.Property(x => x.TargetWorkflowId).HasColumnName("TargetWorkflowId").IsRequired();

        builder.HasOne(x => x.HostWorkflow).WithMany(x => x.CrossWorkflows).HasForeignKey(x => x.HostWorkflowId).IsRequired().OnDelete(DeleteBehavior.NoAction);
        builder.HasOne(x => x.TargetWorkflow).WithMany(x => x.CrossWorkflowsToHere).HasForeignKey(x => x.TargetWorkflowId).IsRequired().OnDelete(DeleteBehavior.NoAction);
    }
}
