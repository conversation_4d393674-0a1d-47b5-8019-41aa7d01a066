using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Product.AWP.Infrastructure.DAL.Configurations.BaseConfigurations;

namespace Product.AWP.Infrastructure.DAL.Configurations;

internal class ServiceAreaAudit : HasReadOnlyIdEntityBase<Entities.ServiceAreaAudit>
{
    public ServiceAreaAudit(string schema = null)
        : base("ServiceAreaAudits", schema)
    {
    }

    protected override void ConfigureProperties(EntityTypeBuilder<Entities.ServiceAreaAudit> builder)
    {
        builder.Property(x => x.ChangeCode).HasColumnName("ChangeCode").HasMaxLength(100).IsRequired();
        builder.Property(x => x.ChangeTime).HasColumnName("ChangeTime").IsRequired();

        builder.Property(x => x.PreviousServiceAreaId).HasColumnName("PreviousServiceAreaId").IsRequired(false);
        builder.Property(x => x.CurrentServiceAreaId).HasColumnName("CurrentServiceAreaId").IsRequired(false);
    }
}
