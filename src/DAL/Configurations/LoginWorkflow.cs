using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Product.AWP.Infrastructure.DAL.Configurations.BaseConfigurations;

namespace Product.AWP.Infrastructure.DAL.Configurations;

internal class LoginWorkflow : HasIdEntityBase<Entities.LoginWorkflow>
{
    public LoginWorkflow(string schema = null)
        : base("LoginWorkflows", schema)
    {
    }

    protected override void ConfigureProperties(EntityTypeBuilder<Entities.LoginWorkflow> builder)
    {
        builder.Property(x => x.WorkflowDefinition).HasColumnName("WorkflowDefinition").IsRequired();
        builder.Property(x => x.ApplicationId).HasColumnName("ApplicationId").IsRequired();
        builder.Property(x => x.ClientTypes).HasColumnName("ClientTypes").IsRequired().HasDefaultValue(1);

        builder.HasOne(x => x.Application).WithMany(x => x.LoginWorkflows).HasForeignKey(x => x.ApplicationId).IsRequired().OnDelete(DeleteBehavior.NoAction);
    }
}
