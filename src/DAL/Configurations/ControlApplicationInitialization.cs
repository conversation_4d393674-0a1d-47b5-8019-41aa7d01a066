using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Product.AWP.Infrastructure.DAL.Configurations.BaseConfigurations;

namespace Product.AWP.Infrastructure.DAL.Configurations;

internal class ControlApplicationInitialization : HasIdEntityBase<Entities.ControlApplicationInitialization>
{
    public ControlApplicationInitialization(string schema = null)
        : base("AppInit_Control", schema)
    {
    }

    protected override void ConfigureKey(EntityTypeBuilder<Entities.ControlApplicationInitialization> builder)
    {
        //base.ConfigureKey(builder);
    }

    protected override void ConfigureProperties(EntityTypeBuilder<Entities.ControlApplicationInitialization> builder)
    {
        builder.Property(x => x.ControlInitialization).HasColumnName("ControlInitialization").IsRequired(false);
        builder.Property(x => x.ControlPerformsLogin).HasColumnName("ControlPerformsLogin").IsRequired();

        builder.OwnsOne(
            x => x.Control,
            c =>
            {
                c.Property(x => x.Assembly).HasColumnName("Control_Assembly").IsRequired(false);
                c.Property(x => x.TypeName).HasColumnName("Control_TypeName").IsRequired(false);
            });
    }
}
