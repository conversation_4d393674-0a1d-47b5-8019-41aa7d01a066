using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Product.AWP.Infrastructure.DAL.Configurations.BaseConfigurations;

namespace Product.AWP.Infrastructure.DAL.Configurations;

internal class WorkSession : HasIdEntityBase<Entities.WorkSession>
{
    public WorkSession(string schema = null)
        : base("WorkSessions", schema)
    {
    }

    protected override void ConfigureProperties(EntityTypeBuilder<Entities.WorkSession> builder)
    {
        builder.Property(x => x.OperatorId).HasColumnName("OperatorId").IsRequired();
        builder.Property(x => x.ClientType).HasColumnName("ClientType").IsRequired(false);
        builder.Property(x => x.Description).HasColumnName("Description").IsRequired(false);

        builder.Property(x => x.UserRoleId).HasColumnName("UserRoleId").IsRequired();
        builder.Property(x => x.ServiceAreaId).HasColumnName("ServiceAreaId").IsRequired();
        builder.Property(x => x.WorkplaceId).HasColumnName("WORKPLACE_ID").IsRequired();

        builder.Property(x => x.StartDate).HasColumnName("StartDate").IsRequired();
        builder.Property(x => x.EndDate).HasColumnName("EndDate").IsRequired(false);
        builder.Property(x => x.ValidUntil).HasColumnName("VALID_UNTIL").IsRequired(false);
    }
}
