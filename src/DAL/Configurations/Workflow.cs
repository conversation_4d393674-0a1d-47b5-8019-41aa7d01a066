using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Product.AWP.Infrastructure.DAL.Configurations.BaseConfigurations;

namespace Product.AWP.Infrastructure.DAL.Configurations;

internal class Workflow : HasIdEntityBase<Entities.Workflow>
{
    public Workflow(string schema = null)
        : base("Workflows", schema)
    {
    }

    protected override void ConfigureProperties(EntityTypeBuilder<Entities.Workflow> builder)
    {
        builder.Property(x => x.Name).HasColumnName("Name").IsRequired();
        builder.Property(x => x.ClientTypes).HasColumnName("ClientTypes").IsRequired().HasDefaultValue(1);
        builder.Property(x => x.Order).HasColumnName("Order").IsRequired();
        builder.Property(x => x.Group).HasColumnName("Group").IsRequired();
        builder.Property(x => x.Description).HasColumnName("Description").IsRequired();

        builder.Property(x => x.SerializedValue).HasColumnName("SerializedValue").IsRequired();
        builder.Property(x => x.EventThreadOption).HasColumnName("EventThreadOption").IsRequired();
        builder.Property(x => x.InvokeAsync).HasColumnName("InvokeAsync").IsRequired();

        builder.Property(x => x.IsBusinessScenario).HasColumnName("IsBusinessScenario").IsRequired();
        builder.Property(x => x.IsService).HasColumnName("IsService").IsRequired();

        builder.Property(x => x.Code).HasColumnName("Code").HasMaxLength(50).IsRequired(false);
        builder.Property(x => x.Icon).HasColumnName("Icon").IsRequired(false);

        builder.Property(x => x.UseConfiguration).HasColumnName("UseConfiguration").IsRequired();
        builder.Property(x => x.ConfigurationValue).HasColumnName("ConfigurationValue").IsRequired(false);

        builder.OwnsOne(
            x => x.Configuration,
            c =>
            {
                c.Property(x => x.Assembly).HasColumnName("Configuration_Assembly").IsRequired(false);
                c.Property(x => x.TypeName).HasColumnName("Configuration_TypeName").IsRequired(false);
            });

        builder.OwnsOne(
            x => x.SummaryControl,
            sc =>
            {
                sc.Property(x => x.Assembly).HasColumnName("SummaryControl_Assembly").IsRequired(false);
                sc.Property(x => x.TypeName).HasColumnName("SummaryControl_TypeName").IsRequired(false);
            });

        builder.OwnsOne(
            x => x.Event,
            e =>
            {
                e.Property(x => x.Assembly).HasColumnName("Event_Assembly").IsRequired(false);
                e.Property(x => x.TypeName).HasColumnName("Event_TypeName").IsRequired(false);
            });

        builder.Property(x => x.WorkflowGroupId).HasColumnName("WorkflowGroupId").IsRequired(false);

        //	foreign keys
        builder.HasOne(x => x.WorkflowGroup).WithMany(x => x.Workflows).HasForeignKey(x => x.WorkflowGroupId).IsRequired(false);
        builder.HasMany(e => e.UserRoles).WithMany(e => e.Workflows)
            .UsingEntity<Dictionary<string, object>>(
                "WorkflowUserRole",
                x => x.HasOne<Entities.UserRole>().WithMany().HasForeignKey("UserRoleId"),
                x => x.HasOne<Entities.Workflow>().WithMany().HasForeignKey("WorkflowId"),
                jet => jet.ToTable("WorkflowUserRole", Schema)
            );
    }
}
