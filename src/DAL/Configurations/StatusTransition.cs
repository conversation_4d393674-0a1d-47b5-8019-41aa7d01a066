using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Product.AWP.Infrastructure.DAL.Configurations.BaseConfigurations;

namespace Product.AWP.Infrastructure.DAL.Configurations;

internal class StatusTransition : HasReadOnlyIdEntityBase<Entities.StatusTransition>
{
    public StatusTransition(string schema = null)
        : base("StatusTransitions", schema)
    {
    }

    protected override void ConfigureProperties(EntityTypeBuilder<Entities.StatusTransition> builder)
    {
        builder.Property(x => x.IsDefault).HasColumnName("IsDefault").IsRequired();
        builder.Property(x => x.OperatorStatusFromId).HasColumnName("OperatorStatusFromId").IsRequired();
        builder.Property(x => x.OperatorStatusToId).HasColumnName("OperatorStatusToId").IsRequired();

        builder.HasOne(x => x.OperatorStatusFrom).WithMany(x => x.StatusTransitionsFromHere).HasForeignKey(x => x.OperatorStatusFromId).IsRequired().OnDelete(DeleteBehavior.NoAction);
        builder.HasOne(x => x.OperatorStatusTo).WithMany(x => x.StatusTransitionsToHere).HasForeignKey(x => x.OperatorStatusToId).IsRequired().OnDelete(DeleteBehavior.NoAction);
    }
}
