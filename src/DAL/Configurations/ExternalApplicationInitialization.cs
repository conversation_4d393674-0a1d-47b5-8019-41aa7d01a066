using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Product.AWP.Infrastructure.DAL.Configurations.BaseConfigurations;

namespace Product.AWP.Infrastructure.DAL.Configurations;

internal class ExternalApplicationInitialization : HasIdEntityBase<Entities.ExternalApplicationInitialization>
{
    public ExternalApplicationInitialization(string schema = null)
        : base("AppInit_External", schema)
    {
    }

    protected override void ConfigureKey(EntityTypeBuilder<Entities.ExternalApplicationInitialization> builder)
    {
        //base.ConfigureKey(builder);
    }

    protected override void ConfigureProperties(EntityTypeBuilder<Entities.ExternalApplicationInitialization> builder)
    {
        builder.Property(x => x.AcquisitionTimeout).HasColumnName("AcquisitionTimeout").IsRequired(false);

        builder.OwnsOne(
            x => x.ExternalApplication,
            ea =>
            {
                ea.Property(x => x.Path).HasColumnName("ExtApp_Path").IsRequired();
                ea.Property(x => x.Arguments).HasColumnName("ExtApp_Arguments").IsRequired(false);
                ea.Property(x => x.WorkingDirectory).HasColumnName("ExtApp_WorkingDirectory").IsRequired(false);
            });

        builder.OwnsOne(
            x => x.UseTopLevelWindow,
            tlw =>
            {
                tlw.Property(x => x.Caption).HasColumnName("TLW_Caption").IsRequired(false);
                tlw.Property(x => x.ClassName).HasColumnName("TLW_ClassName").IsRequired(false);
                tlw.Property(x => x.LimitToProcess).HasColumnName("TLW_LimitToProcess").IsRequired();
                tlw.Property(x => x.UseProcessMainWindow).HasColumnName("TLW_UseProcessMainWindow").IsRequired();
                tlw.Property(x => x.SearchWindowAfterLogin).HasColumnName("TLW_SearchWindowAfterLogin").IsRequired();
            });
    }
}
