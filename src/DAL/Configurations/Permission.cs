using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Product.AWP.Infrastructure.DAL.Configurations.BaseConfigurations;

namespace Product.AWP.Infrastructure.DAL.Configurations;

internal class Permission : HasIdEntityBase<Entities.Permission>
{
    public Permission(string schema = null)
        : base("Permissions", schema)
    {
    }

    protected override void ConfigureProperties(EntityTypeBuilder<Entities.Permission> builder)
    {
        builder.Property(x => x.Name).HasColumnName("Name").HasMaxLength(50).IsRequired();
        builder.Property(x => x.Description).HasColumnName("Description").HasMaxLength(1000).IsRequired(false);

        builder.HasMany(e => e.UserRoles).WithMany(e => e.Permissions)
            .UsingEntity<Dictionary<string, object>>(
                "PermissionUserRole",
                x => x.HasOne<Entities.UserRole>().WithMany().HasForeignKey("UserRoleId"),
                x => x.<PERSON>One<Entities.Permission>().WithMany().HasForeignKey("PermissionId"),
                jet => jet.ToTable("PermissionUserRole", Schema)
            );
    }
}
