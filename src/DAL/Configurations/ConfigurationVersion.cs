using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Product.AWP.Infrastructure.DAL.Configurations.BaseConfigurations;

namespace Product.AWP.Infrastructure.DAL.Configurations;

internal class ConfigurationVersion : HasReadOnlyIdEntityBase<Entities.ConfigurationVersion>
{
    public ConfigurationVersion(string schema = null)
        : base("ConfigurationVersions", schema)
    {
    }

    protected override void ConfigureProperties(EntityTypeBuilder<Entities.ConfigurationVersion> builder)
    {
        builder.Property(x => x.Name).HasColumnName("Name").IsRequired();
        builder.Property(x => x.VersionMarker).HasColumnName("VersionMarker").IsRequired();
    }
}
