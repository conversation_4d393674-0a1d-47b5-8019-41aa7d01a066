using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Product.AWP.Infrastructure.DAL.Configurations.BaseConfigurations;

namespace Product.AWP.Infrastructure.DAL.Configurations;

internal class OperatorCustomAttribute : EntityTypeConfigurationBase<Entities.OperatorCustomAttribute>
{
    public OperatorCustomAttribute(string schema = null)
        : base("OPERATOR_CUSTOM_ATTRIBUTES", schema)
    {
    }

    protected override void ConfigureKey(EntityTypeBuilder<Entities.OperatorCustomAttribute> builder)
    {
        builder.Property(x => x.OperatorId).HasColumnName("OPERATOR_ID").IsRequired();
        builder.Property(x => x.Code).HasColumnName("CODE").HasMaxLength(100).IsRequired();
        builder.HasKey(x => new {x.OperatorId, x.Code});
    }

    protected override void ConfigureProperties(EntityTypeBuilder<Entities.OperatorCustomAttribute> builder)
    {
        builder.Property(x => x.StringValue).HasColumnName("STRING_VALUE").IsRequired(false);
        builder.Property(x => x.GuidValue).HasColumnName("GUID_VALUE").IsRequired(false);
        builder.Property(x => x.LongValue).HasColumnName("LONG_VALUE").IsRequired(false);
        builder.Property(x => x.DecimalValue).HasColumnName("DECIMAL_VALUE").IsRequired(false);
        builder.Property(x => x.BoolValue).HasColumnName("BOOL_VALUE").IsRequired(false);
        builder.Property(x => x.DateTimeValue).HasColumnName("DATETIME_VALUE").IsRequired(false);
        builder.Property(x => x.BinaryValue).HasColumnName("BINARY_VALUE").IsRequired(false);

        builder.HasOne(x => x.Operator).WithMany(x => x.CustomAttributes).HasForeignKey(x => x.OperatorId).IsRequired();
    }
}
