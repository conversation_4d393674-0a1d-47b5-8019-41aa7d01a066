using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Product.AWP.Infrastructure.DAL.Configurations.BaseConfigurations;

namespace Product.AWP.Infrastructure.DAL.Configurations;

internal class Layout : HasIdEntityBase<Entities.Layout>
{
    public Layout(string schema = null)
        : base("Layouts", schema)
    {
    }

    protected override void ConfigureProperties(EntityTypeBuilder<Entities.Layout> builder)
    {
        builder.Property(x => x.Name).HasColumnName("Name").HasMaxLength(50).IsRequired();
        builder.Property(x => x.Value).HasColumnName("Value").IsRequired();
        builder.Property(x => x.Order).HasColumnName("Order").IsRequired();
        builder.Property(x => x.Description).HasColumnName("Description").HasMaxLength(1000).IsRequired(false);

        builder.HasMany(e => e.UserRoles).WithMany(e => e.Layouts)
            .UsingEntity<Dictionary<string, object>>(
                "LayoutUserRole",
                x => x.HasOne<Entities.UserRole>().WithMany().HasForeignKey("UserRoleId"),
                x => x.HasOne<Entities.Layout>().WithMany().HasForeignKey("LayoutId"),
                jet => jet.ToTable("LayoutUserRole", Schema)
            );
    }
}
