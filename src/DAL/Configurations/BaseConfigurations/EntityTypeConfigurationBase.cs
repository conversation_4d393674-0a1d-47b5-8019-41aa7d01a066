using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Product.AWP.Infrastructure.DAL.Configurations.BaseConfigurations;

internal abstract class EntityTypeConfigurationBase<TEntity> : IEntityTypeConfiguration<TEntity>
    where TEntity : class
{
    private readonly string _tableName;

    protected EntityTypeConfigurationBase(string tableName, string schema)
    {
        _tableName = tableName;
        Schema = schema;
    }

    protected string Schema { get; }

    public void Configure(EntityTypeBuilder<TEntity> builder)
    {
        builder.ToTable(_tableName, Schema);
        ConfigureKey(builder);
        ConfigureProperties(builder);
    }

    protected abstract void ConfigureKey(EntityTypeBuilder<TEntity> builder);

    protected abstract void ConfigureProperties(EntityTypeBuilder<TEntity> builder);
}
