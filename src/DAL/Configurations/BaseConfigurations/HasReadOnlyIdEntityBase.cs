using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Product.AWP.Infrastructure.DAL.Entities.BaseClasses;

namespace Product.AWP.Infrastructure.DAL.Configurations.BaseConfigurations;

internal abstract class HasReadOnlyIdEntityBase<TEntity> : EntityTypeConfigurationBase<TEntity>
    where TEntity : HasReadOnlyIdEntityBase
{
    protected HasReadOnlyIdEntityBase(string tableName, string schema)
        : base(tableName, schema)
    {
    }

    protected override void ConfigureKey(EntityTypeBuilder<TEntity> builder)
    {
        builder.Property(x => x.Id).HasColumnName("Id").IsRequired();
        builder.HasKey(x => x.Id);
    }
}
