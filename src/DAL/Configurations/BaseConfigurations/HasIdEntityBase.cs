using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Product.AWP.Infrastructure.DAL.Entities.BaseClasses;

namespace Product.AWP.Infrastructure.DAL.Configurations.BaseConfigurations;

internal abstract class HasIdEntityBase<TEntity> : EntityTypeConfigurationBase<TEntity>
    where TEntity : HasIdEntityBase
{
    protected HasIdEntityBase(string tableName, string schema)
        : base(tableName, schema)
    {
    }

    protected override void ConfigureKey(EntityTypeBuilder<TEntity> builder)
    {
        builder.Property(x => x.Id).HasColumnName("Id").IsRequired();
        builder.HasKey(x => x.Id);
    }
}
