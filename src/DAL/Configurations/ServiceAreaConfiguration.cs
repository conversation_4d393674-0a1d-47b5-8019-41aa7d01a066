using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Product.AWP.Infrastructure.DAL.Configurations.BaseConfigurations;

namespace Product.AWP.Infrastructure.DAL.Configurations;

internal class ServiceAreaConfiguration : HasIdEntityBase<Entities.ServiceAreaConfiguration>
{
    public ServiceAreaConfiguration(string schema = null)
        : base("ServiceAreaConfigurations", schema)
    {
    }

    protected override void ConfigureProperties(EntityTypeBuilder<Entities.ServiceAreaConfiguration> builder)
    {
        builder.Property(x => x.ServiceAreaId).HasColumnName("ServiceAreaId").IsRequired();
        builder.Property(x => x.PropertyKey).HasColumnName("PropertyKey").HasMaxLength(500).IsRequired();

        builder.Property(x => x.Value).HasColumnName("Value").IsRequired(false);
        builder.Property(x => x.ShortValue).HasColumnName("ShortValue").IsRequired(false);
        builder.Property(x => x.IntValue).HasColumnName("IntValue").IsRequired(false);
        builder.Property(x => x.DoubleValue).HasColumnName("DoubleValue").IsRequired(false);
        builder.Property(x => x.LongValue).HasColumnName("LongValue").IsRequired(false);
        builder.Property(x => x.GuidValue).HasColumnName("GuidValue").IsRequired(false);
        builder.Property(x => x.DateTimeValue).HasColumnName("DateTimeValue").IsRequired(false);
        builder.Property(x => x.BoolValue).HasColumnName("BoolValue").IsRequired(false);

        builder.Property(x => x.ConfigurationId).HasColumnName("ConfigurationId").IsRequired(false);
        builder.Property(x => x.ApplicationId).HasColumnName("ApplicationId").IsRequired(false);
        builder.Property(x => x.ModuleId).HasColumnName("ModuleId").IsRequired(false);
        builder.Property(x => x.ShortcutId).HasColumnName("ShortcutId").IsRequired(false);
        builder.Property(x => x.AesQueueId).HasColumnName("AesQueueId").IsRequired(false);
    }
}
