using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Product.AWP.Infrastructure.DAL.Configurations.BaseConfigurations;

namespace Product.AWP.Infrastructure.DAL.Configurations;

internal class AesQueue : HasIdEntityBase<Entities.AesQueue>
{
    public AesQueue(string schema = null)
        : base("AesQueues", schema)
    {
    }

    protected override void ConfigureProperties(EntityTypeBuilder<Entities.AesQueue> builder)
    {
        builder.Property(x => x.Name).HasColumnName("Name").HasMaxLength(100).IsRequired();
        builder.Property(x => x.Extension).HasColumnName("Extension").HasMaxLength(50).IsRequired();
        builder.Property(x => x.Description).HasColumnName("Description").HasMaxLength(1000).IsRequired(false);
        builder.Property(x => x.Aggregatable).HasColumnName("Aggregatable").IsRequired(false);

        builder.HasMany(e => e.UserRoles).WithMany(e => e.AesQueues)
            .UsingEntity<Dictionary<string, object>>(
                "AesQueueUserRole",
                x => x.HasOne<Entities.UserRole>().WithMany().HasForeignKey("UserRoleId"),
                x => x.HasOne<Entities.AesQueue>().WithMany().HasForeignKey("AesQueueId"),
                jet => jet.ToTable("AesQueueUserRole", Schema)
            );
    }
}
