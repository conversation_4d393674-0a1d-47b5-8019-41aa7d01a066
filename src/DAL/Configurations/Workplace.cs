using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Product.AWP.Infrastructure.DAL.Configurations.BaseConfigurations;

namespace Product.AWP.Infrastructure.DAL.Configurations;

internal class Workplace : HasIdEntityBase<Entities.Workplace>
{
    public Workplace(string schema = null)
        : base("WORKPLACES", schema)
    {
    }

    protected override void ConfigureProperties(EntityTypeBuilder<Entities.Workplace> builder)
    {
        builder.Property(x => x.Name).HasColumnName("NAME").IsRequired();
        builder.Property(x => x.Code).HasColumnName("CODE").IsRequired();
        builder.Property(x => x.UtcOffset).HasColumnName("UTC_OFFSET").IsRequired();
        builder.Property(x => x.ParentId).HasColumnName("PARENT_ID").IsRequired(false);

        builder.HasOne(x => x.Parent).WithMany(x => x.Children).HasForeignKey(x => x.ParentId).IsRequired(false);
    }
}
