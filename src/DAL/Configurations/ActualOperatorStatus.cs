using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Product.AWP.Infrastructure.DAL.Configurations.BaseConfigurations;

namespace Product.AWP.Infrastructure.DAL.Configurations;

internal class ActualOperatorStatus : HasIdEntityBase<Entities.ActualOperatorStatus>
{
    public ActualOperatorStatus(string schema = null)
        : base("ActualOperatorStatuses", schema)
    {
    }

    protected override void ConfigureProperties(EntityTypeBuilder<Entities.ActualOperatorStatus> builder)
    {
        builder.Property(x => x.OperatorId).HasColumnName("OperatorId").IsRequired();
        builder.Property(x => x.DateFrom).HasColumnName("DateFrom").IsRequired();
        builder.Property(x => x.ValidUntil).HasColumnName("ValidUntil").IsRequired(false);
        builder.Property(x => x.ActualStatusSetCode).HasColumnName("ActualStatusSetCode").IsRequired(false);
        builder.Property(x => x.ActualStatusId).HasColumnName("ActualStatusId").IsRequired();
        builder.Property(x => x.PreviousStatusId).HasColumnName("PreviousStatusId").IsRequired(false);
        builder.Property(x => x.ActualPendingStatusId).HasColumnName("ActualPendingStatusId").IsRequired(false);
        builder.Property(x => x.PreviousPendingStatusId).HasColumnName("PreviousPendingStatusId").IsRequired(false);
        builder.Property(x => x.WorkSessionId).HasColumnName("WorkSessionId").IsRequired(false);

        //	foreign keys
        builder.HasOne(x => x.Operator).WithMany().HasForeignKey(x => x.OperatorId).IsRequired();
        builder.HasOne(x => x.CurrentOperatorStatus).WithMany().HasForeignKey(x => x.ActualStatusId).IsRequired().OnDelete(DeleteBehavior.NoAction);
        builder.HasOne(x => x.PreviousOperatorStatus).WithMany().HasForeignKey(x => x.PreviousStatusId).IsRequired(false).OnDelete(DeleteBehavior.NoAction);
        builder.HasOne(x => x.WorkSession).WithMany().HasForeignKey(x => x.WorkSessionId).IsRequired(false).OnDelete(DeleteBehavior.NoAction);
    }
}
