using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Product.AWP.Infrastructure.DAL.Interfaces.Audit;

namespace Product.AWP.Infrastructure.DAL;

public class AuditDataProxy : AuditDataProxyBase
{
    public AuditDataProxy(IConfiguration configuration, ILoggerFactory loggerFactory) : base(configuration, loggerFactory)
    {
    }

    protected override IAuditDbContext CreateDataContext()
    {
        var result = Configuration != null ? new AuditPostgreSqlDbContext(Configuration, DbContextDefaultSchema) : new AuditPostgreSqlDbContext(DbContextDefaultSchema);
        result.ChangeTracker.AutoDetectChangesEnabled = false;
        return result;
    }
}
