using System.Transactions;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.Extensions.Configuration;
using Platform.Common.Collections;
using Product.AWP.Infrastructure.DAL.Entities;
using Product.AWP.Infrastructure.DAL.Entities.BaseClasses;
using Product.AWP.Infrastructure.DAL.Interfaces;
using Product.AWP.Infrastructure.DAL.Interfaces.Audit;
using WebClientApplicationInitialization = Product.AWP.Infrastructure.DAL.Configurations.WebClientApplicationInitialization;
using AuditableEntityState = Product.AWP.Infrastructure.DAL.Entities.Audit.AuditableEntityState;

#pragma warning disable 1591

namespace Product.AWP.Infrastructure.DAL;

public class InfrastructureDataModelBase : DbContext, IInfrastructureDataModel
{
    protected const string DefaultConnectionStringName = "AWP.Infrastructure";
    protected const string DefaultDefaultSchema = "AWP_INFRA";
    private static readonly Type LastChangeTimeEntityType = typeof(LastChangeTimeEntityBase);

    private IAuditor _auditor;

    protected InfrastructureDataModelBase(string defaultSchema)
    {
        DefaultSchema = defaultSchema;
        //Database.Log = Log;
    }

    protected InfrastructureDataModelBase(IConfiguration configuration, string defaultSchema)
        : this(defaultSchema)
    {
        Configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
    }

    protected IConfiguration Configuration { get; }
    public string DefaultSchema { get; protected set; }

    public DbSet<TEntity> GetDbSet<TEntity>() where TEntity : class
    {
        return Set<TEntity>();
    }

    public new TEntity Add<TEntity>(TEntity entityToAdd) where TEntity : class
    {
        GetDbSet<TEntity>().Add(entityToAdd);
        return entityToAdd;
    }

    public async Task<TEntity> AddAsync<TEntity>(TEntity entityToAdd) where TEntity : class
    {
        await GetDbSet<TEntity>().AddAsync(entityToAdd);
        return entityToAdd;
    }

    public TEntity Delete<TEntity>(TEntity entityToDelete) where TEntity : class
    {
        var dbSet = GetDbSet<TEntity>();
        dbSet.Remove(entityToDelete);
        return entityToDelete;
    }

    public new DatabaseFacade Database => base.Database;

    public async Task<int> SaveChangesAsync()
    {
        SetLastChangeTimes(DateTime.UtcNow);

        if (_auditor == null)
        {
            return await base.SaveChangesAsync();
        }

        PrepareAudit();

        using var scope = new TransactionScope(TransactionScopeOption.Required, new TransactionOptions {IsolationLevel = IsolationLevel.ReadCommitted}, TransactionScopeAsyncFlowOption.Enabled);
        var result = await base.SaveChangesAsync();
        await _auditor.SaveChangesAsync();
        scope.Complete();
        return result;
    }

    public void SetAuditor(IAuditor auditor)
    {
        _auditor = auditor;
    }

    public void InitAuditData(string dns, string login, string operationName)
    {
        _auditor?.InitAudit(dns, login, operationName);
    }

    protected string GetConnectionString(string connectionStringName)
    {
        return Configuration.GetConnectionString(connectionStringName) ?? throw new InvalidOperationException();
    }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        modelBuilder.HasDefaultSchema(DefaultSchema);

        modelBuilder.ApplyConfiguration(new Configurations.AdministrationObject());
        modelBuilder.ApplyConfiguration(new Configurations.AdministrationPermission());

        modelBuilder.ApplyConfiguration(new Configurations.UserGroup());
        modelBuilder.ApplyConfiguration(new Configurations.Identity());
        modelBuilder.ApplyConfiguration(new Configurations.UserRole());
        modelBuilder.ApplyConfiguration(new Configurations.Workplace());
        modelBuilder.ApplyConfiguration(new Configurations.Configuration());
        modelBuilder.ApplyConfiguration(new Configurations.CustomAttribute());
        modelBuilder.ApplyConfiguration(new Configurations.ConfigurationVersion());

        modelBuilder.ApplyConfiguration(new Configurations.ServiceAreaType());
        modelBuilder.ApplyConfiguration(new Configurations.ServiceArea());
        modelBuilder.ApplyConfiguration(new Configurations.ServiceAreaConfiguration());

        modelBuilder.ApplyConfiguration(new Configurations.Operator());
        modelBuilder.ApplyConfiguration(new Configurations.OperatorCustomAttribute());
        modelBuilder.ApplyConfiguration(new Configurations.OperatorGroup());
        modelBuilder.ApplyConfiguration(new Configurations.OperatorGroupCustomAttribute());
        modelBuilder.ApplyConfiguration(new Configurations.OperatorGroupOperatorLink());

        modelBuilder.ApplyConfiguration(new Configurations.OperatorStatus());
        modelBuilder.ApplyConfiguration(new Configurations.StatusTransition());

        modelBuilder.ApplyConfiguration(new Configurations.ActualOperatorStatus());
        modelBuilder.ApplyConfiguration(new Configurations.ServiceAreaAudit());

        modelBuilder.ApplyConfiguration(new Configurations.Module());
        modelBuilder.ApplyConfiguration(new Configurations.Application());
        modelBuilder.ApplyConfiguration(new Configurations.ApplicationInitialization());
        modelBuilder.ApplyConfiguration(new Configurations.ControlApplicationInitialization());
        modelBuilder.ApplyConfiguration(new Configurations.ExternalApplicationInitialization());
        modelBuilder.ApplyConfiguration(new Configurations.WebApplicationInitialization());
        modelBuilder.ApplyConfiguration(new WebClientApplicationInitialization());

        modelBuilder.ApplyConfiguration(new Configurations.Layout());
        modelBuilder.ApplyConfiguration(new Configurations.Permission());

        modelBuilder.ApplyConfiguration(new Configurations.LoginWorkflow());
        modelBuilder.ApplyConfiguration(new Configurations.WorkflowGroup());
        modelBuilder.ApplyConfiguration(new Configurations.Workflow());
        modelBuilder.ApplyConfiguration(new Configurations.CrossWorkflow());

        modelBuilder.ApplyConfiguration(new Configurations.BaseShortcut());
        modelBuilder.ApplyConfiguration(new Configurations.Shortcut());
        modelBuilder.ApplyConfiguration(new Configurations.UserShortcut());

        modelBuilder.ApplyConfiguration(new Configurations.AesQueue());
        modelBuilder.ApplyConfiguration(new Configurations.WorkSession());
        modelBuilder.ApplyConfiguration(new Configurations.WebAppProxySetting());

        AddLastChangeTime(modelBuilder);
    }

    private void AddLastChangeTime(ModelBuilder modelBuilder)
    {
        foreach (var e in modelBuilder.Model.GetEntityTypes())
        {
            if (!IsVersionedEntityType(e)) continue;
            modelBuilder.Entity(e.ClrType).Property(nameof(LastChangeTimeEntityBase.LastChangeTime)).HasColumnName("LastChangeTime").IsRequired(false);
        }
    }

    private void SetLastChangeTimes(DateTime changeTime)
    {
        ChangeTracker.Entries()
            .Where(x => x.State == EntityState.Added || x.State == EntityState.Modified)
            .Where(x => IsVersionedEntityType(x.Metadata))
            .ForEach(x => (x.Entity as LastChangeTimeEntityBase).LastChangeTime = changeTime);
    }

    private bool IsVersionedEntityType(IReadOnlyTypeBase entityTypeInfo)
    {
        return LastChangeTimeEntityType.IsAssignableFrom(entityTypeInfo.ClrType);
    }

    private static IEnumerable<Tuple<object, object>> GetRelationships(DbContext context, EntityState relationshipState)
    {
        context.ChangeTracker.DetectChanges();

        var result = new List<Tuple<object, object>>();

        var entries = context.ChangeTracker.Entries().Where(x => x.State == relationshipState).ToArray();
        foreach (var entityEntry in entries)
        {
            var met = entityEntry.Metadata;
            var fks = met.GetForeignKeys().ToArray();

            if (fks.Length != 2)
            {
                continue;
            }

            var props = entityEntry.Properties.ToArray();
            if (props.Length != 2)
            {
                continue;
            }

            var fk1 = fks[0];
            var prEntType1 = fk1.PrincipalEntityType.ClrType;
            var curPrinc1 = context.Find(prEntType1, props[0].CurrentValue);

            var fk2 = fks[1];
            var prEntType2 = fk2.PrincipalEntityType.ClrType;
            var curPrinc2 = context.Find(prEntType2, props[1].CurrentValue);

            result.Add(new Tuple<object, object>(curPrinc1, curPrinc2));
        }

        return result;
    }

    private void PrepareAudit()
    {
        foreach (var changedEntry in ChangeTracker.Entries())
        {
            if (changedEntry.State == EntityState.Detached || changedEntry.State == EntityState.Unchanged)
            {
                continue;
            }

            if (changedEntry.Entity is ConfigurationVersion || changedEntry.Entity is WorkSession || changedEntry.Entity is ServiceAreaAudit)
            {
                continue;
            }

            if (changedEntry.State == EntityState.Deleted)
            {
                WriteAudit(changedEntry.OriginalValues.ToObject(), changedEntry.State);
            }
            else
            {
                WriteAudit(changedEntry.Entity, changedEntry.State);
            }
        }

        var addedRelationships = GetRelationships(this, EntityState.Added);
        foreach (var item in addedRelationships)
        {
            WriteLinkTableAudit(item, AuditableEntityState.Added);
        }

        var deletedRelationships = GetRelationships(this, EntityState.Deleted);
        foreach (var item in deletedRelationships)
        {
            WriteLinkTableAudit(item, AuditableEntityState.Deleted);
        }
    }

    private void WriteAudit(object dataEntity, EntityState state)
    {
        AuditableEntityState auditableEntityState;
        switch (state)
        {
            case EntityState.Added:
                auditableEntityState = AuditableEntityState.Added;
                break;
            case EntityState.Deleted:
                auditableEntityState = AuditableEntityState.Deleted;
                break;
            case EntityState.Modified:
                auditableEntityState = AuditableEntityState.Modified;
                break;
            default:
                return;
        }

        //UserRole
        if (dataEntity is UserRole userRoleEntity)
        {
            _auditor.WriteAudit(userRoleEntity, auditableEntityState);
            return;
        }

        ////ActualOperatorStatus
        if (dataEntity is ActualOperatorStatus actualOperatorStatusEntity)
        {
            _auditor.WriteAudit(actualOperatorStatusEntity, auditableEntityState);
            return;
        }

        ////AdministrationObject
        if (dataEntity is AdministrationObject administrationObjectEntity)
        {
            _auditor.WriteAudit(administrationObjectEntity, auditableEntityState);
            return;
        }

        ////AdministrationPermission
        if (dataEntity is AdministrationPermission administrationPermissionEntity)
        {
            _auditor.WriteAudit(administrationPermissionEntity, auditableEntityState);
            return;
        }

        ////AesQueue
        if (dataEntity is AesQueue aesQueueEntity)
        {
            _auditor.WriteAudit(aesQueueEntity, auditableEntityState);
            return;
        }

        //Application
        if (dataEntity is Application applicationEntity)
        {
            _auditor.WriteAudit(applicationEntity, auditableEntityState);
            return;
        }

        //ControlApplicationInitialization
        if (dataEntity is ControlApplicationInitialization controlApplicationInitializationEntity)
        {
            _auditor.WriteAudit(controlApplicationInitializationEntity, auditableEntityState);
            return;
        }

        //ExternalApplicationInitialization
        if (dataEntity is ExternalApplicationInitialization externalApplicationInitializationEntity)
        {
            _auditor.WriteAudit(externalApplicationInitializationEntity, auditableEntityState);
            return;
        }

        //WebApplicationInitialization
        if (dataEntity is WebApplicationInitialization webApplicationInitializationEntity)
        {
            _auditor.WriteAudit(webApplicationInitializationEntity, auditableEntityState);
            return;
        }

        //ApplicationInitialization
        if (dataEntity is ApplicationInitialization applicationInitializationEntity)
        {
            _auditor.WriteAudit(applicationInitializationEntity, auditableEntityState);
            return;
        }

        //Configuration
        if (dataEntity is Configuration configurationEntity)
        {
            _auditor.WriteAudit(configurationEntity, auditableEntityState);
            return;
        }

        //CrossWorkflow
        if (dataEntity is CrossWorkflow crossWorkflowEntity)
        {
            _auditor.WriteAudit(crossWorkflowEntity, auditableEntityState);
            return;
        }

        //CustomAttribute
        if (dataEntity is CustomAttribute customAttributeEntity)
        {
            _auditor.WriteAudit(customAttributeEntity, auditableEntityState);
            return;
        }

        //Layout
        if (dataEntity is Layout layoutEntity)
        {
            _auditor.WriteAudit(layoutEntity, auditableEntityState);
            return;
        }

        //LoginWorkflow
        if (dataEntity is LoginWorkflow loginWorkflowEntity)
        {
            _auditor.WriteAudit(loginWorkflowEntity, auditableEntityState);
            return;
        }

        //Module
        if (dataEntity is Module moduleEntity)
        {
            _auditor.WriteAudit(moduleEntity, auditableEntityState);
            return;
        }

        //Operator
        if (dataEntity is Operator operatorEntity)
        {
            _auditor.WriteAudit(operatorEntity, auditableEntityState);
            return;
        }

        //OperatorCustomAttribute
        if (dataEntity is OperatorCustomAttribute operatorCustomAttributeEntity)
        {
            _auditor.WriteAudit(operatorCustomAttributeEntity, auditableEntityState);
            return;
        }

        //OperatorGroup
        if (dataEntity is OperatorGroup operatorGroupEntity)
        {
            _auditor.WriteAudit(operatorGroupEntity, auditableEntityState);
            return;
        }

        //OperatorGroupCustomAttribute
        if (dataEntity is OperatorGroupCustomAttribute operatorGroupCustomAttributeEntity)
        {
            _auditor.WriteAudit(operatorGroupCustomAttributeEntity, auditableEntityState);
            return;
        }

        //OperatorStatus
        if (dataEntity is OperatorStatus operatorStatusEntity)
        {
            _auditor.WriteAudit(operatorStatusEntity, auditableEntityState);
            return;
        }

        //Permission
        if (dataEntity is Permission permissionEntity)
        {
            _auditor.WriteAudit(permissionEntity, auditableEntityState);
            return;
        }

        //ServiceArea
        if (dataEntity is ServiceArea serviceAreaEntity)
        {
            _auditor.WriteAudit(serviceAreaEntity, auditableEntityState);
            return;
        }

        //ServiceAreaConfiguration
        if (dataEntity is ServiceAreaConfiguration serviceAreaConfigurationEntity)
        {
            _auditor.WriteAudit(serviceAreaConfigurationEntity, auditableEntityState);
            return;
        }

        //ServiceAreaType
        if (dataEntity is ServiceAreaType serviceAreaTypeEntity)
        {
            _auditor.WriteAudit(serviceAreaTypeEntity, auditableEntityState);
            return;
        }

        //UserGroup
        if (dataEntity is UserGroup userGroupEntity)
        {
            _auditor.WriteAudit(userGroupEntity, auditableEntityState);
            return;
        }

        //Identity
        if (dataEntity is Identity identityEntity)
        {
            _auditor.WriteAudit(identityEntity, auditableEntityState);
            return;
        }

        //Shortcut
        if (dataEntity is Shortcut shortcutEntity)
        {
            _auditor.WriteAudit(shortcutEntity, auditableEntityState);
            return;
        }

        //UserShortcut
        if (dataEntity is UserShortcut userShortcutEntity)
        {
            _auditor.WriteAudit(userShortcutEntity, auditableEntityState);
            return;
        }

        //Workflow
        if (dataEntity is Workflow workflowEntity)
        {
            _auditor.WriteAudit(workflowEntity, auditableEntityState);
            return;
        }

        //WorkflowGroup
        if (dataEntity is WorkflowGroup workflowGroupEntity)
        {
            _auditor.WriteAudit(workflowGroupEntity, auditableEntityState);
            return;
        }

        //Workplace
        if (dataEntity is Workplace workplaceEntity)
        {
            _auditor.WriteAudit(workplaceEntity, auditableEntityState);
        }
    }

    private void WriteLinkTableAudit(Tuple<object, object> map, AuditableEntityState status)
    {
        var aesMapping = GetEntitiesForAudit<AesQueue, UserRole>(map.Item1, map.Item2);
        if (aesMapping != null)
        {
            _auditor.WriteAesQueueMappingAudit(aesMapping.Item1.Id, aesMapping.Item2.Id, status);
            return;
        }

        var applicationMapping = GetEntitiesForAudit<Application, UserRole>(map.Item1, map.Item2);
        if (applicationMapping != null)
        {
            _auditor.WriteApplicationMappingAudit(applicationMapping.Item1.Id, applicationMapping.Item2.Id, status);
            return;
        }

        var layoutMapping = GetEntitiesForAudit<Layout, UserRole>(map.Item1, map.Item2);
        if (layoutMapping != null)
        {
            _auditor.WriteLayoutMappingAudit(layoutMapping.Item1.Id, layoutMapping.Item2.Id, status);
            return;
        }

        var moduleMapping = GetEntitiesForAudit<Module, UserRole>(map.Item1, map.Item2);
        if (moduleMapping != null)
        {
            _auditor.WriteModuleMappingAudit(moduleMapping.Item1.Id, moduleMapping.Item2.Id, status);
            return;
        }

        var operatorStatusMapping = GetEntitiesForAudit<OperatorStatus, UserRole>(map.Item1, map.Item2);
        if (operatorStatusMapping != null)
        {
            _auditor.WriteOperatorStatusMappingAudit(operatorStatusMapping.Item1.Id, operatorStatusMapping.Item2.Id, status);
            return;
        }

        var permissionStatusMapping = GetEntitiesForAudit<Permission, UserRole>(map.Item1, map.Item2);
        if (permissionStatusMapping != null)
        {
            _auditor.WritePermissionMappingAudit(permissionStatusMapping.Item1.Id, permissionStatusMapping.Item2.Id, status);
            return;
        }

        var shortcutStatusMapping = GetEntitiesForAudit<Shortcut, UserRole>(map.Item1, map.Item2);
        if (shortcutStatusMapping != null)
        {
            _auditor.WriteShortcutMappingAudit(shortcutStatusMapping.Item1.Id, shortcutStatusMapping.Item2.Id, status);
            return;
        }

        var workflowMapping = GetEntitiesForAudit<Workflow, UserRole>(map.Item1, map.Item2);
        if (workflowMapping != null)
        {
            _auditor.WriteWorkflowMappingAudit(workflowMapping.Item1.Id, workflowMapping.Item2.Id, status);
            return;
        }

        var userGroupServiceAreaMapping = GetEntitiesForAudit<UserGroup, ServiceArea>(map.Item1, map.Item2);
        if (userGroupServiceAreaMapping != null)
        {
            _auditor.WriteUserGroupServiceAreaMappingAudit(userGroupServiceAreaMapping.Item1.Id, userGroupServiceAreaMapping.Item2.Id, status);
            return;
        }

        var userGroupUserRoleMapping = GetEntitiesForAudit<UserGroup, UserRole>(map.Item1, map.Item2);
        if (userGroupUserRoleMapping != null)
        {
            _auditor.WriteUserGroupUserRoleMappingAudit(userGroupUserRoleMapping.Item1.Id, userGroupUserRoleMapping.Item2.Id, status);
            return;
        }

        var userGroupWorkplaceMapping = GetEntitiesForAudit<UserGroup, Workplace>(map.Item1, map.Item2);
        if (userGroupWorkplaceMapping != null)
        {
            _auditor.WriteUserGroupWorkplaceMappingAudit(userGroupWorkplaceMapping.Item1.Id, userGroupWorkplaceMapping.Item2.Id, status);
            return;
        }

        var userGroupIdentityMapping = GetEntitiesForAudit<UserGroup, Identity>(map.Item1, map.Item2);
        if (userGroupIdentityMapping != null)
        {
            _auditor.WriteUserGroupIdentityMappingAudit(userGroupIdentityMapping.Item1.Id, userGroupIdentityMapping.Item2.Id, status);
            return;
        }

        var operatorOperatorGroupMapping = GetEntitiesForAudit<Operator, OperatorGroup>(map.Item1, map.Item2);
        if (operatorOperatorGroupMapping != null)
        {
            _auditor.WriteOperatorOperatorGroupMappingAudit(operatorOperatorGroupMapping.Item1.Id, operatorOperatorGroupMapping.Item2.Id, status);
        }
    }

    private Tuple<TEntity, TMap> GetEntitiesForAudit<TEntity, TMap>(object entity, object map)
        where TEntity : class
        where TMap : class
    {
        if (map is TMap resultMap && entity is TEntity resultEntity)
        {
            return new Tuple<TEntity, TMap>(resultEntity, resultMap);
        }

        if (entity is TMap reverseResultEntity && map is TEntity reverseResultMap)
        {
            return new Tuple<TEntity, TMap>(reverseResultMap, reverseResultEntity);
        }

        return null;
    }

    protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
    {
        base.OnConfiguring(optionsBuilder);

        optionsBuilder.UseLazyLoadingProxies();
    }

    #region props

    public DbSet<AdministrationObject> AdministrationObjects { get; set; }
    public DbSet<AdministrationPermission> AdministrationPermissions { get; set; }

    public DbSet<UserGroup> UserGroups { get; set; }
    public DbSet<Identity> Identities { get; set; }

    public DbSet<UserRole> UserRoles { get; set; }
    public DbSet<CustomAttribute> CustomAttributes { get; set; }

    public DbSet<Configuration> Configurations { get; set; }

    public DbSet<ConfigurationVersion> ConfigurationVersions { get; set; }

    public DbSet<ServiceAreaType> ServiceAreaTypes { get; set; }

    public DbSet<ServiceArea> ServiceAreas { get; set; }

    public DbSet<ServiceAreaAudit> ServiceAreaAudits { get; set; }

    public DbSet<ServiceAreaConfiguration> ServiceAreaConfigurations { get; set; }

    public DbSet<Operator> Operators { get; set; }

    public DbSet<OperatorCustomAttribute> OperatorsCustomAttributes { get; set; }

    public DbSet<OperatorGroup> OperatorGroups { get; set; }

    public DbSet<OperatorGroupCustomAttribute> OperatorGroupCustomAttributes { get; set; }

    public DbSet<OperatorGroupOperatorLink> OperatorGroupOperatorLinks { get; set; }


    public DbSet<OperatorStatus> OperatorStatuses { get; set; }

    public DbSet<StatusTransition> StatusTransitions { get; set; }

    public DbSet<ActualOperatorStatus> ActualOperatorStatuses { get; set; }

    public DbSet<Module> Modules { get; set; }

    public DbSet<Application> Applications { get; set; }

    public DbSet<ApplicationInitialization> ApplicationInitializations { get; set; }
    public DbSet<ControlApplicationInitialization> ControlApplicationInitialization { get; set; }
    public DbSet<ExternalApplicationInitialization> ExternalApplicationInitialization { get; set; }
    public DbSet<WebApplicationInitialization> WebApplicationInitialization { get; set; }

    public DbSet<LoginWorkflow> LoginWorkflows { get; set; }

    public DbSet<Layout> Layouts { get; set; }

    public DbSet<Permission> Permissions { get; set; }

    public DbSet<WorkflowGroup> WorkflowGroups { get; set; }
    public DbSet<Workflow> Workflows { get; set; }
    public DbSet<CrossWorkflow> CrossWorkflows { get; set; }

    public DbSet<BaseShortcut> BaseShortcuts { get; set; }
    public DbSet<Shortcut> Shortcuts { get; set; }
    public DbSet<UserShortcut> UserShortcuts { get; set; }
    public DbSet<AesQueue> AesQueues { get; set; }
    public DbSet<WorkSession> WorkSessions { get; set; }
    public DbSet<Workplace> Workplaces { get; set; }
    public DbSet<WebAppProxySetting> WebAppProxySettings { get; set; }

    #endregion
}
