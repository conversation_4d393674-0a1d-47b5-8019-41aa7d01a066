namespace Product.AWP.Infrastructure.DAL.Entities;

public class ExternalApplicationInitialization : ApplicationInitialization
{
    public ExternalApplicationInitialization()
    {
        ExternalApplication = new ExternalApplicationInfo();
        UseTopLevelWindow = new UseTopLevelWindowInfo();
    }

    public int? AcquisitionTimeout { get; set; }
    public ExternalApplicationInfo ExternalApplication { get; set; }
    public UseTopLevelWindowInfo UseTopLevelWindow { get; set; }
}
