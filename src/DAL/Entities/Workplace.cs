using Product.AWP.Infrastructure.DAL.Entities.BaseClasses;

namespace Product.AWP.Infrastructure.DAL.Entities;

public class Workplace : HasIdEntityBase
{
    public string Name { get; set; }
    public string Code { get; set; }
    public short UtcOffset { get; set; }
    public Guid? ParentId { get; set; }

    public virtual Workplace Parent { get; set; }
    public virtual ICollection<CustomAttribute> CustomAttributes { get; set; }
    public virtual ICollection<Workplace> Children { get; set; }
    public virtual ICollection<UserGroup> UserGroups { get; set; }
}
