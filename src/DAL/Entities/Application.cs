using Product.AWP.Infrastructure.DAL.Entities.BaseClasses;
using Product.AWP.Infrastructure.DAL.Interfaces;

namespace Product.AWP.Infrastructure.DAL.Entities;

public class Application : HasIdEntityBase, IMappedToUserRole
{
    public string Name { get; set; }
    public int Type { get; set; }
    public int SortOrder { get; set; }
    public bool? EnableAutoSignOn { get; set; }
    public bool? Disabled { get; set; }
    public string CredentialsConfigurationName { get; set; }
    public bool IsService { get; set; }
    public bool ManualStart { get; set; }
    public int LaunchOrder { get; set; }
    public short ClientTypes { get; set; }

    /// <summary>
    /// Изображение для панели приложений.
    /// </summary>
    public byte[] ToolbarImage { get; set; }

    //	navigation props
    public virtual ApplicationInitialization ApplicationInitialization { get; set; }
    public virtual WebClientApplicationInitialization WebClientApplicationInitialization { get; set; }
    public virtual ICollection<LoginWorkflow> LoginWorkflows { get; set; }
    public virtual ICollection<Shortcut> Shortcuts { get; set; }
    public virtual ICollection<UserRole> UserRoles { get; set; }
}
