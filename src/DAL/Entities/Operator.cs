using Product.AWP.Infrastructure.DAL.Entities.BaseClasses;

namespace Product.AWP.Infrastructure.DAL.Entities;

public class Operator : HasIdEntityBase
{
    public string FirstName { get; set; }

    public string MiddleName { get; set; }

    public string LastName { get; set; }

    public string Nickname { get; set; }

    public string UserName { get; set; }

    public Guid ActiveDirectoryId { get; set; }

    //	navigation props
    public virtual ICollection<OperatorGroup> OperatorGroups { get; set; }
    public virtual ICollection<OperatorGroupOperatorLink> OperatorGroupLinks { get; set; }
    public virtual ICollection<OperatorCustomAttribute> CustomAttributes { get; set; }
}
