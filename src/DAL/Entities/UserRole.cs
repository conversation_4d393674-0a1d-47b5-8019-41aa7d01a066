using Product.AWP.Infrastructure.DAL.Entities.BaseClasses;
using Product.AWP.Infrastructure.DAL.Interfaces;

namespace Product.AWP.Infrastructure.DAL.Entities;

public class UserRole : HasIdEntityBase, IMappedToUserGroup
{
    public string Name { get; set; }
    public string Description { get; set; }

    public Guid? ParentId { get; set; }

    //	navigation props

    public virtual UserRole Parent { get; set; }
    public virtual ICollection<UserRole> Children { get; set; }

    public virtual ICollection<CustomAttribute> CustomAttributes { get; set; }

    public virtual ICollection<Application> Applications { get; set; }
    public virtual ICollection<Module> Modules { get; set; }
    public virtual ICollection<Layout> Layouts { get; set; }
    public virtual ICollection<OperatorStatus> OperatorStatuses { get; set; }
    public virtual ICollection<Permission> Permissions { get; set; }
    public virtual ICollection<Workflow> Workflows { get; set; }
    public virtual ICollection<Shortcut> Shortcuts { get; set; }
    public virtual ICollection<AesQueue> AesQueues { get; set; }
    public virtual ICollection<UserGroup> UserGroups { get; set; }
}
