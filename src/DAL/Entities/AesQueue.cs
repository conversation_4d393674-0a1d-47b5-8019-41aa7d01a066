using Product.AWP.Infrastructure.DAL.Entities.BaseClasses;
using Product.AWP.Infrastructure.DAL.Interfaces;

namespace Product.AWP.Infrastructure.DAL.Entities;

public class AesQueue : HasIdEntityBase, IMappedToUserRole
{
    public string Extension { get; set; }
    public string Name { get; set; }
    public string Description { get; set; }
    public bool? Aggregatable { get; set; }

    //	navigation props
    public virtual ICollection<UserRole> UserRoles { get; set; }
}
