using Product.AWP.Infrastructure.DAL.Entities.BaseClasses;

namespace Product.AWP.Infrastructure.DAL.Entities;

public class ServiceAreaConfiguration : HasIdEntityBase
{
    public Guid ServiceAreaId { get; set; }
    public string PropertyKey { get; set; }

    public string Value { get; set; }
    public short? ShortValue { get; set; }
    public int? IntValue { get; set; }
    public double? DoubleValue { get; set; }
    public long? LongValue { get; set; }
    public Guid? GuidValue { get; set; }
    public DateTime? DateTimeValue { get; set; }
    public bool? BoolValue { get; set; }

    public Guid? ConfigurationId { get; set; }
    public Guid? ApplicationId { get; set; }
    public Guid? ModuleId { get; set; }
    public Guid? ShortcutId { get; set; }
    public Guid? AesQueueId { get; set; }

    //	TODO: важно, при добавлении нвых связей - актуализировать этот метод!!!
    public Guid GetEntityId()
    {
        if (ConfigurationId.HasValue)
        {
            return ConfigurationId.Value;
        }

        if (ApplicationId.HasValue)
        {
            return ApplicationId.Value;
        }

        if (ModuleId.HasValue)
        {
            return ModuleId.Value;
        }

        if (ShortcutId.HasValue)
        {
            return ShortcutId.Value;
        }

        if (AesQueueId.HasValue)
        {
            return AesQueueId.Value;
        }

        throw new Exception(string.Format("ServiceAreaConfiguration Id='{0}' is NOT mapped to any entity!", Id));
    }
}
