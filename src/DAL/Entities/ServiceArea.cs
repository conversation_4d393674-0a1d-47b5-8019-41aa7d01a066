using Product.AWP.Infrastructure.DAL.Entities.BaseClasses;
using Product.AWP.Infrastructure.DAL.Interfaces;

namespace Product.AWP.Infrastructure.DAL.Entities;

public class ServiceArea : HasIdEntityBase, IMappedToUserGroup
{
    public string Name { get; set; }
    public Guid? ServiceAreaTypeId { get; set; }
    public Guid? ParentId { get; set; }
    public int UtcOffset { get; set; }
    public string Code { get; set; }


    //	navigation props

    public virtual ICollection<CustomAttribute> CustomAttributes { get; set; }

    public virtual ServiceAreaType ServiceAreaType { get; set; }
    public virtual ServiceArea Parent { get; set; }
    public virtual ICollection<ServiceArea> Children { get; set; }

    public virtual ICollection<UserGroup> UserGroups { get; set; }
}
