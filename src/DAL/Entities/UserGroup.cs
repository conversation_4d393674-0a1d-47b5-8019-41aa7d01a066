using Product.AWP.Infrastructure.DAL.Entities.BaseClasses;

namespace Product.AWP.Infrastructure.DAL.Entities;

public class UserGroup : HasIdEntityBase
{
    public string Name { get; set; }
    public string Description { get; set; }

    //	Foreign Keys
    public virtual ICollection<Identity> Identities { get; set; }
    public virtual ICollection<AdministrationPermission> AdministrationPermissions { get; set; }
    public virtual ICollection<UserRole> UserRoles { get; set; }
    public virtual ICollection<ServiceArea> ServiceAreas { get; set; }
    public virtual ICollection<Workplace> Workplaces { get; set; }
}
