using Product.AWP.Infrastructure.DAL.Entities.BaseClasses;
using Product.AWP.Infrastructure.DAL.Interfaces;

namespace Product.AWP.Infrastructure.DAL.Entities;

public class Workflow : HasIdEntityBase, IMappedToUserRole
{
    public Workflow()
    {
        Event = new TypeInfo();
        SummaryControl = new TypeInfo();
        Configuration = new TypeInfo();
    }

    public string SerializedValue { get; set; }

    public int EventThreadOption { get; set; }

    public bool InvokeAsync { get; set; }

    public TypeInfo Event { get; set; }

    public string Name { get; set; }

    public short ClientTypes { get; set; }

    public int Order { get; set; }

    public string Group { get; set; }

    public string Description { get; set; }

    public bool IsBusinessScenario { get; set; }

    public bool IsService { get; set; }

    public string Code { get; set; }

    public byte[] Icon { get; set; }

    public Guid? WorkflowGroupId { get; set; }

    public TypeInfo SummaryControl { get; set; }

    public TypeInfo Configuration { get; set; }

    public string ConfigurationValue { get; set; }

    public bool UseConfiguration { get; set; }

    //	navigation props
    public virtual ICollection<CrossWorkflow> CrossWorkflows { get; set; }

    public virtual ICollection<CrossWorkflow> CrossWorkflowsToHere { get; set; }

    public virtual WorkflowGroup WorkflowGroup { get; set; }

    public virtual ICollection<UserRole> UserRoles { get; set; }
}
