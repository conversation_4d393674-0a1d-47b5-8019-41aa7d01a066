using Product.AWP.Infrastructure.DAL.Entities.BaseClasses;

namespace Product.AWP.Infrastructure.DAL.Entities;

public class WorkSession : HasIdEntityBase
{
    public Guid OperatorId { get; set; }
    public string ClientType { get; set; }
    public string Description { get; set; }
    public Guid ServiceAreaId { get; set; }
    public Guid UserRoleId { get; set; }
    public Guid WorkplaceId { get; set; }
    public DateTimeOffset StartDate { get; set; }
    public DateTimeOffset? EndDate { get; set; }
    public DateTime? ValidUntil { get; set; }
}
