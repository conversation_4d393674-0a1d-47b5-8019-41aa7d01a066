using Product.AWP.Infrastructure.DAL.Entities.BaseClasses;

namespace Product.AWP.Infrastructure.DAL.Entities;

public class StatusTransition : HasReadOnlyIdEntityBase
{
    public Guid OperatorStatusFromId { get; set; }


    public Guid OperatorStatusToId { get; set; }


    public bool IsDefault { get; set; }


    //	navigation props

    public virtual OperatorStatus OperatorStatusFrom { get; set; }

    public virtual OperatorStatus OperatorStatusTo { get; set; }
}
