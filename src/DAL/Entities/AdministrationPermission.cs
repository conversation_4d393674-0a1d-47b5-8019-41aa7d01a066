using Product.AWP.Infrastructure.DAL.Entities.BaseClasses;

namespace Product.AWP.Infrastructure.DAL.Entities;

public class AdministrationPermission : HasIdEntityBase
{
    public byte PermissionId { get; set; }
    public Guid AdministrationObjectId { get; set; }
    public Guid UserGroupId { get; set; }

    //	navigation props
    public virtual AdministrationObject AdministrationObject { get; set; }
    public virtual UserGroup UserGroup { get; set; }
}
