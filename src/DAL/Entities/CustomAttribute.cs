using Product.AWP.Infrastructure.DAL.Entities.BaseClasses;

namespace Product.AWP.Infrastructure.DAL.Entities;

public class CustomAttribute : HasIdEntityBase
{
    public string Name { get; set; }

    public string Value { get; set; }

    public byte[] Image { get; set; }

    // Foreign keys
    public Guid? ServiceAreaId { get; set; }
    public Guid? UserRoleId { get; set; }
    public Guid? OperatorStatusId { get; set; }
    public Guid? WorkplaceId { get; set; }

    //	navigation properties
    public virtual ServiceArea ServiceArea { get; set; }
    public virtual UserRole UserRole { get; set; }
    public virtual OperatorStatus OperatorStatus { get; set; }
    public virtual Workplace Workplace { get; set; }
}
