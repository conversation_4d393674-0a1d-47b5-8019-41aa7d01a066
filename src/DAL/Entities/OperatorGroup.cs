using Product.AWP.Infrastructure.DAL.Entities.BaseClasses;

namespace Product.AWP.Infrastructure.DAL.Entities;

public class OperatorGroup : HasIdEntityBase
{
    public string Name { get; set; }
    public Guid? ParentId { get; set; }

    //	navigation props
    public virtual ICollection<OperatorGroupCustomAttribute> CustomAttributes { get; set; }
    public virtual ICollection<Operator> Operators { get; set; }
    public virtual ICollection<OperatorGroupOperatorLink> OperatorLinks { get; set; }
    public virtual OperatorGroup Parent { get; set; }
    public virtual ICollection<OperatorGroup> Children { get; set; }
}
