using Product.AWP.Infrastructure.DAL.Entities.BaseClasses;

namespace Product.AWP.Infrastructure.DAL.Entities;

public class ApplicationInitialization : HasIdEntityBase
{
    public ApplicationInitialization()
    {
        Adapter = new TypeInfo();
    }

    public string DisplayGroup { get; set; }
    public string AdapterInitialization { get; set; }
    public TypeInfo Adapter { get; set; }
    public bool StartAsync { get; set; }
    public ExpanderSettings ExpanderSettings { get; set; }
    public SizeSettings SizeSettings { get; set; }
    public bool HideInTaskbar { get; set; }
    public bool HideOnStartup { get; set; }

    //	navigation props
    public virtual Application Application { get; set; }
}

public class WebClientApplicationInitialization : HasIdEntityBase
{
    public string DisplayGroup { get; set; }
    public string ComponentInit { get; set; }
    public string ComponentName { get; set; }

    //	navigation props
    public virtual Application Application { get; set; }
}
