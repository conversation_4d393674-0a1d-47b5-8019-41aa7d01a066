using Product.AWP.Infrastructure.DAL.Entities.BaseClasses;

namespace Product.AWP.Infrastructure.DAL.Entities;

public class ActualOperatorStatus : HasIdEntityBase
{
    public Guid OperatorId { get; set; }
    public Guid ActualStatusId { get; set; }
    public string ActualStatusSetCode { get; set; }
    public Guid? PreviousStatusId { get; set; }
    public Guid? ActualPendingStatusId { get; set; }
    public Guid? PreviousPendingStatusId { get; set; }
    public DateTimeOffset DateFrom { get; set; }
    public DateTimeOffset? ValidUntil { get; set; }
    public Guid? WorkSessionId { get; set; }

    //	navigation props
    public virtual WorkSession WorkSession { get; set; }
    public virtual Operator Operator { get; set; }
    public virtual ServiceArea ServiceArea { get; set; }
    public virtual OperatorStatus CurrentOperatorStatus { get; set; }
    public virtual OperatorStatus PreviousOperatorStatus { get; set; }
}
