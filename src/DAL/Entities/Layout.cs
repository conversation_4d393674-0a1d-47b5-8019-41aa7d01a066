using Product.AWP.Infrastructure.DAL.Entities.BaseClasses;
using Product.AWP.Infrastructure.DAL.Interfaces;

namespace Product.AWP.Infrastructure.DAL.Entities;

public class Layout : HasIdEntityBase, IMappedToUserRole
{
    public string Value { get; set; }
    public string Name { get; set; }
    public int Order { get; set; }
    public string Description { get; set; }

    //	navigation props
    public virtual ICollection<UserRole> UserRoles { get; set; }
}
