namespace Product.AWP.Infrastructure.DAL.Entities.Audit;

public class OperatorGroupCustomAttribute : AuditEntityBase
{
    public Guid OperatorGroupId { get; set; }

    public string Code { get; set; }

    public string StringValue { get; set; }

    public Guid? GuidValue { get; set; }

    public long? LongValue { get; set; }

    public decimal? DecimalValue { get; set; }

    public bool? BoolValue { get; set; }

    public DateTime? DateTimeValue { get; set; }

    public byte[] BinaryValue { get; set; }
}
