namespace Product.AWP.Infrastructure.DAL.Entities.Audit;

public class Application : AuditEntityWithIdBase
{
    public string Name { get; set; }
    public int Type { get; set; }
    public int SortOrder { get; set; }
    public bool? EnableAutoSignOn { get; set; }
    public bool? Disabled { get; set; }
    public string CredentialsConfigurationName { get; set; }
    public bool IsService { get; set; }
    public bool ManualStart { get; set; }
    public int LaunchOrder { get; set; }
    public byte[] ToolbarImage { get; set; }
}
