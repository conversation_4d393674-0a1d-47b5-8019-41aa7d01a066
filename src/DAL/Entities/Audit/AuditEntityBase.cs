using Product.AWP.Infrastructure.DAL.Interfaces.Audit;

namespace Product.AWP.Infrastructure.DAL.Entities.Audit;

public abstract class AuditEntityBase : IAuditEntity
{
    protected AuditEntityBase()
    {
        Uid = Guid.NewGuid();
    }

    ////	Foreign Keys
    public Audit InsertAudit { get; set; }
    public Audit UpdateAudit { get; set; }
    public Audit DeleteAudit { get; set; }
    public Guid Uid { get; set; }

    public Guid? InsertAuditId { get; set; }
    public Guid? UpdateAuditId { get; set; }
    public Guid? DeleteAuditId { get; set; }
}
