namespace Product.AWP.Infrastructure.DAL.Entities.Audit;

public class CustomAttribute : AuditEntityWithIdBase
{
    public string Name { get; set; }
    public string Value { get; set; }
    public byte[] Image { get; set; }

    // Foreign keys
    public Guid? ServiceAreaId { get; set; }
    public Guid? UserRoleId { get; set; }
    public Guid? OperatorStatusId { get; set; }
    public Guid? WorkplaceId { get; set; }
}
