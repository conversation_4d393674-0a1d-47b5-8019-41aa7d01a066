namespace Product.AWP.Infrastructure.DAL.Entities.Audit;

public class ApplicationInitialization : AuditEntityWithIdBase
{
    public ApplicationInitialization()
    {
        Adapter = new TypeInfo();
    }

    public string DisplayGroup { get; set; }
    public string AdapterInitialization { get; set; }
    public TypeInfo Adapter { get; set; }
    public bool StartAsync { get; set; }

    public bool WrapInExpander { get; set; }
    public bool InitiallyExpanded { get; set; }
    public string Header { get; set; }
    public int ExpandDirection { get; set; }
    public int ExpandDisplayMode { get; set; }

    public double? Width { get; set; }
    public double? Height { get; set; }
    public double? MinWidth { get; set; }
    public double? MinHeight { get; set; }
    public double? MaxWidth { get; set; }
    public double? MaxHeight { get; set; }

    public bool HideInTaskbar { get; set; }
    public bool HideOnStartup { get; set; }
}
