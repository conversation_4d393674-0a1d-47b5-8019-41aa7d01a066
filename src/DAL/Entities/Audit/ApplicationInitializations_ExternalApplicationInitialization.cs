namespace Product.AWP.Infrastructure.DAL.Entities.Audit;

public class ExternalApplicationInitialization : ApplicationInitialization
{
    public int? AcquisitionTimeout { get; set; }

    public string Path { get; set; }
    public string Arguments { get; set; }
    public string WorkingDirectory { get; set; }

    public string Caption { get; set; }
    public string ClassName { get; set; }
    public bool LimitToProcess { get; set; }
    public bool UseProcessMainWindow { get; set; }
    public bool SearchWindowAfterLogin { get; set; }
}
