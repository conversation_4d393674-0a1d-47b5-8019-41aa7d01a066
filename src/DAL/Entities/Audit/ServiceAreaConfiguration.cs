namespace Product.AWP.Infrastructure.DAL.Entities.Audit;

public class ServiceAreaConfiguration : AuditEntityWithIdBase
{
    public Guid ServiceAreaId { get; set; }
    public string PropertyKey { get; set; }

    public string Value { get; set; }
    public short? ShortValue { get; set; }
    public int? IntValue { get; set; }
    public double? DoubleValue { get; set; }
    public long? LongValue { get; set; }
    public Guid? GuidValue { get; set; }
    public DateTime? DateTimeValue { get; set; }
    public bool? BoolValue { get; set; }

    public Guid? ConfigurationId { get; set; }
    public Guid? ApplicationId { get; set; }
    public Guid? ModuleId { get; set; }
    public Guid? ShortcutId { get; set; }
    public Guid? AesQueueId { get; set; }
}
