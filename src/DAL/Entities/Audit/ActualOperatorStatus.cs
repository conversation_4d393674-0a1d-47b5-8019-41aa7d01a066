namespace Product.AWP.Infrastructure.DAL.Entities.Audit;

public class ActualOperatorStatus : AuditEntityWithIdBase
{
    public Guid OperatorId { get; set; }
    public Guid ActualStatusId { get; set; }
    public string ActualStatusSetCode { get; set; }
    public Guid? PreviousStatusId { get; set; }
    public Guid? ActualPendingStatusId { get; set; }
    public Guid? PreviousPendingStatusId { get; set; }
    public DateTimeOffset DateFrom { get; set; }
    public DateTimeOffset? ValidUntil { get; set; }
    public Guid? WorkSessionId { get; set; }
}
