using Product.AWP.Infrastructure.DAL.Entities.BaseClasses;

namespace Product.AWP.Infrastructure.DAL.Entities;

public class WorkflowGroup : HasReadOnlyIdEntityBase
{
    public string Name { get; set; }

    public string Description { get; set; }

    public byte[] Image { get; set; }

    public int? Order { get; set; }

    //	navigation props
    public virtual ICollection<Workflow> Workflows { get; set; }
}
