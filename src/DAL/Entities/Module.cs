using Product.AWP.Infrastructure.DAL.Entities.BaseClasses;
using Product.AWP.Infrastructure.DAL.Interfaces;

namespace Product.AWP.Infrastructure.DAL.Entities;

public class Module : HasIdEntityBase, IMappedToUserRole
{
    public string Name { get; set; }

    public string Initialization { get; set; }

    public string Description { get; set; }

    public bool IsOptional { get; set; }

    public bool IsDisabled { get; set; }

    public short ClientTypes { get; set; }

    //	navigation props
    public virtual ICollection<UserRole> UserRoles { get; set; }
}
