using Product.AWP.Infrastructure.DAL.Entities.BaseClasses;

namespace Product.AWP.Infrastructure.DAL.Entities;

public class WebAppProxySetting : HasIdEntityBase
{
    public Guid? WebApplicationId { get; set; }
    public string OriginalUrl { get; set; }
    public int ProxyPort { get; set; }
    public string ScriptToInject { get; set; }
    public string ProxyAdapterCode { get; set; }

    public virtual WebApplicationInitialization WebApplicationInitialization { get; set; }
}
