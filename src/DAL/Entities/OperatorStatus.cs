using Product.AWP.Infrastructure.DAL.Entities.BaseClasses;
using Product.AWP.Infrastructure.DAL.Interfaces;

namespace Product.AWP.Infrastructure.DAL.Entities;

public class OperatorStatus : HasIdEntityBase, IMappedToUserRole
{
    public string Name { get; set; }

    public string Code { get; set; }

    public bool IsStarting { get; set; }

    public bool IsTerminating { get; set; }

    public int ValidityDuration { get; set; }

    //	navigation props
    public virtual ICollection<StatusTransition> StatusTransitionsFromHere { get; set; }
    public virtual ICollection<StatusTransition> StatusTransitionsToHere { get; set; }
    public virtual ICollection<CustomAttribute> CustomAttributes { get; set; }
    public virtual ICollection<UserRole> UserRoles { get; set; }
}
