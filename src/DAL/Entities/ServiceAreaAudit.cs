using Product.AWP.Infrastructure.DAL.Entities.BaseClasses;

namespace Product.AWP.Infrastructure.DAL.Entities;

public class ServiceAreaAudit : HasReadOnlyIdEntityBase
{
    public Guid OperatorId { get; set; }

    public Guid? PreviousServiceAreaId { get; set; }
    public Guid? CurrentServiceAreaId { get; set; }

    public DateTimeOffset ChangeTime { get; set; }
    public string ChangeCode { get; set; }
}
