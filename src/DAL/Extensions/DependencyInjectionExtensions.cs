using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Product.AWP.Infrastructure.DAL.Interfaces;
using Product.AWP.Infrastructure.DAL.Interfaces.Audit;
using Product.AWP.Infrastructure.DAL.Interfaces.Repositories;
using Product.AWP.Infrastructure.DAL.Repositories;

namespace Product.AWP.Infrastructure.DAL.Extensions;

/// <summary>
/// Добавляет инфраструктурные зависимости в DI контейнер 
/// </summary>
public static class DependencyInjectionExtensions
{
    /// <summary>
    /// Добавить поддержку работы с базой данных 
    /// </summary>
    /// <param name="services">Коллекция сервисов <see cref="IServiceCollection"/></param>
    /// <param name="configuration">Конфигурация приложения</param>
    /// <returns>Коллекция сервисов <see cref="IServiceCollection"/></returns>
    public static IServiceCollection AddDatabase(this IServiceCollection services, IConfiguration configuration)
    {
        services.AddScoped<IAuditor, AuditDataProxy>();
        services.AddScoped<IInfrastructureDataModel, InfrastructureDataModel>();

        // Все настройки происходят в методе OnConfiguring каждой конкретной реализации контекста
        services.AddDbContext<DbContext, InfrastructureDataModel>();
        services.AddDbContext<DbContext, AuditPostgreSqlDbContext>();

        services.AddRepositories();

        return services;
    }

    /// <summary>
    /// Добавить реализации репозиториев
    /// </summary>
    /// <param name="services">Коллекция сервисов <see cref="IServiceCollection"/></param>
    /// <returns>Коллекция сервисов <see cref="IServiceCollection"/></returns>
    private static IServiceCollection AddRepositories(this IServiceCollection services)
    {
        services.AddScoped<IPermissionRepository, PermissionRepository>();
        services.AddScoped<IOperatorGroupRepository, OperatorGroupRepository>();
        services.AddScoped<IOperatorDataRepository, OperatorDataRepository>();
        services.AddScoped<IUserGroupRepository, UserGroupRepositor>();
        services.AddScoped<IProfileRepository, ProfileRepository>();
        services.AddScoped<IConfigurationRepository, ConfigurationRepository>();
        services.AddScoped<IOperatorStatusAvailabilityRepository, OperatorStatusAvailabilityRepository>();
        services.AddScoped<IOperatorStatusRepository, OperatorStatusRepository>();
        services.AddScoped<IShortcutsRepository, ShortcutsRepository>();
        services.AddScoped<IConfigurationVersionRepository, ConfigurationVersionRepository>();
        services.AddScoped<IActualOperatorStatusRepository, ActualOperatorStatusRepository>();
        return services;
    }
}
