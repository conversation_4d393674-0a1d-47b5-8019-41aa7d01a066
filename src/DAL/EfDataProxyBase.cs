using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Platform.Common;
using Platform.Logging.MicrosoftExtensions;

namespace Product.AWP.Infrastructure.DAL;

public abstract class EfDataProxyBase<TContext> : IHasAdditionalLogInfo, IDisposable
    where TContext : class
{
    private TContext _dataContext;

    protected EfDataProxyBase(IConfiguration configuration, ILoggerFactory loggerFactory, Platform.Logging.LoggingCategory loggingCategory)
    {
        Configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
        DbContextDefaultSchema = configuration.GetValue<string>("DbSchemas:awpInfraAud") ?? throw new ArgumentNullException(nameof(DbContextDefaultSchema));
        Logger = loggerFactory?.CreateLogger(GetType());
        AdditionalLogInfo = new AdditionalLogInfo(loggingCategory);
    }

    private TContext GetDataContext()
    {
        if (_dataContext != null)
        {
            return _dataContext;
        }

        _dataContext = CreateDataContext();
        return _dataContext;
    }

    protected abstract TContext CreateDataContext();

    #region props

    public string DbContextDefaultSchema { get; }
    protected IConfiguration Configuration { get; }

    protected ILogger? Logger { get; }

    protected TContext DataContext => GetDataContext();

    public AdditionalLogInfo AdditionalLogInfo { get; protected set; }

    #endregion

    #region IDisposable implementation

    ~EfDataProxyBase()
    {
        Dispose(false);
        GC.SuppressFinalize(this);
    }

    public void Dispose()
    {
        try
        {
            Dispose(true);
        }
        catch (Exception exc)
        {
            using var mlh = this.CreateMethodLogHelperNoImplicitLogging(Logger).WithNoArgs();
            mlh.LogMethodError(exc, "Dispose FAILED!!!");
        }
    }

    protected virtual void Dispose(bool disposing)
    {
        if (!disposing)
        {
            return;
        }

        _dataContext?.DisposeIfDisposable();
    }

    #endregion
}
