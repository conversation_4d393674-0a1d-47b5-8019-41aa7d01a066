using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Design;
using Microsoft.Extensions.Configuration;

#pragma warning disable 1591

namespace Product.AWP.Infrastructure.DAL;

public class InfrastructureDataModel : InfrastructureDataModelBase
{
    static InfrastructureDataModel()
    {
        AppContext.SetSwitch("Npgsql.EnableLegacyTimestampBehavior", true);
    }

    public InfrastructureDataModel()
        : base("Z")
    {
    }

    public InfrastructureDataModel(IConfiguration configuration)
        : base(configuration, configuration.GetValue<string>("DbSchemas:awpInfra") ?? throw new ArgumentNullException(nameof(configuration)))
    {
    }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);
        TuneProperties(modelBuilder);
    }

    private void TuneProperties(ModelBuilder modelBuilder)
    {
        //	была мысль все строковые пропертя в БД хранить в типе text, но не стал - пусть со стороны БД будет ограничение по длине там, где задано в модели тоже. Чтобы одинаково было. По производительности нет разницы, согласно документации Postgre
        //	https://www.postgresql.org/docs/current/datatype-character.html
        //	https://stackoverflow.com/questions/4848964/postgresql-difference-between-text-and-varchar-character-varying
        //modelBuilder.Properties<string>().Configure(x => x.HasColumnType("text"));

        foreach (var e in modelBuilder.Model.GetEntityTypes())
        {
            if (e == null) continue;

            foreach (var mutableProperty in e.GetProperties())
            {
                if (mutableProperty == null || mutableProperty.PropertyInfo == null) continue;
                if (mutableProperty.PropertyInfo.PropertyType == typeof(DateTimeOffset) || mutableProperty.PropertyInfo.PropertyType == typeof(DateTimeOffset?))
                {
                    //	в постгре для типа "timestamp with time zone" максимальный пресижен 6
                    mutableProperty.SetPrecision(6);
                }
            }
        }
    }

    protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
    {
        base.OnConfiguring(optionsBuilder);

        if (!optionsBuilder.IsConfigured)
        {
            var connection = GetConnectionString(DefaultConnectionStringName);
            optionsBuilder.UseNpgsql(connection, o =>
            {
                o.MigrationsAssembly(typeof(InfrastructureDataModel).Assembly.GetName().Name);
                o.MigrationsHistoryTable($"__{nameof(InfrastructureDataModel)}", DefaultDefaultSchema);

                //o.UseQuerySplittingBehavior(QuerySplittingBehavior.SplitQuery); // TODO: Включить после тестирования
            });
        }
    }
}

public class InfrastructureDataModelFactory : IDesignTimeDbContextFactory<InfrastructureDataModel>
{
    public InfrastructureDataModel CreateDbContext(string[] args)
    {
        //var optionsBuilder = new DbContextOptionsBuilder<InfrastructureDataModel>();
        //optionsBuilder.UseSqlite("Data Source=blog.db");

        //return new BloggingContext(optionsBuilder.Options);

        return new InfrastructureDataModel();
    }
}
