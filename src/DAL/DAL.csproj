<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
        <AssemblyName>Product.AWP.Infrastructure.DAL</AssemblyName>
        <RootNamespace>Product.AWP.Infrastructure.DAL</RootNamespace>
        <LangVersion>default</LangVersion>
        <Product>Product</Product>
        <GenerateDocumentationFile>True</GenerateDocumentationFile>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Microsoft.EntityFrameworkCore.Proxies"/>
        <PackageReference Include="Microsoft.EntityFrameworkCore.Tools">
            <PrivateAssets>all</PrivateAssets>
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
        <PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL"/>
        <PackageReference Include="Platform.Common"/>
        <PackageReference Include="Platform.Logging.MicrosoftExtensions"/>
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\Domain\Domain.csproj"/>
    </ItemGroup>

</Project>
