using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Product.AWP.Infrastructure.DAL.Entities.Audit;
using Product.AWP.Infrastructure.DAL.Interfaces.Audit;

namespace Product.AWP.Infrastructure.DAL;

public abstract class AuditDataProxyBase : EfDataProxyBase<IAuditDbContext>, IAuditor
{
    private Audit? _audit;
    private bool _auditAdded;

    protected AuditDataProxyBase(IConfiguration configuration, ILoggerFactory loggerFactory)
        : base(configuration, loggerFactory, new Platform.Logging.LoggingCategory("AWP.Audit.DataAccess"))
    {
    }

    public void InitAudit(string dns, string login, string operationName)
    {
        _audit = new Audit
        {
            Id = Guid.NewGuid(),
            ChangeDate = DateTime.UtcNow,
            Dns = dns,
            Login = login,
            OperationName = operationName
        };
    }

    public async Task SaveChangesAsync()
    {
        await DataContext.SaveChangesAsync();
    }

    public Audit GetConfigAuditToUseOnSave()
    {
        return _audit ?? throw new InvalidOperationException("InitAudit was not called!");
    }

    //protected override IAuditDbContext CreateDataContext()
    //{
    //	var result = new AuditDbContext(DbContextDefaultSchema);
    //	result.Configuration.AutoDetectChangesEnabled = false;
    //	result.Configuration.ValidateOnSaveEnabled = false;
    //	return result;
    //}

    private TEntity SetAuditId<TEntity>(TEntity entity, AuditableEntityState state)
        where TEntity : IAuditEntity
    {
        var audit = GetConfigAuditToUseOnSave();
        switch (state)
        {
            case AuditableEntityState.Added:
                entity.InsertAuditId = audit.Id;
                break;
            case AuditableEntityState.Modified:
                entity.UpdateAuditId = audit.Id;
                break;
            case AuditableEntityState.Deleted:
                entity.DeleteAuditId = audit.Id;
                break;
            default:
                throw new ArgumentOutOfRangeException();
        }

        if (_auditAdded) return entity;

        DataContext.Audits.Add(audit);
        _auditAdded = true;
        return entity;
    }

    private void MapApplicationInitialization(Product.AWP.Infrastructure.DAL.Entities.ApplicationInitialization entity, ApplicationInitialization auditableEntity)
    {
        auditableEntity.Id = entity.Id;
        auditableEntity.DisplayGroup = entity.DisplayGroup;
        auditableEntity.AdapterInitialization = entity.AdapterInitialization;
        auditableEntity.Adapter = new TypeInfo {Assembly = entity.Adapter?.Assembly, TypeName = entity.Adapter?.TypeName};
        auditableEntity.StartAsync = entity.StartAsync;
        auditableEntity.HideInTaskbar = entity.HideInTaskbar;
        auditableEntity.HideOnStartup = entity.HideOnStartup;

        if (entity.ExpanderSettings != null)
        {
            auditableEntity.WrapInExpander = entity.ExpanderSettings.WrapInExpander;
            auditableEntity.InitiallyExpanded = entity.ExpanderSettings.InitiallyExpanded;
            auditableEntity.Header = entity.ExpanderSettings.Header;
            auditableEntity.ExpandDirection = entity.ExpanderSettings.ExpandDirection;
            auditableEntity.ExpandDisplayMode = entity.ExpanderSettings.ExpandDisplayMode;
        }

        if (entity.SizeSettings != null)
        {
            auditableEntity.Width = entity.SizeSettings.Width;
            auditableEntity.Height = entity.SizeSettings.Height;
            auditableEntity.MinWidth = entity.SizeSettings.MinWidth;
            auditableEntity.MinHeight = entity.SizeSettings.MinHeight;
            auditableEntity.MaxHeight = entity.SizeSettings.MaxHeight;
            auditableEntity.MaxWidth = entity.SizeSettings.MaxWidth;
        }
    }

    private void AddAuditEntity<TEntity>(TEntity entity, AuditableEntityState state, DbSet<TEntity> dbSet)
        where TEntity : class, IAuditEntity
    {
        entity = SetAuditId(entity, state);
        dbSet.Add(entity);
    }

    #region Write Audit

    public void WriteAudit(Product.AWP.Infrastructure.DAL.Entities.UserRole userRole, AuditableEntityState state)
    {
        var result = new UserRole
        {
            Id = userRole.Id,
            Name = userRole.Name,
            Description = userRole.Description,
            ParentId = userRole.ParentId
        };

        AddAuditEntity(result, state, DataContext.UserRoles);
    }

    public void WriteAudit(Product.AWP.Infrastructure.DAL.Entities.ActualOperatorStatus entity, AuditableEntityState state)
    {
        var result = new ActualOperatorStatus
        {
            Id = entity.Id,
            OperatorId = entity.OperatorId,
            ActualStatusId = entity.ActualStatusId,
            ActualStatusSetCode = entity.ActualStatusSetCode,
            PreviousStatusId = entity.PreviousStatusId,
            ActualPendingStatusId = entity.ActualPendingStatusId,
            PreviousPendingStatusId = entity.PreviousPendingStatusId,
            DateFrom = entity.DateFrom,
            ValidUntil = entity.ValidUntil,
            WorkSessionId = entity.WorkSessionId
        };

        AddAuditEntity(result, state, DataContext.ActualOperatorStatuses);
    }

    public void WriteAudit(Product.AWP.Infrastructure.DAL.Entities.AdministrationObject entity, AuditableEntityState state)
    {
        var result = new AdministrationObject
        {
            Id = entity.Id,
            Name = entity.Name,
            ModuleName = entity.ModuleName
        };

        AddAuditEntity(result, state, DataContext.AdministrationObjects);
    }

    public void WriteAudit(Product.AWP.Infrastructure.DAL.Entities.AdministrationPermission entity, AuditableEntityState state)
    {
        var result = new AdministrationPermission
        {
            Id = entity.Id,
            PermissionId = entity.PermissionId,
            AdministrationObjectId = entity.AdministrationObjectId,
            UserGroupId = entity.UserGroupId
        };

        AddAuditEntity(result, state, DataContext.AdministrationPermissions);
    }

    public void WriteAudit(Product.AWP.Infrastructure.DAL.Entities.AesQueue entity, AuditableEntityState state)
    {
        var result = new AesQueue
        {
            Id = entity.Id,
            Extension = entity.Extension,
            Name = entity.Name,
            Description = entity.Description,
            Aggregatable = entity.Aggregatable
        };

        AddAuditEntity(result, state, DataContext.AesQueues);
    }

    public void WriteAesQueueMappingAudit(Guid aesQueueId, Guid userRoleId, AuditableEntityState state)
    {
        var result = new AesQueueUserRole
        {
            AesQueueId = aesQueueId,
            UserRoleId = userRoleId
        };

        AddAuditEntity(result, state, DataContext.AesQueueUserRoles);
    }

    public void WriteAudit(Product.AWP.Infrastructure.DAL.Entities.Application entity, AuditableEntityState state)
    {
        var result = new Application
        {
            Id = entity.Id,
            Type = entity.Type,
            Name = entity.Name,
            SortOrder = entity.SortOrder,
            EnableAutoSignOn = entity.EnableAutoSignOn,
            Disabled = entity.Disabled,
            CredentialsConfigurationName = entity.CredentialsConfigurationName,
            IsService = entity.IsService,
            ManualStart = entity.ManualStart,
            LaunchOrder = entity.LaunchOrder,
            ToolbarImage = entity.ToolbarImage
        };

        AddAuditEntity(result, state, DataContext.Applications);
    }

    public void WriteApplicationMappingAudit(Guid applicationId, Guid userRoleId, AuditableEntityState state)
    {
        var result = new ApplicationUserRole
        {
            ApplicationId = applicationId,
            UserRoleId = userRoleId
        };

        AddAuditEntity(result, state, DataContext.ApplicationUserRoles);
    }

    public void WriteAudit(Product.AWP.Infrastructure.DAL.Entities.ApplicationInitialization entity, AuditableEntityState state)
    {
        var result = new ApplicationInitialization();
        MapApplicationInitialization(entity, result);

        AddAuditEntity(result, state, DataContext.ApplicationInitializations);
    }

    public void WriteAudit(Product.AWP.Infrastructure.DAL.Entities.ControlApplicationInitialization entity, AuditableEntityState state)
    {
        var result = new ControlApplicationInitialization
        {
            Control = new TypeInfo {Assembly = entity.Control.Assembly, TypeName = entity.Control.TypeName},
            ControlInitialization = entity.ControlInitialization,
            ControlPerformsLogin = entity.ControlPerformsLogin
        };
        MapApplicationInitialization(entity, result);

        AddAuditEntity(result, state, DataContext.ControlApplicationInitializations);
    }

    public void WriteAudit(Product.AWP.Infrastructure.DAL.Entities.ExternalApplicationInitialization entity, AuditableEntityState state)
    {
        var result = new ExternalApplicationInitialization
        {
            AcquisitionTimeout = entity.AcquisitionTimeout,
            Path = entity.ExternalApplication.Path,
            Arguments = entity.ExternalApplication.Arguments,
            WorkingDirectory = entity.ExternalApplication.WorkingDirectory,
            Caption = entity.UseTopLevelWindow.Caption,
            ClassName = entity.UseTopLevelWindow.ClassName,
            LimitToProcess = entity.UseTopLevelWindow.LimitToProcess,
            UseProcessMainWindow = entity.UseTopLevelWindow.UseProcessMainWindow,
            SearchWindowAfterLogin = entity.UseTopLevelWindow.SearchWindowAfterLogin
        };
        MapApplicationInitialization(entity, result);

        AddAuditEntity(result, state, DataContext.ExternalApplicationInitializations);
    }

    public void WriteAudit(Product.AWP.Infrastructure.DAL.Entities.WebApplicationInitialization entity, AuditableEntityState state)
    {
        var result = new WebApplicationInitialization
        {
            StartUrl = entity.StartUrl,
            SuppressScriptErrors = entity.SuppressScriptErrors
        };
        MapApplicationInitialization(entity, result);

        AddAuditEntity(result, state, DataContext.WebApplicationInitializations);
    }

    public void WriteAudit(Product.AWP.Infrastructure.DAL.Entities.Configuration entity, AuditableEntityState state)
    {
        var result = new Configuration
        {
            Id = entity.Id,
            Value = entity.Value,
            Name = entity.Name,
            Description = entity.Description
        };

        AddAuditEntity(result, state, DataContext.Configurations);
    }

    public void WriteAudit(Product.AWP.Infrastructure.DAL.Entities.CrossWorkflow entity, AuditableEntityState state)
    {
        var result = new CrossWorkflow
        {
            Id = entity.Id,
            Order = entity.Order,
            HostWorkflowId = entity.HostWorkflowId,
            TargetWorkflowId = entity.TargetWorkflowId
        };

        AddAuditEntity(result, state, DataContext.CrossWorkflows);
    }

    public void WriteAudit(Product.AWP.Infrastructure.DAL.Entities.CustomAttribute entity, AuditableEntityState state)
    {
        var result = new CustomAttribute
        {
            Id = entity.Id,
            Name = entity.Name,
            Value = entity.Value,
            Image = entity.Image,
            ServiceAreaId = entity.ServiceAreaId,
            UserRoleId = entity.UserRoleId,
            OperatorStatusId = entity.OperatorStatusId,
            WorkplaceId = entity.WorkplaceId
        };

        AddAuditEntity(result, state, DataContext.CustomAttributes);
    }

    public void WriteAudit(Product.AWP.Infrastructure.DAL.Entities.Layout entity, AuditableEntityState state)
    {
        var result = new Layout
        {
            Id = entity.Id,
            Value = entity.Value,
            Name = entity.Name,
            Order = entity.Order,
            Description = entity.Description
        };

        AddAuditEntity(result, state, DataContext.Layouts);
    }

    public void WriteLayoutMappingAudit(Guid layoutId, Guid userRoleId, AuditableEntityState state)
    {
        var result = new LayoutUserRole
        {
            LayoutId = layoutId,
            UserRoleId = userRoleId
        };

        AddAuditEntity(result, state, DataContext.LayoutUserRoles);
    }

    public void WriteAudit(Product.AWP.Infrastructure.DAL.Entities.LoginWorkflow entity, AuditableEntityState state)
    {
        var result = new LoginWorkflow
        {
            Id = entity.Id,
            WorkflowDefinition = entity.WorkflowDefinition,
            ApplicationId = entity.ApplicationId
        };

        AddAuditEntity(result, state, DataContext.LoginWorkflows);
    }

    public void WriteAudit(Product.AWP.Infrastructure.DAL.Entities.Module entity, AuditableEntityState state)
    {
        var result = new Module
        {
            Id = entity.Id,
            Name = entity.Name,
            Initialization = entity.Initialization,
            Description = entity.Description,
            IsOptional = entity.IsOptional,
            IsDisabled = entity.IsDisabled
        };

        AddAuditEntity(result, state, DataContext.Modules);
    }

    public void WriteModuleMappingAudit(Guid moduleId, Guid userRoleId, AuditableEntityState state)
    {
        var result = new ModuleUserRole
        {
            ModuleId = moduleId,
            UserRoleId = userRoleId
        };

        AddAuditEntity(result, state, DataContext.ModuleUserRoles);
    }

    public void WriteAudit(Product.AWP.Infrastructure.DAL.Entities.Operator entity, AuditableEntityState state)
    {
        var result = new Operator
        {
            Id = entity.Id,
            FirstName = entity.FirstName,
            MiddleName = entity.MiddleName,
            LastName = entity.LastName,
            Nickname = entity.Nickname,
            UserName = entity.UserName,
            ActiveDirectoryId = entity.ActiveDirectoryId
        };

        AddAuditEntity(result, state, DataContext.Operators);
    }

    public void WriteAudit(Product.AWP.Infrastructure.DAL.Entities.OperatorCustomAttribute entity, AuditableEntityState state)
    {
        var result = new OperatorCustomAttribute
        {
            Code = entity.Code,
            OperatorId = entity.OperatorId,
            BoolValue = entity.BoolValue,
            DecimalValue = entity.DecimalValue,
            StringValue = entity.StringValue,
            LongValue = entity.LongValue,
            DateTimeValue = entity.DateTimeValue,
            GuidValue = entity.GuidValue,
            BinaryValue = entity.BinaryValue
        };

        AddAuditEntity(result, state, DataContext.OperatorCustomAttributes);
    }

    public void WriteAudit(Product.AWP.Infrastructure.DAL.Entities.OperatorGroupCustomAttribute entity, AuditableEntityState state)
    {
        var result = new OperatorGroupCustomAttribute
        {
            Code = entity.Code,
            OperatorGroupId = entity.OperatorGroupId,
            BoolValue = entity.BoolValue,
            DecimalValue = entity.DecimalValue,
            StringValue = entity.StringValue,
            LongValue = entity.LongValue,
            DateTimeValue = entity.DateTimeValue,
            GuidValue = entity.GuidValue,
            BinaryValue = entity.BinaryValue
        };

        AddAuditEntity(result, state, DataContext.OperatorGroupCustomAttributes);
    }

    public void WriteAudit(Product.AWP.Infrastructure.DAL.Entities.OperatorGroup entity, AuditableEntityState state)
    {
        var result = new OperatorGroup
        {
            Id = entity.Id,
            Name = entity.Name,
            ParentId = entity.ParentId
        };

        AddAuditEntity(result, state, DataContext.OperatorGroups);
    }

    public void WriteOperatorOperatorGroupMappingAudit(Guid operatorId, Guid operatorGroupId, AuditableEntityState state)
    {
        var result = new OperatorOperatorGroup
        {
            OperatorId = operatorId,
            OperatorGroupId = operatorGroupId
        };

        AddAuditEntity(result, state, DataContext.OperatorOperatorGroups);
    }

    public void WriteAudit(Product.AWP.Infrastructure.DAL.Entities.OperatorStatus entity, AuditableEntityState state)
    {
        var result = new OperatorStatus
        {
            Id = entity.Id,
            Name = entity.Name,
            Code = entity.Code,
            IsStarting = entity.IsStarting,
            IsTerminating = entity.IsTerminating,
            ValidityDuration = entity.ValidityDuration
        };

        AddAuditEntity(result, state, DataContext.OperatorStatuses);
    }

    public void WriteOperatorStatusMappingAudit(Guid operatorStatusId, Guid userRoleId, AuditableEntityState state)
    {
        var result = new OperatorStatusUserRole
        {
            OperatorStatusId = operatorStatusId,
            UserRoleId = userRoleId
        };

        AddAuditEntity(result, state, DataContext.OperatorStatuseUserRoles);
    }

    public void WriteAudit(Product.AWP.Infrastructure.DAL.Entities.Permission entity, AuditableEntityState state)
    {
        var result = new Permission
        {
            Id = entity.Id,
            Name = entity.Name,
            Description = entity.Description
        };

        AddAuditEntity(result, state, DataContext.Permissions);
    }

    public void WritePermissionMappingAudit(Guid permissionId, Guid userRoleId, AuditableEntityState state)
    {
        var result = new PermissionUserRole
        {
            PermissionId = permissionId,
            UserRoleId = userRoleId
        };

        AddAuditEntity(result, state, DataContext.PermissionUserRoles);
    }

    public void WriteAudit(Product.AWP.Infrastructure.DAL.Entities.ServiceArea entity, AuditableEntityState state)
    {
        var result = new ServiceArea
        {
            Id = entity.Id,
            Name = entity.Name,
            ServiceAreaTypeId = entity.ServiceAreaTypeId,
            ParentId = entity.ParentId,
            UtcOffset = entity.UtcOffset,
            Code = entity.Code
        };

        AddAuditEntity(result, state, DataContext.ServiceAreas);
    }

    public void WriteAudit(Product.AWP.Infrastructure.DAL.Entities.ServiceAreaConfiguration entity, AuditableEntityState state)
    {
        var result = new ServiceAreaConfiguration
        {
            Id = entity.Id,
            ServiceAreaId = entity.ServiceAreaId,
            PropertyKey = entity.PropertyKey,
            Value = entity.Value,
            ShortValue = entity.ShortValue,
            IntValue = entity.IntValue,
            DoubleValue = entity.DoubleValue,
            LongValue = entity.LongValue,
            GuidValue = entity.GuidValue,
            DateTimeValue = entity.DateTimeValue,
            BoolValue = entity.BoolValue,
            ConfigurationId = entity.ConfigurationId,
            ApplicationId = entity.ApplicationId,
            ModuleId = entity.ModuleId,
            ShortcutId = entity.ShortcutId,
            AesQueueId = entity.AesQueueId
        };

        AddAuditEntity(result, state, DataContext.ServiceAreaConfigurations);
    }

    public void WriteAudit(Product.AWP.Infrastructure.DAL.Entities.ServiceAreaType entity, AuditableEntityState state)
    {
        var result = new ServiceAreaType
        {
            Id = entity.Id,
            Name = entity.Name
        };

        AddAuditEntity(result, state, DataContext.ServiceAreaTypes);
    }

    public void WriteAudit(Product.AWP.Infrastructure.DAL.Entities.UserGroup entity, AuditableEntityState state)
    {
        var result = new UserGroup
        {
            Id = entity.Id,
            Name = entity.Name,
            Description = entity.Description
        };

        AddAuditEntity(result, state, DataContext.UserGroups);
    }

    public void WriteAudit(Product.AWP.Infrastructure.DAL.Entities.Identity entity, AuditableEntityState state)
    {
        var result = new Identity
        {
            Id = entity.Id,
            Login = entity.Login
        };

        AddAuditEntity(result, state, DataContext.Identities);
    }

    public void WriteUserGroupIdentityMappingAudit(Guid userGroupId, Guid identityId, AuditableEntityState state)
    {
        var result = new UserGroupIdentity
        {
            UserGroupId = userGroupId,
            IdentityId = identityId
        };

        AddAuditEntity(result, state, DataContext.UserGroupIdentities);
    }

    public void WriteUserGroupServiceAreaMappingAudit(Guid userGroupId, Guid serviceAreaId, AuditableEntityState state)
    {
        var result = new UserGroupServiceArea
        {
            UserGroupId = userGroupId,
            ServiceAreaId = serviceAreaId
        };

        AddAuditEntity(result, state, DataContext.UserGroupServiceAreas);
    }

    public void WriteUserGroupUserRoleMappingAudit(Guid userGroupId, Guid userRoleId, AuditableEntityState state)
    {
        var result = new UserGroupUserRole
        {
            UserGroupId = userGroupId,
            UserRoleId = userRoleId
        };

        AddAuditEntity(result, state, DataContext.UserGroupUserRoles);
    }

    public void WriteUserGroupWorkplaceMappingAudit(Guid userGroupId, Guid workplaceId, AuditableEntityState state)
    {
        var result = new UserGroupWorkplace
        {
            UserGroupId = userGroupId,
            WorkplaceId = workplaceId
        };

        AddAuditEntity(result, state, DataContext.UserGroupWorkplaces);
    }

    public void WriteAudit(Product.AWP.Infrastructure.DAL.Entities.Shortcut entity, AuditableEntityState state)
    {
        var result = new Shortcut
        {
            Id = entity.Id,
            ApplicationId = entity.ApplicationId,
            Disabled = entity.Disabled,
            Type = entity.Type,
            Action = entity.Action,
            SortOrder = entity.SortOrder,
            Name = entity.Name,
            Description = entity.Description
        };

        AddAuditEntity(result, state, DataContext.Shortcuts);
    }

    public void WriteShortcutMappingAudit(Guid shortcutId, Guid userRoleId, AuditableEntityState state)
    {
        var result = new ShortcutUserRole
        {
            ShortcutId = shortcutId,
            UserRoleId = userRoleId
        };

        AddAuditEntity(result, state, DataContext.ShortcutUserRoles);
    }

    public void WriteAudit(Product.AWP.Infrastructure.DAL.Entities.UserShortcut entity, AuditableEntityState state)
    {
        var result = new UserShortcut
        {
            Id = entity.Id,
            UserName = entity.UserName,
            Type = entity.Type,
            Action = entity.Action,
            SortOrder = entity.SortOrder,
            Name = entity.Name,
            Description = entity.Description
        };

        AddAuditEntity(result, state, DataContext.UserShortcuts);
    }

    public void WriteAudit(Product.AWP.Infrastructure.DAL.Entities.Workflow entity, AuditableEntityState state)
    {
        var result = new Workflow
        {
            Id = entity.Id,
            SerializedValue = entity.SerializedValue,
            EventThreadOption = entity.EventThreadOption,
            InvokeAsync = entity.InvokeAsync,
            Event = new TypeInfo {Assembly = entity.Event.Assembly, TypeName = entity.Event.TypeName},
            Name = entity.Name,
            Order = entity.Order,
            Group = entity.Group,
            Description = entity.Description,
            IsBusinessScenario = entity.IsBusinessScenario,
            IsService = entity.IsService,
            Code = entity.Code,
            Icon = entity.Icon,
            WorkflowGroupId = entity.WorkflowGroupId,
            SummaryControl = new TypeInfo {Assembly = entity.SummaryControl.Assembly, TypeName = entity.SummaryControl.TypeName},
            Configuration = new TypeInfo {Assembly = entity.Configuration.Assembly, TypeName = entity.Configuration.TypeName},
            ConfigurationValue = entity.ConfigurationValue,
            UseConfiguration = entity.UseConfiguration
        };

        AddAuditEntity(result, state, DataContext.Workflows);
    }

    public void WriteWorkflowMappingAudit(Guid workflowId, Guid userRoleId, AuditableEntityState state)
    {
        var result = new WorkflowUserRole
        {
            WorkfolwId = workflowId,
            UserRoleId = userRoleId
        };

        AddAuditEntity(result, state, DataContext.WorkflowUserRoles);
    }

    public void WriteAudit(Product.AWP.Infrastructure.DAL.Entities.WorkflowGroup entity, AuditableEntityState state)
    {
        var result = new WorkflowGroup
        {
            Id = entity.Id,
            Name = entity.Name,
            Description = entity.Description,
            Image = entity.Image,
            Order = entity.Order
        };

        AddAuditEntity(result, state, DataContext.WorkflowGroups);
    }

    public void WriteAudit(Product.AWP.Infrastructure.DAL.Entities.Workplace entity, AuditableEntityState state)
    {
        var result = new Workplace
        {
            Id = entity.Id,
            Name = entity.Name,
            Code = entity.Code,
            UtcOffset = entity.UtcOffset,
            ParentId = entity.ParentId
        };

        AddAuditEntity(result, state, DataContext.Workplaces);
    }

    #endregion
}
