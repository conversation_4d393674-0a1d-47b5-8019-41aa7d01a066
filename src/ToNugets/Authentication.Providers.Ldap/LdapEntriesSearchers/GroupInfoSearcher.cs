using System.DirectoryServices.Protocols;
using System.Text;
using Platform.AWP.Authentication.Providers.Ldap.LdapAttributes;
using Platform.AWP.Authentication.Providers.Ldap.LdapEntries;
using Platform.Identities.Base;

namespace Platform.AWP.Authentication.Providers.Ldap.LdapEntriesSearchers;

internal class GroupInfoSearcher : ProviderBase
{
    private readonly DomainsProcessor _domainsProcessor;

    public GroupInfoSearcher(TimeSpan cacheExpirationTimeout, DomainsProcessor domainsProcessor) : base(
        cacheExpirationTimeout)
    {
        _domainsProcessor = domainsProcessor;
    }

    public async Task<GroupIdentityInfoWithMembers> FindGroupInfo(Guid groupId)
    {
        return await GetFromCacheOrSet($"{nameof(FindGroupInfo)}({groupId})",
            async () => await DoFindGroupInfo(groupId));
    }

    public async Task<GroupIdentityInfo> FindGroupInfo(string groupName)
    {
        return await GetFromCacheOrSet($"{nameof(FindGroupInfo)}({groupName})",
            async () => await DoFindGroupInfo(groupName));
    }

    private async Task<GroupIdentityInfoWithMembers> DoFindGroupInfo(Guid groupId)
    {
        SearchResultEntry groupEntry = null;
        string groupDomain = null;

        foreach (var fullDomainName in _domainsProcessor.FullDomainNames)
        {
            var ldapConnectionWrapper = _domainsProcessor.GetLdapConnection(fullDomainName);

            var searchRequest = BuildSearchRequestForGroupById(groupId, fullDomainName,
                LdapAttributeNames.ObjectSid, LdapAttributeNames.SamAccountName, LdapAttributeNames.ObjectGuid,
                LdapAttributeNames.Member);
            var searchResult = await ldapConnectionWrapper.ExecuteLdapAction(async ldapConnection => await ldapConnection.FindEntries(searchRequest));

            if (searchResult.Count != 1) continue;

            groupEntry = searchResult[0];
            groupDomain = fullDomainName;
            break;
        }

        if (groupEntry == null) return null;

        var groupAttrs = groupEntry.Attributes;

        var groupSid = LdapAttributes.LdapAttributes.ObjectSid.GetValue(groupAttrs);
        var groupSamAccountName = LdapAttributes.LdapAttributes.SamAccountName.GetValue(groupAttrs);
        var groupGuid = LdapAttributes.LdapAttributes.ObjectGuid.GetValue(groupAttrs);

        //	тема про определение принадлежности в случае с FSP: https://stackoverflow.com/questions/********/how-to-check-if-user-is-a-member-of-crossdomain-group-in-ldap
        var totalMembers = LdapAttributes.LdapAttributes.Member.GetValues(groupAttrs).ToList();

        var membersInfos = new List<LdapEntryInfoBase>();

        var ldapConnectionWrapperForGroupDomain = _domainsProcessor.GetLdapConnection(groupDomain);
        var groupMembersSearchRequest = BuildSearchRequestForGroupMembers(groupEntry.DistinguishedName,
            groupDomain, LdapAttributeNames.ObjectClass, LdapAttributeNames.ObjectSid,
            LdapAttributeNames.SamAccountName, LdapAttributeNames.ObjectGuid,
            LdapAttributeNames.UserPrincipalName);
        var groupMembers = await ldapConnectionWrapperForGroupDomain.ExecuteLdapAction(async ldapConnection => await ldapConnection.FindEntries(groupMembersSearchRequest));

        foreach (SearchResultEntry groupMember in groupMembers)
        {
            var memberDn = groupMember.DistinguishedName;
            var totalMember = totalMembers.FirstOrDefault(x => string.Equals(x, memberDn, StringComparison.InvariantCultureIgnoreCase));

            var memberAttrs = groupMember.Attributes;

            var memberSid = LdapAttributes.LdapAttributes.ObjectSid.GetValue(memberAttrs);
            var memberClasses = LdapAttributes.LdapAttributes.ObjectClass.GetValues(memberAttrs);
            var memberSam = LdapAttributes.LdapAttributes.SamAccountName.GetValue(memberAttrs);
            var memberGuid = LdapAttributes.LdapAttributes.ObjectGuid.GetValue(memberAttrs);

            if (memberClasses.Contains("user"))
            {
                if (LdapAttributes.LdapAttributes.UserPrincipalName.TryGetValue(memberAttrs, out var memberUpn))
                {
                    membersInfos.Add(new UserIdentityInfo(groupDomain, memberDn, memberSid, memberSam, memberGuid, memberUpn));
                }
                else
                {
                    //	нет userPrincipalName... случилось такое с какой-то служебной УЗ. Не разбирался пока, почему такое может быть...
                    //	заюзаем пока sam в качестве upn
                    membersInfos.Add(new UserIdentityInfo(groupDomain, memberDn, memberSid, memberSam, memberGuid, memberSam));
                }
            }
            else if (memberClasses.Contains("group"))
            {
                membersInfos.Add(new GroupIdentityInfo(groupDomain, memberDn, memberSid, memberSam, memberGuid));
            }
            else
            {
                throw new Exception($"Unknown entry class! ({string.Join(",", memberClasses)})");
            }

            if (totalMember != null)
            {
                //	среди более широкого набора нашли - удаляем из широкого, потому что в итоговый список добавили тут
                totalMembers.Remove(totalMember);
            }
        }

        foreach (var member in totalMembers)
        {
            membersInfos.Add(new LdapEntryInfoBase(LdapEntryType.ForeignSecurityPrincipal, groupDomain, member));
        }

        return new GroupIdentityInfoWithMembers(groupDomain, groupEntry.DistinguishedName, groupSid, groupSamAccountName, groupGuid, membersInfos.ToArray());
    }

    private SearchRequest BuildSearchRequestForGroupById(Guid groupEntityId, string domainName, params string[] attributes)
    {
        var domainDistinguishedName = _domainsProcessor.GetDomainDistinguishedName(domainName);

        var entityIdBytes = groupEntityId.ToByteArray();
        var hex = new StringBuilder(entityIdBytes.Length * 3);
        foreach (var b in entityIdBytes)
        {
            hex.AppendFormat("\\{0:x2}", b);
        }

        var searchFilter = $"(&(objectClass=group)({LdapAttributeNames.ObjectGuid}={hex}))";

        return new SearchRequest(domainDistinguishedName, searchFilter, SearchScope.Subtree, attributes);
    }

    private SearchRequest BuildSearchRequestForGroupMembers(string groupCn, string domainName, params string[] attributes)
    {
        var domainDistinguishedName = _domainsProcessor.GetDomainDistinguishedName(domainName);
        var searchFilter = $"(&(|(objectClass=group)(objectClass=user))(memberOf={groupCn}))";

        return new SearchRequest(domainDistinguishedName, searchFilter, SearchScope.Subtree, attributes);
    }

    private async Task<GroupIdentityInfo> DoFindGroupInfo(string groupName)
    {
        SearchResultEntry groupEntry = null;
        string groupDomain = null;

        foreach (var fullDomainName in _domainsProcessor.FullDomainNames)
        {
            var ldapConnectionWrapper = _domainsProcessor.GetLdapConnection(fullDomainName);

            var searchRequest = BuildSearchRequestForGroupById(groupName, fullDomainName,
                LdapAttributeNames.ObjectSid, LdapAttributeNames.SamAccountName, LdapAttributeNames.ObjectGuid,
                LdapAttributeNames.Member);
            var searchResult = await ldapConnectionWrapper.ExecuteLdapAction(async ldapConnection => await ldapConnection.FindEntries(searchRequest));

            if (searchResult.Count != 1) continue;

            groupEntry = searchResult[0];
            groupDomain = fullDomainName;
            break;
        }

        if (groupEntry == null) return null;


        var groupAttrs = groupEntry.Attributes;

        var groupSid = LdapAttributes.LdapAttributes.ObjectSid.GetValue(groupAttrs);
        var groupSamAccountName = LdapAttributes.LdapAttributes.SamAccountName.GetValue(groupAttrs);
        var groupGuid = LdapAttributes.LdapAttributes.ObjectGuid.GetValue(groupAttrs);

        return new GroupIdentityInfo(groupDomain, groupEntry.DistinguishedName, groupSid, groupSamAccountName, groupGuid);
    }

    private SearchRequest BuildSearchRequestForGroupById(string groupName, string domainName, params string[] attributes)
    {
        var domainDistinguishedName = _domainsProcessor.GetDomainDistinguishedName(domainName);
        var searchFilter = $"(&(objectClass=group)({LdapAttributeNames.Cn}={groupName}))";

        return new SearchRequest(domainDistinguishedName, searchFilter, SearchScope.Subtree, attributes);
    }
}
