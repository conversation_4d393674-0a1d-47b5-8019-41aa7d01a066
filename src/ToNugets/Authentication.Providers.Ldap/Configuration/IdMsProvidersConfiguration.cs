using Microsoft.Extensions.Configuration;

namespace Platform.AWP.Authentication.Providers.Ldap.Configuration;

public class IdMsProvidersConfiguration
{
    public const string DefaultConfigSectionName = "IdMsProvidersConfiguration";

    public ProvidersType ProvidersType { get; set; }
    public int CacheInMinuites { get; set; }

    public static IdMsProvidersConfiguration ReadFromConfig(IConfiguration configuration, string sectionName = DefaultConfigSectionName)
    {
        if (configuration == null) throw new ArgumentNullException(nameof(configuration));

        var configSection = configuration.GetSection(sectionName);
        return configSection?.Get<IdMsProvidersConfiguration>();
    }
}
