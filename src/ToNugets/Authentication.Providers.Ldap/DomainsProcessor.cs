using System.DirectoryServices.Protocols;
using Microsoft.Extensions.Logging;

#pragma warning disable CS1591

namespace Platform.AWP.Authentication.Providers.Ldap;

public sealed class DomainsProcessor
{
    private readonly DomainNameInfo[] _domains;
    private readonly Func<string, LdapConnection> _ldapConnectionFactoryMethod;
    private readonly ILoggerFactory _loggerFactory;

    public DomainsProcessor(DomainNameInfo[] domainNameInfos, Func<string, LdapConnection> ldapConnectionFactoryMethod, ILoggerFactory loggerFactory)
    {
        if (domainNameInfos == null) throw new ArgumentNullException(nameof(domainNameInfos));
        if (domainNameInfos.Length == 0)
        {
            //	TODO: ANB: дефолтный домен надо как-то получить
            throw new NotSupportedException("Constructing without domains specified is not supported");
        }

        _domains = domainNameInfos;
        _ldapConnectionFactoryMethod = ldapConnectionFactoryMethod ?? throw new ArgumentNullException(nameof(ldapConnectionFactoryMethod));
        _loggerFactory = loggerFactory;
        FullDomainNames = _domains.Select(x => x.Name).ToArray();
    }

    public IEnumerable<string> FullDomainNames { get; }

    public string GetDomainDistinguishedName(string domainName)
    {
        var fullDomainName = GetFullDomainName(domainName);
        var domainParts = SplitDomainIntoParts(fullDomainName);
        return string.Join(",", domainParts.Select(x => $"DC={x}"));
    }

    private bool CheckDomainKvp(string domainName, string checkingName, string[] checkingParts)
    {
        if (string.Equals(domainName, checkingName, StringComparison.InvariantCultureIgnoreCase))
        {
            return true;
        }

        var configuredDomainParts = checkingParts;
        var checkingDomainParts = SplitDomainIntoParts(domainName);

        if (checkingDomainParts.Length >= configuredDomainParts.Length)
        {
            return false;
        }

        var allPartsEquals = true;
        for (var i = 0; i < checkingDomainParts.Length; i++)
        {
            if (string.Equals(checkingDomainParts[i], configuredDomainParts[i], StringComparison.InvariantCultureIgnoreCase))
            {
                continue;
            }

            allPartsEquals = false;
            break;
        }

        return allPartsEquals;
    }

    public string GetFullDomainName(string domainName)
    {
        foreach (var configuredDomainKvp in _domains)
        {
            if (CheckDomainKvp(domainName, configuredDomainKvp.Name, configuredDomainKvp.NameParts))
            {
                return configuredDomainKvp.Name;
            }

            if (configuredDomainKvp.Aliases != null)
            {
                foreach (var aliasParts in configuredDomainKvp.Aliases)
                {
                    if (CheckDomainKvp(domainName, aliasParts.Key, aliasParts.Value))
                    {
                        return configuredDomainKvp.Name;
                    }
                }
            }
        }

        //	если сюда пришли, объяснение одно: домен, о котором мы не знаем, считаем, что он "полный", просто другой
        return domainName;
    }

    private static string[] SplitDomainIntoParts(string fullDomainName)
    {
        return fullDomainName.Split('.');
    }

    public LdapConnectionWrapper GetLdapConnection(string domain)
    {
        return new LdapConnectionWrapper(domain, CreateNewLdapConnection, _loggerFactory);
    }

    private LdapConnection CreateNewLdapConnection(string domain)
    {
        //logger.LogMethodRequested(LoggingCategory.Default, this);
        //	TODO: ANB: исправить
        var result = _ldapConnectionFactoryMethod(domain);
        if (result.AutoBind)
        {
            //logger.LogMethodDetails("AutoBind set, not calling Bind...", LoggingCategory.Default, this);
        }
        else
        {
            //logger.LogMethodDetails("Calling Bind...", LoggingCategory.Default, this);
            result.Bind();
            //logger.LogMethodDetails("Bind completed", LoggingCategory.Default, this);
        }

        //logger.LogMethodCompleted(LoggingCategory.Default, this);
        return result;
    }

    public class DomainNameInfo
    {
        public DomainNameInfo(string name, params string[] aliases)
        {
            Name = name;
            NameParts = SplitDomainIntoParts(Name);
            Aliases = aliases?.ToDictionary(x => x, SplitDomainIntoParts);
        }

        public string Name { get; }
        public string[] NameParts { get; }
        public Dictionary<string, string[]> Aliases { get; }
    }
}
