using System.DirectoryServices.Protocols;
using System.Text;
using Microsoft.Extensions.Logging;
using Platform.AWP.Authentication.Providers.Ldap.LdapAttributes;
using Platform.Identities;
using Platform.Identities.Base;
using Platform.Identities.Exceptions;
using Platform.Logging;
using Platform.Logging.MicrosoftExtensions;
using ILogger = Microsoft.Extensions.Logging.ILogger;

#pragma warning disable CS1591

namespace Platform.AWP.Authentication.Providers.Ldap;

public class LdapIdentityProvider : IdentityProviderBase, IHasAdditionalLogInfo
{
    private readonly DomainsProcessor _domainsProcessor;

    private readonly ILogger _logger;

    public LdapIdentityProvider(TimeSpan cacheExpirationTimeout, DomainsProcessor domainsProcessor, ILoggerFactory loggerFactory)
        : base(cacheExpirationTimeout)
    {
        _domainsProcessor = domainsProcessor ?? throw new ArgumentNullException(nameof(domainsProcessor));
        _logger = loggerFactory?.CreateLogger(GetType());
        AdditionalLogInfo = new AdditionalLogInfo(LoggingCategory.Default);
        AdditionalLogInfo.AddProperty(nameof(cacheExpirationTimeout), cacheExpirationTimeout);
    }

    public AdditionalLogInfo AdditionalLogInfo { get; protected set; }

    public override void SplitLogin(string login, out string username, out string domain)
    {
        if (login.Contains("@"))
        {
            var loginParts = login.Split('@');
            username = loginParts[0];
            domain = _domainsProcessor.GetFullDomainName(loginParts[1]);
        }
        else if (login.Contains("\\"))
        {
            var loginParts = login.Split('\\');
            username = loginParts[1];
            domain = _domainsProcessor.GetFullDomainName(loginParts[0]);
        }
        else
        {
            username = login;
            domain = null;
        }
    }

    public override T ConvertAttribute<T>(object value)
    {
        var type = typeof(T);
        var attribute = value as DirectoryAttribute;

        if (attribute == null)
        {
            throw new ArgumentException($"{nameof(value)} is not a {typeof(DirectoryAttribute)}");
        }

        var result = attribute.GetValues(type)[0];
        return (T) Convert.ChangeType(result, typeof(T));
    }

    protected override async Task<Guid> GetIdentityIdByLogin(string login)
    {
        using var mlh = this.CreateMethodLogHelper(_logger).WithArgs("login={login}", login);

        return await mlh.ExecuteWithTryAsync(async () =>
        {
            var searchResultPair = await FindEntry(login, LdapAttributeNames.ObjectGuid);
            return CheckSearchResultAndExtractObjectGuid(login, searchResultPair.Item1, searchResultPair.Item2);
        });
    }

    protected override async Task<Identity> GetIdentityByLogin(string login, string[] attributes)
    {
        using var mlh = this.CreateMethodLogHelper(_logger).WithArgs("login={login}", login);

        return await mlh.ExecuteWithTryAsync(async () =>
        {
            var attributesToLoad = new List<string>(attributes ?? Array.Empty<string>())
            {
                LdapAttributeNames.ObjectGuid,
                LdapAttributeNames.SamAccountName,
                LdapAttributeNames.UserPrincipalName
            };

            var searchResultPair = await FindEntry(login, attributesToLoad.ToArray());
            var searchResult = searchResultPair.Item1;
            var objectGuid = CheckSearchResultAndExtractObjectGuid(login, searchResult, searchResultPair.Item2);

            var upnFound = LdapAttributes.LdapAttributes.UserPrincipalName.TryGetValue(searchResult.Attributes, out var upn);
            var samFound = LdapAttributes.LdapAttributes.SamAccountName.TryGetValue(searchResult.Attributes, out var samAccountName);

            var result = new Identity
            {
                Id = objectGuid,
                Name = upnFound ? upn : samFound ? samAccountName : null
            };

            if (attributes != null)
            {
                var attrResult = new List<Tuple<string, object>>();
                foreach (var attributeName in attributes)
                {
                    if (searchResult.Attributes.Contains(attributeName))
                    {
                        var attributeValue = searchResult.Attributes[attributeName];
                        attrResult.Add(new Tuple<string, object>(attributeName, attributeValue));
                    }
                }

                result.Attributes = attrResult.ToArray();
            }

            return result;
        });
    }

    protected override async Task<string> GetLoginByIdentityId(Guid identityId)
    {
        SearchResultEntry searchResult = null;
        var searchErrors = new List<Exception>();
        foreach (var domainToSearchIn in _domainsProcessor.FullDomainNames)
        {
            var ldapConnectionWrapper = _domainsProcessor.GetLdapConnection(domainToSearchIn);
            var searchRequest = BuildSearchRequestForUsersOrGroupsById(identityId, domainToSearchIn, LdapAttributeNames.UserPrincipalName, LdapAttributeNames.SamAccountName);
            try
            {
                searchResult = await ldapConnectionWrapper.ExecuteLdapAction(async ldapConnection => await ldapConnection.FindEntry(searchRequest));
            }
            catch (Exception exc)
            {
                searchErrors.Add(exc);
            }

            if (searchResult != null) break;
        }

        if (searchResult == null || searchResult.Attributes.Count == 0 || (!searchResult.Attributes.Contains(LdapAttributeNames.UserPrincipalName) && !searchResult.Attributes.Contains(LdapAttributeNames.SamAccountName)))
        {
            //	TODO: EXC: свой тип исключения
            if (searchErrors.Count > 0)
            {
                throw new AggregateException("Not found identity for requested identityId - errors occured during search over domains", searchErrors);
            }

            throw new ArgumentException("Not found identity for requested identityId", nameof(identityId));
        }

        var upnAttr = searchResult.Attributes.Contains(LdapAttributeNames.UserPrincipalName)
            ? searchResult.Attributes[LdapAttributeNames.UserPrincipalName]
            : searchResult.Attributes[LdapAttributeNames.SamAccountName];
        var resultAsObj = upnAttr.GetValues(typeof(string))[0];

        return $"{resultAsObj}";
    }

    private async Task<Tuple<SearchResultEntry, List<Exception>>> FindEntry(string login, params string[] attributesToLoad)
    {
        SplitLogin(login, out var userName, out var domainName);

        var domainsToSearchIn = string.IsNullOrEmpty(domainName) ? _domainsProcessor.FullDomainNames : new[] {_domainsProcessor.GetFullDomainName(domainName)};

        SearchResultEntry searchResult = null;
        var searchErrors = new List<Exception>();
        foreach (var domainToSearchIn in domainsToSearchIn)
        {
            var ldapConnectionWrapper = _domainsProcessor.GetLdapConnection(domainToSearchIn);
            var searchRequest = BuildSearchRequestForUsersOrGroupsByName(userName, domainToSearchIn, attributesToLoad);
            try
            {
                searchResult = await ldapConnectionWrapper.ExecuteLdapAction(async ldapConnection => await ldapConnection.FindEntry(searchRequest, _logger));
            }
            catch (Exception exc)
            {
                searchErrors.Add(exc);
            }

            if (searchResult != null) break;
        }

        return new Tuple<SearchResultEntry, List<Exception>>(searchResult, searchErrors);
    }

    private Guid CheckSearchResultAndExtractObjectGuid(string login, SearchResultEntry searchResult, List<Exception> searchErrors)
    {
        if (searchResult == null || searchResult.Attributes.Count == 0 || !searchResult.Attributes.Contains(LdapAttributeNames.ObjectGuid))
        {
            if (searchErrors.Count > 0)
            {
                throw new IdentitySearchException($"Not found identity for requested login='{login}' - errors occured during search over domains", searchErrors);
            }

            throw new IdentityNotFoundException($"Not found identity for requested login='{login}'");
        }

        if (searchResult.Attributes[LdapAttributeNames.ObjectGuid][0] is not byte[] objectGuidBytes)
        {
            throw new IdentitySearchException("Found identity for requested login does not have GUID");
        }

        return new Guid(objectGuidBytes);
    }

    private SearchRequest BuildSearchRequestForUsersOrGroupsByName(string userName, string domainName, params string[] attributes)
    {
        var domainDistinguishedName = _domainsProcessor.GetDomainDistinguishedName(domainName);
        var searchFilter = $"(&(|(objectClass=group)(objectClass=user))({LdapAttributeNames.SamAccountName}={userName}))";

        return new SearchRequest(domainDistinguishedName, searchFilter, SearchScope.Subtree, attributes);
    }

    private SearchRequest BuildSearchRequestForUsersOrGroupsById(Guid entityId, string domainName, params string[] attributes)
    {
        var domainDistinguishedName = _domainsProcessor.GetDomainDistinguishedName(domainName);

        var entityIdBytes = entityId.ToByteArray();
        var hex = new StringBuilder(entityIdBytes.Length * 3);
        foreach (var b in entityIdBytes)
        {
            hex.AppendFormat("\\{0:x2}", b);
        }

        var searchFilter = $"(&(|(objectClass=group)(objectClass=user))({LdapAttributeNames.ObjectGuid}={hex}))";

        return new SearchRequest(domainDistinguishedName, searchFilter, SearchScope.Subtree, attributes);
    }
}
