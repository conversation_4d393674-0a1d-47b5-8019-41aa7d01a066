<Project Sdk="Microsoft.NET.Sdk">
    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
        <AssemblyName>Platform.AWP.Authentication.Providers.Ldap</AssemblyName>
        <RootNamespace>Platform.AWP.Authentication.Providers.Ldap</RootNamespace>
        <LangVersion>preview</LangVersion>
        <Product>Product</Product>
        <Version>7.0.7</Version>
        <GenerateDocumentationFile>True</GenerateDocumentationFile>
        <ManagePackageVersionsCentrally>false</ManagePackageVersionsCentrally>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Platform.AWP.Core.DataContracts" Version="10.1.5"/>
        <PackageReference Include="Platform.Identities.Base" Version="2.0.1"/>
        <PackageReference Include="Platform.Identities.Providers.Keycloak" Version="2.0.2"/>
        <PackageReference Include="Platform.Keycloak.Clients" Version="3.0.2"/>
        <PackageReference Include="Platform.Logging.MicrosoftExtensions" Version="4.0.1"/>
        <PackageReference Include="Platform.WebApi.WebProxies.AuthTokenProviders.Oidc" Version="6.0.7" />
        <PackageReference Include="Platform.WebApi.WebProxies.Base" Version="3.1.1"/>
        <PackageReference Include="Platform.WebApps.Auth.Negotiate" Version="4.0.1"/>
        <PackageReference Include="System.DirectoryServices.Protocols" Version="9.0.7" />
        <PackageReference Include="System.Text.Json" Version="9.0.7" />
    </ItemGroup>
</Project>
