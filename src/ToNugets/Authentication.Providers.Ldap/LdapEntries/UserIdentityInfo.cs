namespace Platform.AWP.Authentication.Providers.Ldap.LdapEntries;

internal class UserIdentityInfo : IdentityInfoBase
{
    public UserIdentityInfo(string domain, string distinguishedName, string sid, string accountName, Guid objectGuid, string upn)
        : base(LdapEntryType.User, domain, distinguishedName, sid, accountName, objectGuid)
    {
        Upn = upn ?? throw new ArgumentNullException(nameof(upn));
    }

    public string Upn { get; }
}
