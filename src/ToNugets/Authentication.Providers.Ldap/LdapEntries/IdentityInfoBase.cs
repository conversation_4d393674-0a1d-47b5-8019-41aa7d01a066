namespace Platform.AWP.Authentication.Providers.Ldap.LdapEntries;

internal abstract class IdentityInfoBase : LdapEntryInfoBase
{
    protected IdentityInfoBase(LdapEntryType entryType, string domain, string distinguishedName, string sid, string accountName, Guid objectGuid)
        : base(entryType, domain, distinguishedName)
    {
        Sid = sid ?? throw new ArgumentNullException(nameof(sid));
        AccountName = accountName ?? throw new ArgumentNullException(nameof(accountName));
        ObjectGuid = objectGuid;
    }

    public string Sid { get; }
    public string AccountName { get; }
    public Guid ObjectGuid { get; }
}
