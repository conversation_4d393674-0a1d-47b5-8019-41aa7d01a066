namespace Platform.AWP.Authentication.Providers.Ldap.LdapEntries;

internal class LdapEntryInfoBase
{
    public LdapEntryInfoBase(LdapEntryType entryType, string domain, string distinguishedName)
    {
        EntryType = entryType;
        Domain = domain ?? throw new ArgumentNullException(nameof(domain));
        DistinguishedName = distinguishedName ?? throw new ArgumentNullException(nameof(distinguishedName));
    }

    public LdapEntryType EntryType { get; }
    public string Domain { get; }
    public string DistinguishedName { get; }
}
