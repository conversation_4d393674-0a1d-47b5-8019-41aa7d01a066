namespace Platform.AWP.Authentication.Providers.Ldap.LdapEntries;

internal class GroupIdentityInfoWithMembers : GroupIdentityInfo
{
    public GroupIdentityInfoWithMembers(string domain, string distinguishedName, string sid, string accountName, Guid objectGuid, LdapEntryInfoBase[] members)
        : base(domain, distinguishedName, sid, accountName, objectGuid)
    {
        Members = members ?? throw new ArgumentNullException(nameof(members));
    }

    public LdapEntryInfoBase[] Members { get; }
}
