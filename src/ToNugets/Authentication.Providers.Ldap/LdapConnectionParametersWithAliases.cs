using Microsoft.Extensions.Configuration;
using Platform.WebApps.Auth.Negotiate;

namespace Platform.AWP.Authentication.Providers.Ldap;

public class LdapConnectionParametersWithAliases : LdapConnectionParameters
{
    public string Aliases { get; set; }

    public string[] GetAliasesArray()
    {
        return Aliases?.Split(",", StringSplitOptions.RemoveEmptyEntries | StringSplitOptions.TrimEntries);
    }

    public static LdapConnectionParametersWithAliases[] ReadSettingFromConfiguration(IConfiguration configuration, string settingName = "LdapConnections")
    {
        if (configuration == null) throw new ArgumentNullException(nameof(configuration));
        return configuration.GetSection(settingName).Get<LdapConnectionParametersWithAliases[]>();
    }
}
