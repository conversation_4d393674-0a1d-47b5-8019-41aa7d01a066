using System.DirectoryServices.Protocols;

namespace Platform.AWP.Authentication.Providers.Ldap.LdapAttributes;

internal class LdapSimpleAttribute<TValue> : LdapAttributeBase
{
    public LdapSimpleAttribute(string attributeName)
        : base(attributeName)
    {
    }

    public virtual bool TryGetValue(SearchResultAttributeCollection attributeCollection, out TValue result)
    {
        return TryGetValue<TValue>(attributeCollection, out result);
    }

    public TValue GetValue(SearchResultAttributeCollection attributeCollection)
    {
        if (TryGetValue(attributeCollection, out var result)) return result;
        throw new ArgumentException("Collection does not contain specified attribute");
    }
}
