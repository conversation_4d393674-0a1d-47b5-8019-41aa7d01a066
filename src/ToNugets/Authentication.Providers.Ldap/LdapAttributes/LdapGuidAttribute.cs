using System.DirectoryServices.Protocols;

namespace Platform.AWP.Authentication.Providers.Ldap.LdapAttributes;

internal class LdapGuidAttribute : LdapSimpleAttribute<Guid>
{
    public LdapGuidAttribute(string attributeName) : base(attributeName)
    {
    }

    public override bool TryGetValue(SearchResultAttributeCollection attributeCollection, out Guid result)
    {
        if (!TryGetValue<byte[]>(attributeCollection, out var guidBytes))
        {
            result = Guid.Empty;
            return false;
        }

        result = new Guid(guidBytes);
        return true;
    }
}
