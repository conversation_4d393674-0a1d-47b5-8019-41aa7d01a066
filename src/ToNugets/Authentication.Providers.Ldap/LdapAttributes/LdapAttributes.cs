namespace Platform.AWP.Authentication.Providers.Ldap.LdapAttributes;

internal static class LdapAttributes
{
    public static readonly LdapGuidAttribute ObjectGuid = new(LdapAttributeNames.ObjectGuid);
    public static readonly LdapSidAttribute ObjectSid = new(LdapAttributeNames.ObjectSid);
    public static readonly LdapSimpleAttribute<string> UserPrincipalName = new(LdapAttributeNames.UserPrincipalName);
    public static readonly LdapSimpleAttribute<string> SamAccountName = new(LdapAttributeNames.SamAccountName);
    public static readonly LdapArrayAttribute<string> MemberOf = new(LdapAttributeNames.MemberOf);
    public static readonly LdapArrayAttribute<string> Member = new(LdapAttributeNames.Member);
    public static readonly LdapArrayAttribute<string> ObjectClass = new(LdapAttributeNames.ObjectClass);
}
