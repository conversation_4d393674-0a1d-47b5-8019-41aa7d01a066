using System.DirectoryServices.Protocols;

namespace Platform.AWP.Authentication.Providers.Ldap.LdapAttributes;

internal abstract class LdapAttributeBase
{
    protected LdapAttributeBase(string attributeName)
    {
        AttributeName = attributeName ?? throw new ArgumentNullException(nameof(attributeName));
    }

    public string AttributeName { get; }


    protected bool TryGetValues<TValue>(SearchResultAttributeCollection attributeCollection, out TValue[] result)
    {
        var attr = attributeCollection[AttributeName];
        if (attr == null)
        {
            result = null;
            return false;
        }

        result = attr.GetValues(typeof(TValue)).Cast<TValue>().ToArray();
        return true;
    }

    protected bool TryGetValue<TValue>(SearchResultAttributeCollection attributeCollection, out TValue result)
    {
        if (!TryGetValues<TValue>(attributeCollection, out var values))
        {
            result = default;
            return false;
        }

        result = values[0];
        return true;
    }
}
