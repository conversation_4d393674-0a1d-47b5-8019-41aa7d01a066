using System.DirectoryServices.Protocols;

namespace Platform.AWP.Authentication.Providers.Ldap.LdapAttributes;

internal class LdapArrayAttribute<TValue> : LdapAttributeBase
{
    public LdapArrayAttribute(string attributeName)
        : base(attributeName)
    {
    }

    public bool TryGetValues(SearchResultAttributeCollection attributeCollection, out TValue[] result)
    {
        return TryGetValues<TValue>(attributeCollection, out result);
    }

    public TValue[] GetValues(SearchResultAttributeCollection attributeCollection)
    {
        if (TryGetValues(attributeCollection, out var result)) return result;
        throw new ArgumentException("Collection does not contain specified attribute");
    }
}
