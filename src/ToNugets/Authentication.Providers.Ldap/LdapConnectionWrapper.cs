using System.DirectoryServices.Protocols;
using Microsoft.Extensions.Logging;
using Platform.Logging;
using Platform.Logging.MicrosoftExtensions;
using ILogger = Microsoft.Extensions.Logging.ILogger;

#pragma warning disable CS1591

namespace Platform.AWP.Authentication.Providers.Ldap;

public sealed class LdapConnectionWrapper : IDisposable, IHasAdditionalLogInfo
{
    private readonly SemaphoreSlim _connectionRecreationSemaphore = new(1, 1);
    private readonly string _domain;
    private readonly Func<string, LdapConnection> _ldapConnectionFactoryMethod;
    private readonly ILogger _logger;
    private LdapConnection _ldapConnection;

    internal LdapConnectionWrapper(string domain, Func<string, LdapConnection> ldapConnectionFactoryMethod, ILoggerFactory loggerFactory)
    {
        _domain = domain;
        _ldapConnectionFactoryMethod = ldapConnectionFactoryMethod;
        _logger = loggerFactory?.CreateLogger(GetType());
        AdditionalLogInfo = new AdditionalLogInfo(new LoggingCategory("Ldap"));
        AdditionalLogInfo.AddProperty("domain", domain);
        _ldapConnection = _ldapConnectionFactoryMethod(_domain);
    }

    public void Dispose()
    {
        _ldapConnection?.Dispose();
        _connectionRecreationSemaphore?.Dispose();
    }

    public AdditionalLogInfo AdditionalLogInfo { get; }

    public async Task<T> ExecuteLdapAction<T>(Func<LdapConnection, Task<T>> action)
    {
        var connectionToUse = _ldapConnection;
        try
        {
            return await action(connectionToUse);
        }
        //	TODO: нужно конкретные типы исключений перехватывать
        catch (Exception exc)
        {
            using var mlh = this.CreateMethodLogHelperNoImplicitLogging(_logger).WithNoArgs();

            mlh.LogMethodFailed(exc);
            if (exc is LdapException ldapExc)
            {
                mlh.LogMethodError("xxx: ErrorCode={errorCode}, ServerErrorMessage={serverErrorMessage}", ldapExc.ErrorCode, ldapExc.ServerErrorMessage);
            }

            mlh.LogMethodDetails("getting next LdapConnection...");
            connectionToUse = await GetNextConnection(connectionToUse);
            mlh.LogMethodDetails("got next LdapConnection...");
            mlh.LogMethodDetails("Executing action on next connection...");
            return await action(connectionToUse);
        }
    }

    private async Task<LdapConnection> GetNextConnection(LdapConnection currentLdapConnection)
    {
        if (currentLdapConnection != _ldapConnection) return _ldapConnection;

        await _connectionRecreationSemaphore.WaitAsync();
        try
        {
            if (currentLdapConnection != _ldapConnection) return _ldapConnection;

            _ldapConnection?.Dispose();
            return _ldapConnection = _ldapConnectionFactoryMethod(_domain);
        }
        finally
        {
            _connectionRecreationSemaphore.Release();
        }
    }
}
