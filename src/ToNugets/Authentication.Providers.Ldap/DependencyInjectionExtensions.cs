using System.DirectoryServices.Protocols;
using System.Net;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Platform.AWP.Authentication.Providers.Ldap.Configuration;
using Platform.Identities;
using Platform.Identities.Providers.Keycloak;
using Platform.Keycloak.Clients;
using Platform.WebApi.WebProxies;
using Platform.WebApi.WebProxies.AuthTokenProviders.Oidc;
using Platform.WebApps.Auth.Negotiate;

namespace Platform.AWP.Authentication.Providers.Ldap;

public static class DependencyInjectionExtensions
{
    public static IServiceCollection AddIdentityAndMembershipProviders(this IServiceCollection services, IConfiguration configuration)
    {
        var idMsConfiguration = IdMsProvidersConfiguration.ReadFromConfig(configuration);

        switch (idMsConfiguration.ProvidersType)
        {
            case ProvidersType.Ldap:
                //	LdapConnections и LdapCacheInMinutes оставлены для обратной совместимости
                var ldapConnectionParameters = configuration.GetSection("LdapConnections").Get<LdapConnectionParametersWithAliases[]>();
                var cacheInMinutes = idMsConfiguration?.CacheInMinuites ?? configuration.GetValue("LdapCacheInMinuites", 5);
                return services.AddIdentityAndMembershipProvidersForLdap(ldapConnectionParameters, cacheInMinutes);
            case ProvidersType.Keycloak:
                var kcAdminParameters = new KeyCloakAdminParameters();
                configuration.Bind("KeyCloakAdminParameters", kcAdminParameters);
                return services.AddIdentityAndMembershipProvidersForKeycloak(kcAdminParameters, idMsConfiguration.CacheInMinuites);
            case ProvidersType.Custom:
            // TODO: Тут регестрируется Unity. С ним нужно будет разобраться потом. Всё это из пакета Platform.Identities.Providers.DI.Unity
            // services.AddSingleton<IIdentityProvider, CustomIdentityProvider>();
            // services.AddSingleton<IGroupMembershipProvider, CustomGroupMembershipProvider>();
            // return services.AddIdentityAndMembershipProvidersFromCustomUnityContainer(configuration);
            default:
                throw new NotSupportedException($"Specified ProvidersType not supported ('{idMsConfiguration.ProvidersType}')");
        }
    }

    private static IServiceCollection AddIdentityAndMembershipProvidersForLdap(this IServiceCollection services, LdapConnectionParametersWithAliases[] ldapConnectionParameters, int ldapCacheInMinutes)
    {
        if (ldapConnectionParameters == null) throw new ArgumentNullException(nameof(ldapConnectionParameters));

        var cacheExpirationTimeout = TimeSpan.FromMinutes(ldapCacheInMinutes);

        var ldapConnectionsProcessor = new LdapConnectionsParametersProcessor(ldapConnectionParameters.Cast<LdapConnectionParameters>().ToArray());

        //var domains = ldapConnectionParameters.Select(x => x.FullDomainName).ToArray();
        var domains = ldapConnectionParameters
            .Select(x => new DomainsProcessor.DomainNameInfo(x.FullDomainName, x.GetAliasesArray()))
            .ToArray();


        services.AddSingleton(sp =>
        {
            return new DomainsProcessor(
                domains,
                domain => ldapConnectionsProcessor.CreateNewLdapConnection(domain, false),
                sp.GetService<ILoggerFactory>()
            );
        });

        services.AddSingleton<IIdentityProvider>(sp => new LdapIdentityProvider(cacheExpirationTimeout, sp.GetRequiredService<DomainsProcessor>(), sp.GetService<ILoggerFactory>()));
        services.AddSingleton<IGroupMembershipProvider>(sp => new LdapGroupMembershipProvider(cacheExpirationTimeout, sp.GetRequiredService<DomainsProcessor>()));

        return services;
    }

    private static IServiceCollection AddIdentityAndMembershipProvidersForKeycloak(this IServiceCollection services, KeyCloakAdminParameters kcAdminParameters, int cacheInMinutes)
    {
        var keycloakServerUrl = kcAdminParameters.KeycloakServerUrl;
        var realm = kcAdminParameters.Realm;

        var realmUrl = $"{keycloakServerUrl}/realms/{realm}";
        var clientId = kcAdminParameters.AdminClientId;
        IAuthTokenProvider authTokenInjector = string.IsNullOrEmpty(kcAdminParameters.AdminClientSecret)
            ? new UsernamePasswordOpenIdTokenProvider(realmUrl, clientId, kcAdminParameters.AdminClientUsername, kcAdminParameters.AdminClientPassword, null)
            : new ClientSecretOpenIdTokenProvider(realmUrl, clientId, kcAdminParameters.AdminClientSecret, null);
        var usersClient = new KeycloakAdminCliUsersClient(keycloakServerUrl, realm, authTokenInjector);
        var groupsClient = new KeycloakAdminCliGroupsClient(keycloakServerUrl, realm, authTokenInjector);

        var cacheExpirationTimeout = TimeSpan.FromMinutes(cacheInMinutes);
        services.AddSingleton<IIdentityProvider>(new KeycloakIdentityProvider(usersClient, groupsClient, cacheExpirationTimeout));
        services.AddSingleton<IGroupMembershipProvider>(new KeycloakGroupMembershipProvider(groupsClient, usersClient, cacheExpirationTimeout));

        return services;
    }

    private class LdapConnectionsParametersProcessor
    {
        private readonly LdapConnectionParameters[] _ldapConnectionsParameters;

        public LdapConnectionsParametersProcessor(LdapConnectionParameters[] ldapConnectionsParameters)
        {
            _ldapConnectionsParameters = ldapConnectionsParameters ?? throw new ArgumentNullException(nameof(ldapConnectionsParameters));

            var firstOrDefaultLdapPar = ldapConnectionsParameters.FirstOrDefault();
            if (firstOrDefaultLdapPar == null)
            {
                throw new ArgumentException("LDAP connection parameters must be not empty", nameof(ldapConnectionsParameters));
            }
        }

        public LdapConnection CreateNewLdapConnection(string domain, bool autoBind)
        {
            var ldapConnectionParameters = _ldapConnectionsParameters.FirstOrDefault(x => string.Equals(x.FullDomainName, domain, StringComparison.InvariantCultureIgnoreCase));
            if (ldapConnectionParameters == null)
            {
                throw new ArgumentException($"No LDAP connection parameters registered for specified domain='{domain}'", nameof(domain));
            }

            var ldapDirIdentifier = ldapConnectionParameters.Port > 0
                ? new LdapDirectoryIdentifier(ldapConnectionParameters.Server, ldapConnectionParameters.Port)
                : new LdapDirectoryIdentifier(ldapConnectionParameters.Server);

            LdapConnection result;
            if (string.IsNullOrEmpty(ldapConnectionParameters.Login))
            {
                result = new LdapConnection(ldapDirIdentifier);
            }
            else
            {
                var credential = new NetworkCredential(ldapConnectionParameters.Login, ldapConnectionParameters.Password);
                result = new LdapConnection(ldapDirIdentifier, credential, AuthType.Basic);
            }

            result.AutoBind = autoBind;
            result.SessionOptions.ProtocolVersion = 3;
            return result;
        }
    }
}
