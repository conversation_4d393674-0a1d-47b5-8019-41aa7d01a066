<Project>
  <PropertyGroup>
    <ManagePackageVersionsCentrally>true</ManagePackageVersionsCentrally>
    <NoWarn>NU1507</NoWarn>
  </PropertyGroup>
  <ItemGroup>
    <!-- Microsoft Packages -->
    <PackageVersion Include="Microsoft.EntityFrameworkCore.Design" Version="9.0.7">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageVersion>
    <PackageVersion Include="Microsoft.EntityFrameworkCore.Proxies" Version="9.0.7" />
    <PackageVersion Include="Microsoft.EntityFrameworkCore.Tools" Version="9.0.7">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageVersion>
    <PackageVersion Include="Microsoft.Extensions.Caching.Hybrid" Version="9.7.0" />
    <PackageVersion Include="Microsoft.Extensions.Caching.StackExchangeRedis" Version="9.0.7" />
    <PackageVersion Include="Microsoft.Extensions.Configuration.Json" Version="9.0.6" />
    <PackageVersion Include="Microsoft.Extensions.Hosting.Systemd" Version="9.0.7" />
    <PackageVersion Include="Microsoft.Extensions.Http" Version="9.0.1" />
    <PackageVersion Include="Microsoft.Extensions.Logging" Version="9.0.7" />
    <PackageVersion Include="Microsoft.Extensions.Options" Version="9.0.6" />
    <PackageVersion Include="Microsoft.Extensions.Options.ConfigurationExtensions" Version="9.0.6" />
    <PackageVersion Include="Microsoft.FeatureManagement" Version="4.2.1" />
    <PackageVersion Include="Microsoft.OpenApi" Version="1.6.24" />
    <PackageVersion Include="Npgsql.EntityFrameworkCore.PostgreSQL" Version="9.0.4" />
    <PackageVersion Include="Platform.WebApi.WebProxies.AuthTokenProviders.Oidc" Version="6.0.7" />
    <!-- Swagger/OpenAPI -->
    <PackageVersion Include="Swashbuckle.AspNetCore" Version="9.0.3" />
    <PackageVersion Include="Swashbuckle.AspNetCore.Annotations" Version="9.0.3" />
    <!-- Platform Packages -->
    <PackageVersion Include="Platform.Common" Version="6.0.1" />
    <PackageVersion Include="Platform.WebApps.Swagger" Version="4.0.1" />
    <PackageVersion Include="Platform.WebApps.Common" Version="7.1.2" />
    <PackageVersion Include="Platform.TextJsonConverters" Version="3.0.1" />
    <PackageVersion Include="Platform.Logging.MicrosoftExtensions" Version="4.0.1" />
    <PackageVersion Include="Platform.AWP.Core.DataContracts" Version="10.1.5" />
    <PackageVersion Include="Platform.Identities.Common" Version="1.0.0" />
    <PackageVersion Include="Platform.WebApps.Auth.KnownConfigurations" Version="4.2.0" />
    <!-- Product Packages -->
    <PackageVersion Include="Product.Contracts" Version="8.1.32" />
    <PackageVersion Include="Product.Sdk.Core" Version="3.0.2" />
    <!-- Other Packages -->
  </ItemGroup>
</Project>
