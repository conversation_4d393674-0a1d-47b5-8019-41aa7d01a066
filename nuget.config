<?xml version="1.0" encoding="utf-8"?>
<configuration>
    <packageSources>
        <!-- remove any machine-wide sources with <clear/> -->
        <clear/>
        <!-- Defaul Nuget repository 
        <add key="nuget.org" value="https://api.nuget.org/v3/index.json" />
        -->
        <add key="nuget-proxy"
             value="https://nexus.avelana.ru/repository/nuget.org-proxy/index.json"/>
        <add key="avelana"
             value="https://gitlab.avelana.ru/api/v4/groups/1057/-/packages/nuget/index.json"/>
    </packageSources>
    <activePackageSource>
        <add key="All"
             value="(Aggregate source)"/>
    </activePackageSource>
    <packageSourceCredentials>
        <!-- Credentials for Nexus (nexus.avelana.ru) -->
        <nuget-proxy>
            <add key="Username"
                 value="%BASE_REGISTRY_USER%"/>
            <add key="ClearTextPassword"
                 value="%BASE_REGISTRY_PASSWORD%"/>
        </nuget-proxy>
        <!-- Credentials for GitLab (wh-omni.gitlab.yandexcloud.net) -->
        <avelana>
            <add key="Username"
                 value="%GITLAB_PACKAGE_REGISTRY_USERNAME%"/>
            <add key="ClearTextPassword"
                 value="%GITLAB_PACKAGE_REGISTRY_PASSWORD%"/>
        </avelana>
    </packageSourceCredentials>
</configuration>
